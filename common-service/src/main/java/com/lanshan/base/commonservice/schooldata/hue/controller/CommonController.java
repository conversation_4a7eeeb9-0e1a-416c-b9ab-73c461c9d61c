package com.lanshan.base.commonservice.schooldata.hue.controller;

import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.schooldata.hue.service.TgxcwzxrynzjxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/hue/common")
@Api(tags = "二师控制层", hidden = true)
public class CommonController {
    /**
     * 服务对象
     */
    @Resource
    private TgxcwzxrynzjxService tgxcwzxrynzjxService;

    /**
     * 查询年终绩效
     *
     * @return 所有数据
     */
    @ApiOperation("查询年终绩效")
    @GetMapping("/queryNzjx")
    public Result<Object> queryNzjx(@RequestParam(name = "year") String year) {
        return Result.build(tgxcwzxrynzjxService.queryNzjx(year));
    }
}

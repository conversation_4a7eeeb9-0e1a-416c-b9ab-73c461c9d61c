package com.lanshan.base.commonservice.welcomenewstudent.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.enums.MsgTypeEnum;
import com.lanshan.base.api.enums.UserStatusEnum;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.api.vo.user.UserInfoVo;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.group.constant.GroupConstant;
import com.lanshan.base.commonservice.group.entity.GroupChatScope;
import com.lanshan.base.commonservice.group.qo.AddOrRemoveMemberQo;
import com.lanshan.base.commonservice.group.service.GroupChatService;
import com.lanshan.base.commonservice.system.service.ISysConfigService;
import com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentDataDao;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import com.lanshan.base.commonservice.welcomenewstudent.enums.NewStuGroupTypeEnum;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentExportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentImportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.excel.listener.UserNewStuListener;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentGroupService;
import com.lanshan.base.commonservice.welcomenewstudent.util.WxCpServiceUtil;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentStatVO;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpChatService;
import me.chanjar.weixin.cp.api.WxCpMessageService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpUser;
import me.chanjar.weixin.cp.bean.article.NewArticle;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 新生信息表(NewStudentData)表服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("newStudentDataService")
public class NewStudentDataServiceImpl extends ServiceImpl<NewStudentDataDao, NewStudentData> implements NewStudentDataService {

    @Resource
    private RedisService redisService;

    @Resource
    private NewStudentGroupService newStudentGroupService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private WxCpServiceUtil wxCpServiceUtil;

    @Resource
    private UserService userService;

    @Resource
    private GroupChatService groupChatService;

    @Resource(name = "newStudentTaskExecutor")
    private Executor newStudentTaskExecutor;

    @Resource(name = "wxApiTaskExecutor")
    private Executor wxApiTaskExecutor;

    @Override
    public List<NewStudentStatVO> getNewStudentStat(String year) {
        return this.baseMapper.getNewStudentStat(year);
    }

    @Override
    public Boolean importData(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请选择要上传的Excel文件");
        }
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !(originalFilename.endsWith(".xls") || originalFilename.endsWith(".xlsx"))) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请上传Excel格式的文件(.xls或.xlsx)");
        }
        if (file.getSize() > 100 * 1024 * 1024) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导入文件不能大于100M");
        }
        final String userId = SecurityContextHolder.getUserId();
        final String opUserName = SecurityContextHolder.getUserName();
        String groupType = sysConfigService.selectConfigByKey("welcome.new.stu.groupType");
        try (InputStream is = file.getInputStream()) {
            EasyExcel.read(is)
                    .head(NewStudentImportDTO.class)
                    .sheet()
                    .registerReadListener(new UserNewStuListener(redisService, this, newStudentGroupService, groupType))
                    .doRead();
            return !redisService.hasKey(CommonServiceRedisKeys.USER_INFO_NEW_STUDENT_IMPORT_ERROR_LIST);
        } catch (IOException e) {
            log.error("导入新生数据异常", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导入新生数据异常");
        }
    }

    @Override
    public List<NewStudentExportDTO> getImportNewStudentErrorData() {
        if (!redisService.hasKey(CommonServiceRedisKeys.USER_INFO_NEW_STUDENT_IMPORT_ERROR_LIST)) {
            return Collections.emptyList();
        }
        return redisService.getCacheList(CommonServiceRedisKeys.USER_INFO_NEW_STUDENT_IMPORT_ERROR_LIST);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendWelcomeMsg() {
        //查询所有已开通企微的新生，且未发送首次任务通知的
        List<NewStudentData> newStudentDataList = this.list(Wrappers.lambdaQuery(NewStudentData.class)
                .eq(NewStudentData::getOpenStatus, true)
                .eq(NewStudentData::getStartSendStatus, false)
                .eq(NewStudentData::getYear, String.valueOf(DateUtil.thisYear()))
        );
        if (CollUtil.isEmpty(newStudentDataList)) {
            return;
        }
        Set<String> canSendUserIdSet = new HashSet<>();
        for (NewStudentData newStudentData : newStudentDataList) {
            try {
                WxCpUser wxCpUser = wxCpServiceUtil.getDefaultWxCpService().getUserService().getById(newStudentData.getUserid());
                Integer enable = wxCpUser.getEnable();
                if (enable == UserStatusEnum.ACTIVATED.getCode()) {
                    canSendUserIdSet.add(newStudentData.getUserid());
                }
            } catch (WxErrorException e) {
                log.error("查询用户信息异常", e);
            }
        }
        //更新企微标准表的数据
        userService.update(Wrappers.lambdaUpdate(CpUser.class)
                .set(CpUser::getStatus, UserStatusEnum.ACTIVATED.getCode())
                .in(CpUser::getUserid, canSendUserIdSet));
        userService.refreshCache();

        String agentId = sysConfigService.selectConfigByKey("welcome.new.stu.agentId");
        WxCpService wxCpService = wxCpServiceUtil.getWelcomeWxCpService();

        //使用专用线程池异步执行拉群操作，避免阻塞主线程
        CompletableFuture<Void> joinGroupFuture = CompletableFuture.runAsync(() -> {
            try {
                addUserJoinGroup(newStudentDataList, canSendUserIdSet, wxCpService, agentId);
            } catch (Exception e) {
                log.error("异步拉群操作失败", e);
            }
        }, newStudentTaskExecutor);

        String welcomeConfig = sysConfigService.selectConfigByKey("welcome.new.stu.start.message");
        if (StrUtil.isEmpty(welcomeConfig) || !JSONUtil.isTypeJSON(welcomeConfig)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请配置欢迎消息配置");
        }
        JSONObject weConfigObj = JSON.parseObject(welcomeConfig);
        WxCpMessageService messageService = wxCpService.getMessageService();
        WxCpMessage message = getFirstMessage(weConfigObj, agentId, canSendUserIdSet);
        if (Objects.isNull(message)) {
            return;
        }
        //发送欢迎消息
        try {
            messageService.send(message);
        } catch (WxErrorException e) {
            log.error("发送欢迎语异常", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("发送欢迎语异常");
        }

        //更新已发送状态 - 优化：使用userid而不是整个对象列表
        List<String> userIdList = newStudentDataList.stream()
                .map(NewStudentData::getUserid)
                .filter(canSendUserIdSet::contains)
                .collect(Collectors.toList());

        this.update(Wrappers.lambdaUpdate(NewStudentData.class)
                .set(NewStudentData::getStartSendStatus, true)
                .eq(NewStudentData::getYear, String.valueOf(DateUtil.thisYear()))
                .in(NewStudentData::getUserid, userIdList));
    }

    @Override
    public void joinUserToWelcomeGroup() {
        //查询所有已开通企微的新生，且未发送首次任务通知的
        List<NewStudentData> newStudentDataList = this.list(Wrappers.lambdaQuery(NewStudentData.class)
                .eq(NewStudentData::getOpenStatus, true)
                .eq(NewStudentData::getStartSendStatus, true)
                .eq(NewStudentData::getJoinInGroupStatus, false)
        );
        if (CollUtil.isEmpty(newStudentDataList)) {
            return;
        }

        List<String> userIdList = newStudentDataList.stream()
                .map(NewStudentData::getUserid)
                .collect(Collectors.toList());

        List<UserInfoVo> userInfoVos = userService.listUsersByUseridList(userIdList);
        if (CollUtil.isEmpty(userInfoVos)) {
            return;
        }

        Set<String> canSendUserIdSet = userInfoVos.stream()
                .filter(userInfoVo -> UserStatusEnum.ACTIVATED.getCode() == userInfoVo.getStatus())
                .map(UserInfoVo::getUserid)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(canSendUserIdSet)) {
            log.warn("没有可加入群聊的用户");
            return;
        }

        WxCpService wxCpService = wxCpServiceUtil.getWelcomeWxCpService();
        String agentId = sysConfigService.selectConfigByKey("welcome.new.stu.agentId");

        //异步执行拉群操作，提高响应速度
        CompletableFuture.runAsync(() -> {
            try {
                addUserJoinGroup(newStudentDataList, canSendUserIdSet, wxCpService, agentId);
            } catch (Exception e) {
                log.error("异步拉群操作失败", e);
            }
        }, newStudentTaskExecutor);
    }

    @Override
    public void exportErrorData(HttpServletResponse response) {
        List<NewStudentExportDTO> importNewStudentErrorData = getImportNewStudentErrorData();
        if (CollUtil.isEmpty(importNewStudentErrorData)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("没有错误数据");
        }
        try {
            response.setContentType("application/octet-stream;charset=UTF-8");
            EasyExcel.write(response.getOutputStream(), NewStudentExportDTO.class)
                    .sheet("新生数据导入错误数据")
                    .doWrite(importNewStudentErrorData);
        } catch (IOException e) {
            log.error("导出新生错误数据异常", e);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导出新生错误数据异常");
        }
    }

    /**
     * 获取首次欢迎消息
     *
     * @param weConfigObj      消息配置对象
     * @param agentId          发送消息的 agentId
     * @param canSendUserIdSet 可发送的企微用户id集合
     * @return WxCpMessage
     */
    private WxCpMessage getFirstMessage(JSONObject weConfigObj, String agentId, Set<String> canSendUserIdSet) {
        String msgType = weConfigObj.getString("msgType");
        String title = weConfigObj.getString("title");
        String content = weConfigObj.getString("content");
        String imgUrl = weConfigObj.getString("imgUrl");
        String url = weConfigObj.getString("url");
        if (MsgTypeEnum.NEWS.getValue().equals(msgType)) {
            NewArticle newArticle = new NewArticle();
            newArticle.setTitle(title);
            newArticle.setDescription(content);
            newArticle.setPicUrl((imgUrl));
            newArticle.setUrl(url);
            return WxCpMessage.NEWS()
                    .agentId(Integer.valueOf(agentId))
                    .toUser(String.join("|", canSendUserIdSet))
                    .addArticle(newArticle)
                    .build();
        }
        if (MsgTypeEnum.TEXT.getValue().equals(msgType)) {
            return WxCpMessage.TEXT()
                    .agentId(Integer.valueOf(agentId))
                    .toUser(String.join("|", canSendUserIdSet))
                    .content(content)
                    .build();
        }
        return null;
    }

    /**
     * 添加用户入群
     *
     * @param newStudentDataList 需要操作的新生数据
     * @param canSendUserIdSet   可发送用户id集合
     * @param wxCpService        企微服务
     * @param agentId           建群的 agentId
     */
    private void addUserJoinGroup(List<NewStudentData> newStudentDataList, Set<String> canSendUserIdSet, WxCpService wxCpService, String agentId) {
        String groupType = sysConfigService.selectConfigByKey("welcome.new.stu.groupType");
        Map<String, List<NewStudentData>> needAddNewStuGroupMap = newStudentDataList.stream().filter(newStudentData -> canSendUserIdSet.contains(newStudentData.getUserid()))
                .collect(Collectors.groupingBy(item -> {
                    String key = "";
                    switch (NewStuGroupTypeEnum.valueOf(groupType)) {
                        case COLLEGE:
                            key = item.getCollegeCode();
                            break;
                        case MAJOR:
                            key = item.getCollegeCode() + item.getMajorCode();
                            break;
                        case CLASS:
                            key = item.getCollegeCode() + item.getMajorCode() + item.getClassCode();
                            break;
                    }
                    return key;
                }));
        for (Map.Entry<String, List<NewStudentData>> entry : needAddNewStuGroupMap.entrySet()) {
            String key = entry.getKey();
            NewStudentGroup newStudentGroup = newStudentGroupService.getOne(Wrappers.lambdaQuery(NewStudentGroup.class).eq(NewStudentGroup::getGroupCode, key));
            if (Objects.isNull(newStudentGroup)) {
                continue;
            }
            AddOrRemoveMemberQo addOrRemoveMemberQo = new AddOrRemoveMemberQo();
            addOrRemoveMemberQo.setChatId(newStudentGroup.getChatId());
            addOrRemoveMemberQo.setAgentId(agentId);
            addOrRemoveMemberQo.setAddUseridList(entry.getValue().stream().map(newStudentData -> {
                GroupChatScope groupChatScope = new GroupChatScope();
                groupChatScope.setType(GroupConstant.SCOPE_USER);
                groupChatScope.setDataId(newStudentData.getUserid());
                groupChatScope.setDataName(newStudentData.getName());
                return groupChatScope;
            }).collect(Collectors.toList()));
            WxCpChatService chatService = wxCpService.getChatService();
            try {
                groupChatService.addOrRemoveMember(addOrRemoveMemberQo);
                List<String> joinInUser = newStudentGroup.getJoinInUser();
                List<String> userids = entry.getValue().stream().map(NewStudentData::getUserid).collect(Collectors.toList());
                joinInUser.addAll(userids);
                newStudentGroup.setJoinInUser(joinInUser);
                newStudentGroupService.updateById(newStudentGroup);

                Set<String> userIdSet = newStudentDataList.stream().map(NewStudentData::getUserid)
                        .filter(canSendUserIdSet::contains)
                        .collect(Collectors.toSet());

                //更新已加入群聊状态
                this.update(Wrappers.lambdaUpdate(NewStudentData.class)
                        .set(NewStudentData::getJoinInGroupStatus, true)
                        .eq(NewStudentData::getYear, String.valueOf(DateUtil.thisYear()))
                        .in(NewStudentData::getUserid, userIdSet));
            } catch (Exception e) {
                log.error("更新群聊成员异常：", e);
            }
        }
    }
}


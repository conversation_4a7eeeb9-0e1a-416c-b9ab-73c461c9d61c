package com.lanshan.base.commonservice.welcomenewstudent.converter;


import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentGroupImportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentGroupVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 新生群表(NewStudentGroup)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface NewStudentGroupConverter {

    NewStudentGroupConverter INSTANCE = Mappers.getMapper(NewStudentGroupConverter.class);

    NewStudentGroupVO toVO(NewStudentGroup entity);

    NewStudentGroup toEntity(NewStudentGroupVO vo);

    List<NewStudentGroupVO> toVO(List<NewStudentGroup> entityList);

    List<NewStudentGroup> toEntity(List<NewStudentGroupVO> voList);

    NewStudentGroupImportDTO toImportDTO(NewStudentGroup group);

    List<NewStudentGroupImportDTO> toImportDTO(List<NewStudentGroup> groupList);

    NewStudentGroup importToEntity(NewStudentGroupImportDTO importDTO);

    List<NewStudentGroup> importToEntity(List<NewStudentGroupImportDTO> importDTOList);
}



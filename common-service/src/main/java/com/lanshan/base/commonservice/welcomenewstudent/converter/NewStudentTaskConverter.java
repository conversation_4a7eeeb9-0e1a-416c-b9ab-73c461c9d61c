package com.lanshan.base.commonservice.welcomenewstudent.converter;


import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTask;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentTaskVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 新生任务表(NewStudentTask)bean转换工具
 *
 * <AUTHOR>
 */
@Mapper
public interface NewStudentTaskConverter {

    NewStudentTaskConverter INSTANCE = Mappers.getMapper(NewStudentTaskConverter.class);

    NewStudentTaskVO toVO(NewStudentTask entity);

    NewStudentTask toEntity(NewStudentTaskVO vo);

    List<NewStudentTaskVO> toVO(List<NewStudentTask> entityList);

    List<NewStudentTask> toEntity(List<NewStudentTaskVO> voList);
}



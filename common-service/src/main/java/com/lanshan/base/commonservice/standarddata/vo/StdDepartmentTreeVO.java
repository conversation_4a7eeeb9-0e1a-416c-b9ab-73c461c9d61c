package com.lanshan.base.commonservice.standarddata.vo;

import cn.hutool.core.collection.CollUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class StdDepartmentTreeVO implements Serializable {
    private static final long serialVersionUID = -7427942425466701453L;

    private Long id;

    private String name;

    private Long parentid;

    private List<StdDepartmentTreeVO> children;

    public static List<StdDepartmentTreeVO> buildTree(List<StdDepartmentTreeVO> flatList) {
        List<StdDepartmentTreeVO> roots = new ArrayList<>();
        if (CollUtil.isEmpty(flatList)) {
            return roots;
        }
        Map<Long, StdDepartmentTreeVO> nodeMap = new HashMap<>(flatList.size());
        for (StdDepartmentTreeVO node : flatList) {
            node.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), node);
        }
        for (StdDepartmentTreeVO node : flatList) {
            Long parentId = node.getParentid() == null ? 0L : node.getParentid();
            StdDepartmentTreeVO parent = nodeMap.get(parentId);
            if (parent == null ) {
                // 作为根节点
                roots.add(node);
            } else {
                parent.getChildren().add(node);
            }
        }
        return roots;
    }

}

package com.lanshan.base.commonservice.schooldata.northwestpolitics.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dto.MasterStatusInfoDTO;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.po.SdSsyjsxjxx;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dao.SdSsyjsxjxxMapper;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.service.SdSsyjsxjxxService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.util.NorthwestPoliticsUtil;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.util.SM4Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 硕士研究生学籍信息表 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-26
 */

@Slf4j
@Service
public class SdSsyjsxjxxServiceImpl extends ServiceImpl<SdSsyjsxjxxMapper, SdSsyjsxjxx> implements SdSsyjsxjxxService {

    @Resource
    private SdSsyjsxjxxMapper sdSsyjsxjxxMapper;

    @Override
    public void syncSsyjsxjxx() {
        List<MasterStatusInfoDTO> records = NorthwestPoliticsUtil.getMasterStatusInfo(null);
        if (CollUtil.isEmpty(records)){
            log.info("硕士研究生学籍信息为空，同步终止");
            return;
        }
        sdSsyjsxjxxMapper.truncate();
        List<SdSsyjsxjxx> recordList = BeanUtil.copyToList(records, SdSsyjsxjxx.class);

        SdSsyjsxjxxServiceImpl proxy = (SdSsyjsxjxxServiceImpl) AopContext.currentProxy();
        proxy.insertBatch(recordList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(List<SdSsyjsxjxx> records) {
        List<List<SdSsyjsxjxx>> batchList = CollUtil.split(records, 2000);
        for (List<SdSsyjsxjxx> batch : batchList) {
            sdSsyjsxjxxMapper.insertBatch(batch);
        }
    }

}

package com.lanshan.base.commonservice.schooldata.hbou.task;

import cn.hutool.core.date.DateUtil;
import com.lanshan.base.commonservice.schooldata.hbou.service.HbouSyncService;
import com.lanshan.base.commonservice.schooldata.hbou.utils.ThreadPoolConfig;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class HbouSyncDataTask {

    private final HbouSyncService hbouSyncService;
    private final MeterRegistry meterRegistry;
    private final ThreadPoolConfig threadPoolConfig;

    @Autowired
    public HbouSyncDataTask(HbouSyncService hbouSyncService,
                            ThreadPoolConfig threadPoolConfig,
                            MeterRegistry meterRegistry) {
        this.hbouSyncService = hbouSyncService;
        this.threadPoolConfig = threadPoolConfig;
        this.meterRegistry = meterRegistry;
    }


    /**
     * 同步数据任务
     * 优化：并行处理三种同步，添加监控指标和全局错误处理
     */
    @XxlJob("hbouSyncData")
    public void syncData() {
        log.info("===== 湖北开放大学数据同步任务开始 =====");

        //开始时间
        long startSeconds = DateUtil.currentSeconds();

        try {
            // 初始化线程池
            Executor syncExecutor = threadPoolConfig.syncExecutor();

            // 创建三个并行任务
//            CompletableFuture<Void> studentFuture = CompletableFuture.runAsync(() -> {
//                Timer.Sample studentTimer = Timer.start(meterRegistry);
//                try {
//                    hbouSyncService.syncStudent();
//
//                    long timeMs = studentTimer.stop(meterRegistry.timer("hbou.sync.student"));
//
//                    log.info("学生数据同步完成，耗时：{}ms", timeMs / 1000000);
//
//                } catch (Exception e) {
//                    log.error("学生数据同步失败", e);
//
//                    meterRegistry.counter("hbou.sync.student.error").increment();
//
//                }
//            }, syncExecutor);

            CompletableFuture<Void> jzgFuture = CompletableFuture.runAsync(() -> {
                Timer.Sample jzgTimer = Timer.start(meterRegistry);
                try {
                    hbouSyncService.syncJzg();

                    long timeMs = jzgTimer.stop(meterRegistry.timer("hbou.sync.jzg"));

                    log.info("教职工数据同步完成，耗时：{}ms", timeMs / 1000000);

                } catch (Exception e) {
                    log.error("教职工数据同步失败", e);

                    meterRegistry.counter("hbou.sync.jzg.error").increment();

                }
            }, syncExecutor);

            CompletableFuture<Void> zzjgFuture = CompletableFuture.runAsync(() -> {
                Timer.Sample zzjgTimer = Timer.start(meterRegistry);
                try {
                    hbouSyncService.syncZzjg();

                    long timeMs = zzjgTimer.stop(meterRegistry.timer("hbou.sync.zzjg"));

                    log.info("组织机构数据同步完成，耗时：{}ms", timeMs / 1000000);

                } catch (Exception e) {
                    log.error("组织机构数据同步失败", e);

                    meterRegistry.counter("hbou.sync.zzjg.error").increment();

                }
            }, syncExecutor);

            CompletableFuture<Void> bzksFuture = CompletableFuture.runAsync(() -> {
                Timer.Sample timer = Timer.start(meterRegistry);
                try {
                    hbouSyncService.syncBzks();

                    long timeMs = timer.stop(meterRegistry.timer("hbou.sync.bzks"));

                    log.info("本专科生数据同步完成，耗时：{}ms", timeMs / 1000000);

                } catch (Exception e) {
                    log.error("本专科生数据同步失败", e);

                    meterRegistry.counter("hbou.sync.bzks.error").increment();

                }
            }, syncExecutor);

            // 等待所有任务完成
            CompletableFuture.allOf( jzgFuture, zzjgFuture,bzksFuture).join();

        } catch (Exception e) {
            log.error("湖北开放大学数据同步任务执行异常", e);

            meterRegistry.counter("hbou.sync.global.error").increment();

        } finally {
            long endSeconds = DateUtil.currentSeconds();
            long duration = endSeconds - startSeconds;

            log.info("===== 湖北开放大学数据同步任务结束，总耗时：{}秒 =====", duration);

            meterRegistry.timer("hbou.sync.total").record(duration, TimeUnit.SECONDS);

        }
    }
}
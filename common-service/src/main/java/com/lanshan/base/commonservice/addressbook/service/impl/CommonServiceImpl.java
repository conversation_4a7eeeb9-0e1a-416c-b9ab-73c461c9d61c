package com.lanshan.base.commonservice.addressbook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.lanshan.base.api.constant.CommonConstant;
import com.lanshan.base.api.dto.common.IdDTO;
import com.lanshan.base.api.dto.common.TagIdDTO;
import com.lanshan.base.api.dto.user.DepartmentDto;
import com.lanshan.base.api.dto.user.TagAddOrRemoveUsersDto;
import com.lanshan.base.api.dto.user.TagDto;
import com.lanshan.base.api.dto.user.UserDto;
import com.lanshan.base.api.enums.ErrcodeEnum;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.enums.UserStatusEnum;
import com.lanshan.base.api.enums.YnEnum;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.feign.user.DeptFeign;
import com.lanshan.base.api.feign.user.TagFeign;
import com.lanshan.base.api.feign.user.UserFeign;
import com.lanshan.base.api.qo.user.*;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.addressbook.converter.CpUserTagRelationConverter;
import com.lanshan.base.commonservice.addressbook.entity.*;
import com.lanshan.base.commonservice.addressbook.mapper.*;
import com.lanshan.base.commonservice.addressbook.service.*;
import com.lanshan.base.commonservice.config.manager.AsyncManager;
import com.lanshan.base.commonservice.config.manager.factory.AsyncFactory;
import com.lanshan.base.commonservice.config.properties.AgentProperties;
import com.lanshan.base.commonservice.config.properties.DoubleCheckProperties;
import com.lanshan.base.commonservice.constant.AddressbookConstant;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.enums.OperateStatusEnum;
import com.lanshan.base.commonservice.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-20
 */

@Slf4j
@Service
public class CommonServiceImpl implements CommonService {

    @Resource
    private AgentProperties agentProperties;

    @Resource
    private DoubleCheckProperties doubleCheckProperties;

    @Resource
    private DepartmentOperateService departmentOperateService;

    @Resource
    private UserOperateService userOperateService;

    @Resource
    private TagOperateService tagOperateService;

    @Resource
    private DepartmentService departmentService;

    @Resource
    private UserService userService;

    @Resource
    private TagService tagService;

    @Resource
    private UserDepartmentRelationService userDepartmentRelationService;

    @Resource
    private UserDepartmentRelationBakService userDepartmentRelationBakService;

    @Resource
    private DepartmentBakService departmentBakService;

    @Resource
    private UserBakService userBakService;

    @Resource
    private TagBakService tagBakService;

    @Resource
    private UserTagRelationBakService userTagRelationBakService;

    @Resource
    private DepartmentTagRelationBakService departmentTagRelationBakService;

    @Resource
    private DeptFeign deptFeign;

    @Resource
    private UserFeign userFeign;

    @Resource
    private TagFeign tagFeign;

    @Resource
    private CpDepartmentBakMapper departmentBakMapper;

    @Resource
    private CpUserBakMapper userBakMapper;

    @Resource
    private CpTagBakMapper tagBakMapper;

    @Resource
    private CpUserTagRelationBakMapper userTagRelationBakMapper;

    @Resource
    private CpDepartmentTagRelationBakMapper departmentTagRelationBakMapper;

    @Resource
    private CpDepartmentMapper departmentMapper;

    @Resource
    private CpUserMapper userMapper;

    @Resource
    private CpUserDepartmentRelationMapper userDepartmentRelationMapper;

    @Resource
    private CpUserDepartmentRelationBakMapper userDepartmentRelationBakMapper;

    @Resource
    private DepartmentTagRelationService departmentTagRelationService;

    @Resource
    private CpDepartmentTagRelationMapper departmentTagRelationMapper;

    @Resource
    private CpTagMapper tagMapper;

    @Resource
    private CpUserTagRelationMapper userTagRelationMapper;

    @Resource
    private UserTagRelationOperateService userTagRelationOperateService;

    @Resource
    private DepartmentTagRelationOperateService departmentTagRelationOperateService;

    @Resource
    private UserTagRelationService userTagRelationService;

    @Resource
    private UserDepartmentRelationOperateService userDepartmentRelationOperateService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private CpUserSensitiveInfoService cpUserSensitiveInfoService;

    @Resource
    private CpUserSensitiveInfoMapper cpUserSensitiveInfoMapper;

    @Resource
    private CpUserOperateBindMobileService cpUserOperateBindMobileService;

    @Resource
    private RedisService redisService;

    @Resource
    private CpUserPhoneService cpUserPhoneService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource(name = "commonTaskExecutor")
    private ThreadPoolTaskExecutor commonExecutor;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 通讯录操作表定时任务
     */
    @Override
    public void addressbookOperate() {
        StopWatch stopWatch = new StopWatch();
        RLock lock = redissonClient.getLock(CommonServiceRedisKeys.COMMON_SERVICE_ADDRESS_OPERATE);
        try {
            // 获取锁
            boolean lockResult = lock.tryLock(AddressbookConstant.GET_LOCK_OVERTIME, TimeUnit.SECONDS);
            if (!lockResult) {
                log.error("获取通讯录操作锁失败");
                return;
            }

            String corpId = agentProperties.getCorpId();
            String agentId = agentProperties.getCommonAgentId();
            // 用来存储所有 CompletableFuture
            stopWatch.start("并行处理通讯录操作");
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            futures.add(CompletableFuture.runAsync(() -> {
                executeWithTransaction("处理部门", () -> departmentOperate(corpId, agentId));
            }, commonExecutor));

            futures.add(CompletableFuture.runAsync(() -> {
                executeWithTransaction("处理用户", () -> userOperate(corpId, null, false));
            }, commonExecutor));

            futures.add(CompletableFuture.runAsync(() -> {
                executeWithTransaction("处理用户与部门关联", () -> userDeptRelationOperate(corpId));
            }, commonExecutor));

            futures.add(CompletableFuture.runAsync(() -> {
                executeWithTransaction("处理标签", () -> tagOperate(corpId));
            }, commonExecutor));

            futures.add(CompletableFuture.runAsync(() -> {
                executeWithTransaction("处理标签关联", () -> tagRelationOperate(corpId));
            }, commonExecutor));
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.get();
            stopWatch.stop();
            log.info("处理操作表结束");

        } catch (InterruptedException e) {
            log.error("操作表线程异常中止：", e);
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error(" 任务执行失败", e.getCause());
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    public void executeWithTransaction(String operationName, Runnable operation) {
        log.debug("开始{}操作", operationName);
        try {
            transactionTemplate.execute(status -> {
                operation.run();
                return null;
            });
        } finally {
            log.debug("完成{}操作", operationName);
        }
    }

    /**
     * 处理用户与部门的关联
     */
    private void userDeptRelationOperate(String corpId) {
        // 获取用户部门未处理的数据
        LambdaQueryWrapper<CpUserDepartmentRelationOperate> userTagQueryWrapper = Wrappers
                .lambdaQuery(CpUserDepartmentRelationOperate.class);
        userTagQueryWrapper.eq(CpUserDepartmentRelationOperate::getOperateStatus,
                OperateStatusEnum.UNTREATED.getCode());
        List<CpUserDepartmentRelationOperate> opList = userDepartmentRelationOperateService.list(userTagQueryWrapper);

        // 如果为空，则不处理
        if (CollUtil.isEmpty(opList)) {
            return;
        }

        // 用户与部门的关联map
        Map<String, List<CpUserDepartmentRelationOperate>> userDeptMap = opList.stream()
                .collect(Collectors.groupingBy(CpUserDepartmentRelationOperate::getUserid));
        // 需要更新的用户列表
        List<UserSaveQo> updateUserList = new ArrayList<>();

        // 查询用户
        List<CpUser> userList;
        List<String> userIdList = new ArrayList<>(userDeptMap.keySet());
        // 如果数量小于1000，则按条件查询
        if (userIdList.size() < AddressbookConstant.IN_QUERY_SIZE) {
            userList = userService.listByIds(userIdList);
        } else {
            userList = userService.list();
        }
        Map<String, CpUser> userMap = userList.stream()
                .collect(Collectors.toMap(CpUser::getUserid, Function.identity()));

        // 处理需要更新的用户
        processUpdateUser(userDeptMap, updateUserList, userMap);

        // 同步用户部门至企微
        syncUserDeptRelation(corpId, updateUserList, userDeptMap);

        // 操作成功的插入标准表
        List<CpUserDepartmentRelationOperate> successList = opList.stream()
                .filter(item -> OperateStatusEnum.SUCCESS.getCode() == item.getOperateStatus())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(successList)) {
            // 处理标准表
            processUserDeptRelationStandard(successList);
        }

        // 处理操作表
        processUserDeptRelationOperate(opList);

        long failCount = opList.stream().filter(item -> OperateStatusEnum.FAIL.getCode() == item.getOperateStatus())
                .count();
        log.info("处理用户部门关系成功{}条，失败{}条", successList.size(), failCount);
    }

    /**
     * 处理需要更新的用户
     */
    private void processUpdateUser(Map<String, List<CpUserDepartmentRelationOperate>> userDeptMap,
                                   List<UserSaveQo> updateUserList, Map<String, CpUser> userMap) {

        for (Map.Entry<String, List<CpUserDepartmentRelationOperate>> entry : userDeptMap.entrySet()) {
            String userId = entry.getKey();
            List<CpUserDepartmentRelationOperate> operateList = entry.getValue();
            // 新的部门id列表
            List<Long> newDeptIdList = operateList.stream()
                    .map(CpUserDepartmentRelationOperate::getDepartmentid).collect(Collectors.toList());

            CpUser user = userMap.get(userId);
            // 如果企微用户不存在，则不处理
            if (user == null) {
                operateList.forEach(item -> {
                    item.setOperateStatus(OperateStatusEnum.FAIL.getCode());
                    item.setErrorMsg("企微用户不存在");
                });
                continue;
            }

            // 旧的部门id列表
            List<Long> oldDeptIdList = user.getDepartment();

            // 获取旧部门id中没有的新部门id
            List<Long> addDeptList = new ArrayList<>();
            for (Long newDeptId : newDeptIdList) {
                if (!oldDeptIdList.contains(newDeptId)) {
                    addDeptList.add(newDeptId);
                }
            }
            // 如果不为空则需要更新
            if (CollUtil.isNotEmpty(addDeptList)) {
                UserSaveQo userUpdateQo = new UserSaveQo();
                userUpdateQo.setUserid(userId);

                // 旧部门id添加新部门id
                oldDeptIdList.addAll(addDeptList);
                userUpdateQo.setDepartment(oldDeptIdList);

                // 部门负责人默认设为否
                List<Integer> oldIsLeaderInDept = user.getIsLeaderInDept();
                for (int i = 0; i < addDeptList.size(); i++) {
                    oldIsLeaderInDept.add(0);
                }
                userUpdateQo.setIsLeaderInDept(oldIsLeaderInDept);
                updateUserList.add(userUpdateQo);

                // 如果为空则无需操作，设为成功
            } else {
                operateList.forEach(item -> item.setOperateStatus(OperateStatusEnum.SUCCESS.getCode()));
            }
        }
    }

    /**
     * 处理用户部门关系操作表
     */
    private void processUserDeptRelationOperate(List<CpUserDepartmentRelationOperate> opList) {
        // 不为未处理的都更新
        List<CpUserDepartmentRelationOperate> opUpdateList = opList.stream()
                .filter(item -> OperateStatusEnum.UNTREATED.getCode() != item.getOperateStatus())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(opUpdateList)) {
            userDepartmentRelationOperateService.updateBatchById(opUpdateList);
        }
    }

    /**
     * 处理用户部门关系标准表
     */
    private void processUserDeptRelationStandard(List<CpUserDepartmentRelationOperate> successList) {
        // 删除旧的用户部门关联
        List<String> userIds = successList.stream().map(CpUserDepartmentRelationOperate::getUserid)
                .collect(Collectors.toList());
        LambdaQueryWrapper<CpUserDepartmentRelation> queryWrapper = Wrappers
                .lambdaQuery(CpUserDepartmentRelation.class);
        queryWrapper.in(CpUserDepartmentRelation::getUserid, userIds);
        userDepartmentRelationService.remove(queryWrapper);
        // 新增用户部门关联
        List<CpUserDepartmentRelation> userDeptRelationSaveList = new ArrayList<>();
        for (CpUserDepartmentRelationOperate userOperate : successList) {
            CpUserDepartmentRelation userDeptRelation = new CpUserDepartmentRelation();
            userDeptRelation.setUserid(userOperate.getUserid());
            userDeptRelation.setDepartmentid(userOperate.getDepartmentid());
            // 默认非部门负责人
            userDeptRelation.setIsLeader(YnEnum.NO.getValue());
            userDeptRelationSaveList.add(userDeptRelation);
        }
        userDepartmentRelationService.saveBatch(userDeptRelationSaveList);
    }

    /**
     * 同步用户部门至企微
     */
    private void syncUserDeptRelation(String corpId, List<UserSaveQo> updateUserList,
                                      Map<String, List<CpUserDepartmentRelationOperate>> userDeptMap) {
        if (CollUtil.isNotEmpty(updateUserList)) {

            List<Future<Integer>> futureList = new ArrayList<>();
            // 拆分多线程执行
            List<List<UserSaveQo>> split = ListUtil.splitAvg(updateUserList,
                    AddressbookConstant.PROCESS_THREAD_COUNT_16);
            for (List<UserSaveQo> userList : split) {
                if (CollUtil.isEmpty(userList)) {
                    continue;
                }
                // 异步方式同步用户部门关系至企微
                Future<Integer> future = userService.asyncUserDeptRelationToCp(corpId, userList, userDeptMap);
                futureList.add(future);
            }

            // 校验全部任务执行完
            verifyTaskFinish(futureList);
            log.info("同步用户部门关系至企微完成");
        }
    }

    /**
     * 企微备份
     */
    @Override
    public void cpBak() {
        StopWatch stopWatch = new StopWatch();
        RLock lock = redissonClient.getLock(CommonServiceRedisKeys.COMMON_SERVICE_CP_BAK);
        try {
            // 获取锁
            boolean lockResult = lock.tryLock(AddressbookConstant.GET_LOCK_OVERTIME, TimeUnit.SECONDS);
            if (!lockResult) {
                log.error("获取企微备份锁失败");
                return;
            }

            // 校验是否有备份 如果有则不处理
            if (isBakExist()) {
                log.info("已经存在备份");
                return;
            }

            String corpId = agentProperties.getCorpId();
            String agentId = agentProperties.getCommonAgentId();
            // 用来存储所有 CompletableFuture
            stopWatch.start("并行处理通讯录操作");
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            futures.add(CompletableFuture.runAsync(() -> {
                executeWithTransaction("备份部门", () -> deptBak(corpId, agentId));
            }, commonExecutor));

            futures.add(CompletableFuture.runAsync(() -> {
                // 备份用户
                executeWithTransaction("备份用户", () -> userBak(corpId, agentId));
            }, commonExecutor));

            futures.add(CompletableFuture.runAsync(() -> {
                // 备份标签
                executeWithTransaction("备份标签", () -> tagBak(corpId));
            }, commonExecutor));

            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.get();
            stopWatch.stop();

            log.info("备份结束");

        } catch (InterruptedException e) {
            log.error("操作表线程异常中止：", e);
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error(" 任务执行失败", e.getCause());
        } finally {
            lock.unlock();
        }

        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    /**
     * 清空企微备份
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cpBakRemove() {
        StopWatch stopWatch = new StopWatch();
        RLock lock = redissonClient.getLock(CommonServiceRedisKeys.COMMON_SERVICE_CP_BAK_REMOVE);
        try {
            // 获取锁
            boolean lockResult = lock.tryLock(AddressbookConstant.GET_LOCK_OVERTIME, TimeUnit.SECONDS);
            if (!lockResult) {
                log.error("获取清空企微备份锁失败");
                return;
            }

            // 清空部门备份
            stopWatch.start("清空部门备份");
            departmentBakMapper.truncate();
            stopWatch.stop();

            // 清空用户备份
            stopWatch.start("清空用户备份");
            userBakMapper.truncate();
            userDepartmentRelationBakMapper.truncate();
            stopWatch.stop();

            // 清空标签备份
            stopWatch.start("清空标签备份");
            tagBakMapper.truncate();
            // 清空用户标签关系备份
            userTagRelationBakMapper.truncate();
            // 清空部门标签关系备份
            departmentTagRelationBakMapper.truncate();
            stopWatch.stop();

            log.info("清空备份完成");

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            lock.unlock();
        }
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    /**
     * 检查是否有备份 如果有则清空
     */
    private boolean isBakExist() {
        // 查询是否有备份数据
        return isExistDepartmentBak();
    }

    /**
     * 企微恢复
     */
    @Override
    public void cpRecover() {
        StopWatch stopWatch = new StopWatch();
        RLock lock = redissonClient.getLock(CommonServiceRedisKeys.COMMON_SERVICE_CP_RECOVER);
        try {
            // 获取锁
            boolean lockResult = lock.tryLock(AddressbookConstant.GET_LOCK_OVERTIME, TimeUnit.SECONDS);
            if (!lockResult) {
                log.error("获取企微恢复锁失败");
                return;
            }

            // 查询是否有新组织架构
            CpDepartment newDept = departmentMapper.queryNewDept();
            // 如果为空则不处理
            if (newDept == null) {
                log.info("新组织架构不存在");
                return;
            }

            // 查询是否有备份数据
            boolean existDepartmentBak = isExistDepartmentBak();
            // 不存在则不处理
            if (!existDepartmentBak) {
                log.info("备份数据不存在");
                return;
            }

            String corpId = agentProperties.getCorpId();
            // 用来存储所有 CompletableFuture
            stopWatch.start("并行处理通讯录操作");
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            futures.add(CompletableFuture.runAsync(() -> {
                // 恢复用户 - 独立事务
                executeWithTransaction("恢复用户", () -> userRecover(corpId));
            }, commonExecutor));
            futures.add(CompletableFuture.runAsync(() -> {
                // 恢复部门 - 独立事务
                executeWithTransaction("恢复部门", () -> deptRecover(corpId, newDept.getId()));
            }, commonExecutor));
            futures.add(CompletableFuture.runAsync(() -> {
                // 恢复标签 - 独立事务
                executeWithTransaction("恢复标签", this::tagRecover);
            }, commonExecutor));

            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.get();
            stopWatch.stop();
            log.info("恢复完成");

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error(" 任务执行失败", e.getCause());
        } finally {
            lock.unlock();
        }
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    /**
     * 应用新组织架构 将新组织架构移动到根目录下
     */
    @Transactional
    @Override
    public void applyNewDept() {
        StopWatch stopWatch = new StopWatch();
        RLock lock = redissonClient.getLock(CommonServiceRedisKeys.COMMON_SERVICE_APPLY_NEW_DEPT);
        try {
            // 获取锁
            boolean lockResult = lock.tryLock(AddressbookConstant.GET_LOCK_OVERTIME, TimeUnit.SECONDS);
            if (!lockResult) {
                log.error("获取应用新组织架构锁失败");
                return;
            }

            // 查询是否有新组织架构
            CpDepartment newDept = departmentMapper.queryNewDept();
            // 如果为空则不处理
            if (newDept == null) {
                log.info("新组织架构不存在");
                return;
            }

            String corpId = agentProperties.getCorpId();

            Long newDeptId = newDept.getId();
            // 查询组织架构
            List<CpDepartment> list = departmentService.list();
            // 移除根目录
            list.removeIf(department -> department.getId() == CommonConstant.ROOT_DEPT_ID);
            // 获取新组织架构
            List<CpDepartment> newDeptList = list.stream()
                    .filter(item -> item.getIdPath().contains(newDeptId.toString())).collect(Collectors.toList());
            // 获取新组织架构的id列表
            List<Long> newDeptIdList = newDeptList.stream().map(CpDepartment::getId).collect(Collectors.toList());
            // 获取旧组织架构的id列表
            List<CpDepartment> oldDeptList = list.stream().filter(item -> !newDeptIdList.contains(item.getId()))
                    .collect(Collectors.toList());

            // 用户移除以前的部门关联
            stopWatch.start("用户移除以前的部门关联");
            userRemoveOldDept(newDeptIdList, oldDeptList, corpId, newDeptId);
            stopWatch.stop();

            // 处理新部门 删除旧组织架构 并将新组织架构的二级部门pid设为1 删除新组织架构根部门
            stopWatch.start("处理新部门");
            processNewDept(newDeptList, oldDeptList, newDept, corpId);
            stopWatch.stop();

            // 移除旧的部门标签关联
            stopWatch.start("移除旧的部门标签关联");
            removeOldDeptTagRelation(oldDeptList);
            stopWatch.stop();

            log.info("应用新组织架构完成");

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            lock.unlock();
        }
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    /**
     * 通讯录同步定时任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class, timeout = AddressbookConstant.SYNC_TASK_TIMEOUT) // 设置30分钟超时
    public void addressbookSync() {
        StopWatch stopWatch = new StopWatch();
        RLock lock = redissonClient.getLock(CommonServiceRedisKeys.COMMON_SERVICE_ADDRESS_SYNC);
        try {
            // 获取锁，设置合理的锁超时时间和锁持有时间
            boolean lockResult = lock.tryLock(AddressbookConstant.GET_LOCK_OVERTIME,
                    AddressbookConstant.SYNC_LOCK_LEASE_TIME, TimeUnit.SECONDS);
            if (!lockResult) {
                log.error("获取通讯录同步锁失败");
                return;
            }

            String corpId = agentProperties.getCorpId();
            String agentId = agentProperties.getCommonAgentId();

            // 同步部门
            stopWatch.start("同步部门");
            List<CpDepartment> cpDepartments = deptSync(corpId, agentId);
            stopWatch.stop();

            // 同步用户
            stopWatch.start("同步用户");
            userSync(corpId, agentId, cpDepartments);
            stopWatch.stop();

            // 同步标签
            stopWatch.start("同步标签");
            tagSync(corpId);
            stopWatch.stop();

            // 删除用户缓存
            redisService.deleteObject(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP);

            log.info("同步结束");

        } catch (InterruptedException e) {
            log.error("通讯录同步被中断", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("通讯录同步执行异常", e);
            throw e;
        } finally {
            // 安全释放锁
            try {
                if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                    log.debug("成功释放通讯录同步锁");
                }
            } catch (Exception e) {
                log.error("释放通讯录同步锁失败", e);
            }
        }

        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveUser(UserSaveSimpleQo qo) {
        // 查询是否存在用户
        CpUser user = userService.getById(qo.getUserid());

        // 不存在则新增
        if (user != null) {
            throw ExceptionCodeEnum.USER_EXIST.toServiceException();
        }

        // 如果主部门为空，则默认第一个部门为主部门
        if (qo.getMainDepartment() == null) {
            qo.setMainDepartment(qo.getDepartment().get(0));
        }

        // 同步至企微
        UserSaveQo userSaveQo = JacksonUtils.toObj(JacksonUtils.toJson(qo), UserSaveQo.class);
        Result<?> userCreateResult = userFeign.userCreate(agentProperties.getCorpId(), Boolean.FALSE, userSaveQo);
        if (!userCreateResult.success()) {
            throw new ServiceException(userCreateResult.getMsg(), userCreateResult.getCode());
        }

        // 如果部门id列表不为空，则插入用户-部门关联表
        List<Long> department = qo.getDepartment();
        // 是否为部门领导
        List<Integer> isLeaderInDept = new ArrayList<>();
        if (CollUtil.isNotEmpty(department)) {
            List<CpUserDepartmentRelation> userDepartmentRelationList = new ArrayList<>();
            for (Long departmentId : department) {
                CpUserDepartmentRelation userDepartmentRelation = new CpUserDepartmentRelation();
                userDepartmentRelation.setUserid(qo.getUserid());
                userDepartmentRelation.setDepartmentid(departmentId);
                // 默认不是部门领导
                userDepartmentRelation.setIsLeader(YnEnum.NO.getValue());
                isLeaderInDept.add(YnEnum.NO.getValue());

                userDepartmentRelationList.add(userDepartmentRelation);
            }
            userDepartmentRelationService.saveBatch(userDepartmentRelationList);
        }

        // 插入标准表
        CpUser entity = JacksonUtils.toObj(JacksonUtils.toJson(qo), CpUser.class);
        entity.setIsLeaderInDept(isLeaderInDept);
        // 默认状态为未激活
        entity.setStatus(UserStatusEnum.INACTIVE.getCode());
        userService.save(entity);

        // 设置用户缓存
        redisService.setCacheMapValue(CommonServiceRedisKeys.COMMON_SERVICE_USER_MAP, qo.getUserid(),
                JacksonUtils.toJson(entity));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSaveUser(List<UserSaveSimpleQo> qoList) {
        // 写入操作表
        List<CpUserOperate> userOperateList = JacksonUtils.toObj(JacksonUtils.toJson(qoList), new TypeReference<>() {
        });
        userOperateService.saveBatch(userOperateList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveDepartment(DepartmentSaveQo qo) throws ServiceException {
        // 查询企微是否存在部门
        CpDepartment department = departmentService.getById(qo.getId());

        // 不存在则新增
        if (department != null) {
            throw ExceptionCodeEnum.DEPT_EXIST.toServiceException();
        }

        // 如果部门排序为空，则设为初始部门顺序值
        if (qo.getOrder() == null) {
            qo.setOrder(AddressbookConstant.INIT_ORDER);
        }
        // 同步至企微
        Result<IdDTO> departmentCreateResult = deptFeign.departmentCreate(agentProperties.getCorpId(), qo);
        if (!departmentCreateResult.success()) {
            throw new ServiceException(departmentCreateResult.getMsg(), departmentCreateResult.getCode());
        }

        // 写入标准表
        CpDepartment entity = BeanUtil.copyProperties(qo, CpDepartment.class);
        entity.setId(departmentCreateResult.getResult().getId());
        departmentService.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSaveDepartment(List<DepartmentSaveQo> qoList) {
        // 写入操作表
        List<CpDepartmentOperate> departmentOperateList = BeanUtil.copyToList(qoList, CpDepartmentOperate.class);
        departmentOperateService.saveBatch(departmentOperateList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveTag(TagSaveQo qo) {
        // 查询是否存在
        CpTag tag = tagService.getById(qo.getTagid());
        if (tag != null) {
            throw ExceptionCodeEnum.TAG_EXIST.toServiceException();
        }

        // 创建标签
        Result<TagIdDTO> tagCreateResult = tagFeign.tagCreate(agentProperties.getCorpId(), null, qo);

        if (!tagCreateResult.success()) {
            throw new ServiceException(tagCreateResult.getMsg(), tagCreateResult.getCode());
        }

        // 写入标准表
        CpTag entity = CpTag.builder()
                .tagname(qo.getTagname())
                .tagid(tagCreateResult.getResult().getTagid()).build();
        tagService.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSaveTag(List<TagSaveQo> qoList) {
        // 写入操作表
        List<CpTagOperate> tagOperateList = BeanUtil.copyToList(qoList, CpTagOperate.class);
        tagOperateService.saveBatch(tagOperateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveUserDeptRelation(List<UserDeptRelationQo> list) {
        // 写入操作表
        List<CpUserDepartmentRelationOperate> relationList = BeanUtil.copyToList(list,
                CpUserDepartmentRelationOperate.class);
        userDepartmentRelationOperateService.saveBatch(relationList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveUserTagRelation(List<UserTagRelationQo> list) {
        // 写入操作表
        List<CpUserTagRelationOperate> relationList = BeanUtil.copyToList(list, CpUserTagRelationOperate.class);
        userTagRelationOperateService.saveBatch(relationList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveDeptTagRelation(List<DeptTagRelationQo> list) {
        // 写入操作表
        List<CpDepartmentTagRelationOperate> relationList = BeanUtil.copyToList(list,
                CpDepartmentTagRelationOperate.class);
        departmentTagRelationOperateService.saveBatch(relationList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sensitiveInfoSync() {
        cpUserSensitiveInfoMapper.sensitiveInfoSync();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void useridBindMobile(String userid, String mobile) {
        useridBindMobileCommon(userid, mobile);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void unbindUser(String userid) {
        // 解绑用户
        unbindUserCommon(userid);
    }

    @Override
    public CpUserOperateBindMobile userCanBind(String userid) {
        // 通过userid查询绑定表
        // 如果绑定数据存在，则可以绑定
        return cpUserOperateBindMobileService.getById(userid);
    }

    @Override
    public void useridBindMobileTestCp(String userid, String mobile) {
        String corpId = agentProperties.getCorpId();

        UserSaveQo cpUserParam = UserSaveQo.builder()
                .userid("test")
                .name("test")
                .mobile("20000010001")
                .department(Collections.singletonList(CommonConstant.ROOT_DEPT_ID))
                .mainDepartment(CommonConstant.ROOT_DEPT_ID)
                .build();

        // 新增至企微
        Result<?> userCreateResult = userFeign.userCreate(corpId, true, cpUserParam);
        if (userCreateResult.hasError()) {
            log.error("新增用户失败：{}", userCreateResult.getMsg());
        }

        TagUsersQo tagUsersQo = TagUsersQo.builder()
                .tagId(99999L)
                .userList(Collections.singletonList("test"))
                .build();

        // 增加标签成员
        Result<TagAddOrRemoveUsersDto> tagAddResult = tagFeign.addTagUsers(corpId, null, tagUsersQo);
        if (tagAddResult.hasError()) {
            // 删除企微用户
            userFeign.userDelete(corpId, userid);
            log.error("新增用户标签失败：{}", tagAddResult.getMsg());
            throw new ServiceException("回滚新增用户", tagAddResult.getCode());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addAddressBookInfo(AddAddressBookQo qo) {
        String corpId = agentProperties.getCorpId();

        // 新增用户
        addAddressbookInfoUser(qo, corpId);

        // 新增部门
        addAddressbookInfoDept(qo, corpId);
    }

    /**
     * 新增部门
     */
    private void addAddressbookInfoDept(AddAddressBookQo qo, String corpId) {
        Long departmentid = qo.getDepartmentid();
        if (departmentid != null) {
            // 查询部门标准表是否已经存在
            CpDepartment department = departmentService.getById(departmentid);
            // 如果不存在则新增
            if (department == null) {
                // 查询企微部门
                Result<DepartmentDto> departmentResult = deptFeign.departmentGet(corpId,
                        agentProperties.getCommonAgentId(), departmentid);
                if (departmentResult.hasError()) {
                    log.error("新增通讯录信息-查询企微部门失败：{}", departmentResult.getMsg());
                    return;
                }

                CpDepartment cpDepartment = JacksonUtils.toObj(JacksonUtils.toJson(departmentResult.getResult()),
                        CpDepartment.class);
                // 查询父部门
                CpDepartment parentDepartment = departmentService.getById(cpDepartment.getParentid());
                if (parentDepartment != null) {
                    // 设置path
                    cpDepartment.setIdPath(parentDepartment.getIdPath() + cpDepartment.getId() + StrPool.SLASH);
                    cpDepartment.setPath(parentDepartment.getPath() + cpDepartment.getName() + StrPool.SLASH);
                }
                // 插入部门标准表
                departmentService.save(cpDepartment);
            }
        }
    }

    /**
     * 新增用户
     */
    private void addAddressbookInfoUser(AddAddressBookQo qo, String corpId) {
        String userid = qo.getUserid();
        if (StringUtils.isNotBlank(userid)) {
            // 查询用户标准表是否已经存在
            CpUser user = userService.getById(userid);
            // 如果不存在则新增
            if (user == null) {
                // 查询企微用户
                Result<UserDto> userResult = userFeign.userGet(corpId, agentProperties.getCommonAgentId(), userid);
                if (userResult.hasError()) {
                    log.error("新增通讯录信息-查询企微用户失败：{}", userResult.getMsg());
                    return;
                }

                CpUser cpUser = JacksonUtils.toObj(JacksonUtils.toJson(userResult.getResult()), CpUser.class);

                // 插入用户标准表
                userService.save(cpUser);

                // 插入用户部门关联表
                List<Long> departmentidList = cpUser.getDepartment();
                List<CpUserDepartmentRelation> userDepartmentRelationList = new ArrayList<>();
                for (Long departmentid : departmentidList) {
                    CpUserDepartmentRelation userDepartmentRelation = new CpUserDepartmentRelation();
                    userDepartmentRelation.setUserid(cpUser.getUserid());
                    userDepartmentRelation.setDepartmentid(departmentid);
                    userDepartmentRelationList.add(userDepartmentRelation);
                }
                userDepartmentRelationService.saveBatch(userDepartmentRelationList);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void externalJoin(ExternalJoinQo qo) {
        // 检验验证码
        cpUserPhoneService.checkPhoneCode(qo.getMobile(), qo.getCode());

        // 获取指定外部部门
        Long deptId = Long.valueOf(sysConfigService.selectConfigByKey("external.join.deptId"));

        // 用户新增至企微
        UserSaveQo userSaveQo = new UserSaveQo();
        userSaveQo.setUserid(qo.getMobile());
        userSaveQo.setName(qo.getName());
        userSaveQo.setMobile(qo.getMobile());
        userSaveQo.setDepartment(Collections.singletonList(deptId));
        userSaveQo.setMainDepartment(deptId);
        Result<?> userCreateResult = userFeign.userCreate(agentProperties.getCorpId(), true, userSaveQo);
        if (userCreateResult.hasError()) {
            log.error("新增外部用户失败：name={}, mobile={}, code={}, msg={}", qo.getName(), qo.getMobile(),
                    userCreateResult.getCode(), userCreateResult.getMsg());
            int code = userCreateResult.getCode();
            if (ErrcodeEnum.ERROR_60104.getValue() == code) {
                throw ExceptionCodeEnum.EXTERNAL_USER_ADD_MOBILE_EXIST.toServiceException();
            } else {
                throw ExceptionCodeEnum.EXTERNAL_USER_ADD_FAIL.toServiceException();
            }
        }

        // 二次验证通过 查询不需要关注分布式事务
        Result<Object> userAuthsuccResult = userFeign.userAuthsucc(agentProperties.getCorpId(), qo.getMobile());
        if (userAuthsuccResult.hasError()) {
            log.error("二次验证失败：name={}, mobile={}, code={}, msg={}", qo.getName(), qo.getMobile(),
                    userAuthsuccResult.getCode(), userAuthsuccResult.getMsg());
        }
    }

    @Override
    public void delCache(String key) {
        if (StringUtils.isEmpty(key)) {
            return;
        }

        redisService.deleteObject(key);
    }

    @Override
    public void autoDelTag() {
        // 查询全部待删除标签操作信息
        List<CpUserTagRelationOperate> operateList = userTagRelationOperateService
                .list(Wrappers.lambdaQuery(CpUserTagRelationOperate.class)
                        .eq(CpUserTagRelationOperate::getOperateStatus, OperateStatusEnum.NEED_DELETE.getCode())
                        .isNull(CpUserTagRelationOperate::getErrorMsg));
        if (CollUtil.isEmpty(operateList)) {
            return;
        }
        Map<Long, List<String>> groupTagMap = operateList.stream()
                .collect(Collectors.groupingBy(CpUserTagRelationOperate::getTagid,
                        Collectors.mapping(CpUserTagRelationOperate::getUserid, Collectors.toList())));
        log.info("删除标签开始-待删除标签操作信息：{}", JSON.toJSONString(groupTagMap));
        for (Map.Entry<Long, List<String>> entry : groupTagMap.entrySet()) {
            BatchTagUsersQo batchTagUsersQo = new BatchTagUsersQo();
            batchTagUsersQo.setTagIdList(Collections.singletonList(entry.getKey()));
            List<String> userIdList = entry.getValue();
            List<List<String>> splitList = Lists.partition(userIdList, 999);
            for (List<String> splitUserIdList : splitList) {
                batchTagUsersQo.setUserList(splitUserIdList);
                tagService.batchDelTagUsers(batchTagUsersQo);
            }
        }
        log.info("删除标签结束-所有标签删除成功");
        // 更新操作表状态
        userTagRelationOperateService.update(Wrappers.lambdaUpdate(CpUserTagRelationOperate.class)
                .eq(CpUserTagRelationOperate::getOperateStatus, OperateStatusEnum.NEED_DELETE.getCode())
                .set(CpUserTagRelationOperate::getErrorMsg, OperateStatusEnum.SUCCESS.getCode()));
    }

    @Override
    public void batchDelDeptUser(String deptId) {
        userService
                .batchDelDeptUser(StringUtils.isNotBlank(deptId) && NumberUtil.isNumber(deptId) ? Long.valueOf(deptId)
                        : doubleCheckProperties.getNewEnrollmentStuDept());
    }

    /**
     * userid绑定手机号
     */
    private void useridBindMobileCommon(String userid, String mobile) {
        String corpId = agentProperties.getCorpId();
        String agentId = agentProperties.getCommonAgentId();
        log.info("用户绑定手机号-开始, corpId:{}, agentId:{}, userid:{}", corpId, agentId, userid);
        // 查询企微用户
        Result<UserDto> userResult = userFeign.userGet(corpId, agentId, userid);
        int userResultCode = userResult.getCode();
        // 如果有异常且不为用户不存在，则抛出异常
        if (userResult.hasError() && ErrcodeEnum.ERROR_60111.getValue() != userResultCode) {
            log.error("用户绑定手机号-查询企微用户异常, userid:{}, code:{}, msg:{}", userid, userResultCode, userResult.getMsg());
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.build("用户绑定手机号-查询企微用户失败").toServiceException();
        }

        // 企微用户不存在则新增
        if (ErrcodeEnum.ERROR_60111.getValue() == userResultCode) {
            // 绑定新增用户
            bindSaveUser(userid, mobile, corpId);

        } else {
            // 存在则更新手机号
            ChangeMobileQo changeMobileQo = new ChangeMobileQo();
            changeMobileQo.setUserid(userid);
            changeMobileQo.setMobile(mobile);
            userService.changeMobile(changeMobileQo);
        }
    }

    private void bindSaveUser(String userid, String mobile, String corpId) {
        // 通过userid查询绑定表（绑定表的数据都是数据中心没有手机号的数据）
        CpUserOperateBindMobile bindMobile = cpUserOperateBindMobileService.getById(userid);
        // 如果绑定数据不存在，则抛异常
        if (bindMobile == null) {
            log.error("用户绑定手机号-绑定数据不存在, userid:{}", userid);
            throw ExceptionCodeEnum.USER_BIND_MOBILE_NOT_EXIST.toServiceException();
        }

        // 用户新增至企微
        UserSaveQo userSaveQo = JacksonUtils.toObj(JacksonUtils.toJson(bindMobile), UserSaveQo.class);
        userSaveQo.setMobile(mobile);
        Result<?> userCreateResult = userFeign.userCreate(corpId, true, userSaveQo);
        if (userCreateResult.hasError()) {
            log.error("用户绑定手机号-新增用户失败, userid:{}, mobile:{}, code:{}, msg:{}", userid, mobile,
                    userCreateResult.getCode(), userCreateResult.getMsg());
            throw ExceptionCodeEnum.USER_BIND_ADD_USER_FAIL.toServiceException();
        }

        // 查询待绑定用户标签表
        LambdaQueryWrapper<CpUserTagRelationOperate> userTagRelationOperateQw = Wrappers
                .lambdaQuery(CpUserTagRelationOperate.class);
        userTagRelationOperateQw.eq(CpUserTagRelationOperate::getUserid, userid);
        userTagRelationOperateQw.eq(CpUserTagRelationOperate::getOperateStatus,
                OperateStatusEnum.PENDING_BIND.getCode());
        List<CpUserTagRelationOperate> userTagRelationOperateList = userTagRelationOperateService
                .list(userTagRelationOperateQw);
        if (CollUtil.isNotEmpty(userTagRelationOperateList)) {
            // 用户添加标签
            for (CpUserTagRelationOperate tagRelationOperate : userTagRelationOperateList) {
                TagUsersQo tagUsersQo = TagUsersQo.builder()
                        .tagId(tagRelationOperate.getTagid())
                        .userList(Collections.singletonList(tagRelationOperate.getUserid()))
                        .build();

                Result<TagAddOrRemoveUsersDto> tagAddResult = tagFeign.addTagUsers(corpId, null, tagUsersQo);
                if (tagAddResult.hasError()) {
                    // 删除企微用户
                    userFeign.userDelete(corpId, userid);
                    log.error("用户绑定手机号-新增用户标签失败, userid:{}, tagid:{}, code:{}, msg:{}", userid,
                            tagRelationOperate.getTagid(), tagAddResult.getCode(), tagAddResult.getMsg());
                    throw ExceptionCodeEnum.USER_BIND_USER_ADD_TAG_FAIL.toServiceException();
                }
            }

        }

        log.info("用户绑定手机号-新增用户成功, userid:{}, mobile:{}", userid, mobile);

        // 异步新增到用户、用户部门关联、标签关联标准表
        commonExecutor.execute(() -> asyncStandard(userid, userSaveQo, userTagRelationOperateList));
    }

    /**
     * 异步新增到用户、用户部门关联、标签关联标准表
     */
    private void asyncStandard(String userid, UserSaveQo userSaveQo,
                               List<CpUserTagRelationOperate> userTagRelationOperateList) {
        AsyncManager am = AsyncManager.me();

        // 新增到用户标准表
        CpUser cpUser = JacksonUtils.toObj(JacksonUtils.toJson(userSaveQo), CpUser.class);
        // 用户状态默认未激活
        cpUser.setStatus(UserStatusEnum.INACTIVE.getCode());
        am.execute(AsyncFactory.addUser(Collections.singletonList(cpUser)));

        // 用户部门关系标准表
        CpUserDepartmentRelation userDepartmentRelation = CpUserDepartmentRelation.builder().userid(userid)
                .departmentid(cpUser.getMainDepartment()).build();
        am.execute(AsyncFactory.addUserDepartmentRelation(Collections.singletonList(userDepartmentRelation)));

        // 新增用户标签关系标准表
        if (CollUtil.isNotEmpty(userTagRelationOperateList)) {
            List<CpUserTagRelation> userTagRelationList = CpUserTagRelationConverter.INSTANCE
                    .toEntity(userTagRelationOperateList);
            am.execute(AsyncFactory.addUserTagRelation(userTagRelationList));
        }
    }

    /**
     * 解绑用户
     */
    private void unbindUserCommon(String userid) {
        // 删除企微用户
        Result<Object> result = userFeign.userDelete(agentProperties.getCorpId(), userid);
        if (result.hasError()) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("删除用户失败").toServiceException();
        }

        // 删除相关标准表
        userService.removeById(userid);
        userDepartmentRelationService.remove(
                new LambdaQueryWrapper<CpUserDepartmentRelation>().eq(CpUserDepartmentRelation::getUserid, userid));
        userTagRelationService
                .remove(new LambdaQueryWrapper<CpUserTagRelation>().eq(CpUserTagRelation::getUserid, userid));
    }

    /**
     * 标签同步
     */
    private void tagSync(String corpId) {
        try {
            log.info("开始同步标签数据，corpId: {}", corpId);

            // 查询全部标签 查询无处理
            Result<List<TagDto>> result = tagFeign.tagList(corpId, null);
            // 企微查询异常
            if (result.hasError()) {
                throw new ServiceException("通讯录同步-查询企微标签异常：" + result.getMsg(), result.getCode());
            }

            List<TagDto> cpTagList = result.getResult();
            // 标签为空则不处理
            if (CollUtil.isEmpty(cpTagList)) {
                log.info("企微标签数据为空，跳过同步");
                return;
            }

            log.info("从企微获取到标签数据{}条", cpTagList.size());

            // tagid对应用户id、部门id列表 - 使用线程安全的集合
            Map<Long, List<String>> tagUserMap = new ConcurrentHashMap<>();
            Map<Long, List<Long>> tagDeptMap = new ConcurrentHashMap<>();
            // 查询标签关联
            getTagRelationWithTimeout(corpId, cpTagList, tagUserMap, tagDeptMap);

            List<CpUserTagRelation> userTagRelationList = new ArrayList<>();
            List<CpDepartmentTagRelation> deptTagRelationList = new ArrayList<>();
            for (TagDto wxCpTag : cpTagList) {
                Long tagId = wxCpTag.getTagid();

                // 标签用户关系
                List<String> userIdList = tagUserMap.get(tagId);
                if (CollUtil.isNotEmpty(userIdList)) {
                    for (String userId : userIdList) {
                        CpUserTagRelation userTagRelation = CpUserTagRelation.builder()
                                .tagid(tagId)
                                .userid(userId).build();
                        userTagRelationList.add(userTagRelation);
                    }
                }

                // 标签部门关系
                List<Long> deptIdList = tagDeptMap.get(tagId);
                if (CollUtil.isNotEmpty(deptIdList)) {
                    for (Long deptId : deptIdList) {
                        CpDepartmentTagRelation deptTagRelation = CpDepartmentTagRelation.builder()
                                .tagid(tagId)
                                .departmentid(deptId).build();
                        deptTagRelationList.add(deptTagRelation);
                    }
                }
            }

            // 查询标签
            List<CpTag> tags = tagService.list();
            List<Long> tagIdList = tags.stream().map(CpTag::getTagid).collect(Collectors.toList());
            List<Long> cpTagIdList = cpTagList.stream().map(TagDto::getTagid).collect(Collectors.toList());

            // 需要新增的标签
            List<CpTag> tagSaveList = cpTagList.stream().filter(wxCpTag -> !tagIdList.contains(wxCpTag.getTagid()))
                    .map(item -> CpTag.builder().tagid(item.getTagid()).tagname(item.getTagname()).build())
                    .collect(Collectors.toList());
            // 需要更新的标签
            List<CpTag> tagUpdateList = cpTagList.stream().filter(wxCpTag -> tagIdList.contains(wxCpTag.getTagid()))
                    .map(item -> CpTag.builder().tagid(item.getTagid()).tagname(item.getTagname()).build())
                    .collect(Collectors.toList());
            // 需要删除的标签id列表
            List<Long> tagDeleteList = tags.stream().map(CpTag::getTagid).filter(tagid -> !cpTagIdList.contains(tagid))
                    .collect(Collectors.toList());

            // 处理标签和关系到标准表
            processTagAndRelationWithTransaction(tagSaveList, tagUpdateList, cpTagList, userTagRelationList,
                    deptTagRelationList, tagDeleteList);
        } catch (Exception e) {
            log.error("标签同步过程发生异常", e);
            throw e;
        }
    }

    /**
     * 处理标签和关系到标准表（带事务控制）
     */
    private void processTagAndRelationWithTransaction(List<CpTag> tagSaveList, List<CpTag> tagUpdateList, List<TagDto> cpTagList,
                                                      List<CpUserTagRelation> userTagRelationList, List<CpDepartmentTagRelation> deptTagRelationList,
                                                      List<Long> tagDeleteList) {
        // 使用独立事务处理标签数据
        TransactionTemplate tagTransactionTemplate = new TransactionTemplate(
                transactionTemplate.getTransactionManager());
        tagTransactionTemplate.setPropagationBehavior(
                org.springframework.transaction.TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        tagTransactionTemplate.setTimeout(AddressbookConstant.TRANSACTION_TIMEOUT);

        tagTransactionTemplate.execute(status -> {
            try {
                log.info("开始处理标签数据，新增: {}, 更新: {}, 删除: {}",
                        tagSaveList.size(), tagUpdateList.size(), tagDeleteList.size());

                if (CollUtil.isNotEmpty(tagSaveList)) {
                    // 保存标签
                    tagService.saveBatch(tagSaveList);
                }

                if (CollUtil.isNotEmpty(tagUpdateList)) {
                    // 更新标签
                    tagService.updateBatchById(tagUpdateList);
                }

                if (CollUtil.isNotEmpty(tagDeleteList)) {
                    // 删除标签
                    tagService.removeByIds(tagDeleteList);

                    // 删除标签用户关系
                    LambdaUpdateWrapper<CpUserTagRelation> utrUw = Wrappers.lambdaUpdate(CpUserTagRelation.class);
                    utrUw.in(CpUserTagRelation::getTagid, tagDeleteList);
                    userTagRelationService.remove(utrUw);

                    // 删除标签部门关系
                    LambdaUpdateWrapper<CpDepartmentTagRelation> dtrUw = Wrappers.lambdaUpdate(CpDepartmentTagRelation.class);
                    dtrUw.in(CpDepartmentTagRelation::getTagid, tagDeleteList);
                    departmentTagRelationService.remove(dtrUw);
                }

                log.info("同步标签成功{}条", cpTagList.size());

                // 保存标签用户关系
                if (CollUtil.isNotEmpty(userTagRelationList)) {
                    // 清除标签用户关系
                    userTagRelationMapper.truncate();
                    userTagRelationService.saveBatch(userTagRelationList);
                    log.info("同步标签用户关系成功{}条", userTagRelationList.size());
                }

                // 保存标签部门关系
                if (CollUtil.isNotEmpty(deptTagRelationList)) {
                    // 清除标签部门关系
                    departmentTagRelationMapper.truncate();
                    departmentTagRelationService.saveBatch(deptTagRelationList);
                    log.info("同步标签部门关系成功{}条", deptTagRelationList.size());
                }

                return true;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("同步标签数据失败", e);
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("同步标签数据失败: " + e.getMessage());
            }
        });
    }

    /**
     * 处理标签和关系到标准表（原方法保持兼容）
     */
    private void processTagAndRelation(List<CpTag> tagSaveList, List<CpTag> tagUpdateList, List<TagDto> cpTagList,
                                       List<CpUserTagRelation> userTagRelationList, List<CpDepartmentTagRelation> deptTagRelationList,
                                       List<Long> tagDeleteList) {
        if (CollUtil.isNotEmpty(tagSaveList)) {
            // 保存标签
            tagService.saveBatch(tagSaveList);
        }

        if (CollUtil.isNotEmpty(tagUpdateList)) {
            // 更新标签
            tagService.updateBatchById(tagUpdateList);
        }

        if (CollUtil.isNotEmpty(tagDeleteList)) {
            // 删除标签
            tagService.removeByIds(tagDeleteList);

            // 删除标签用户关系
            LambdaUpdateWrapper<CpUserTagRelation> utrUw = Wrappers.lambdaUpdate(CpUserTagRelation.class);
            utrUw.in(CpUserTagRelation::getTagid, tagDeleteList);
            userTagRelationService.remove(utrUw);

            // 删除标签部门关系
            LambdaUpdateWrapper<CpDepartmentTagRelation> dtrUw = Wrappers.lambdaUpdate(CpDepartmentTagRelation.class);
            dtrUw.in(CpDepartmentTagRelation::getTagid, tagDeleteList);
            departmentTagRelationService.remove(dtrUw);
        }

        log.info("同步标签成功{}条", cpTagList.size());

        // 保存标签用户关系
        if (CollUtil.isNotEmpty(userTagRelationList)) {
            // 清除标签用户关系
            userTagRelationMapper.truncate();

            userTagRelationService.saveBatch(userTagRelationList);
            log.info("同步标签用户关系成功{}条", userTagRelationList.size());
        }

        // 保存标签部门关系
        if (CollUtil.isNotEmpty(deptTagRelationList)) {
            // 清除标签部门关系
            departmentTagRelationMapper.truncate();

            departmentTagRelationService.saveBatch(deptTagRelationList);
            log.info("同步标签部门关系成功{}条", deptTagRelationList.size());
        }
    }

    /**
     * 用户同步
     */
    private void userSync(String corpId, String agentId, List<CpDepartment> cpDeptList) {
        // 使用线程安全的集合来存储用户列表,由于一个人可以有多个部门，因此需要一个map来存储
        Map<String, UserDto> cpUserMap = new ConcurrentHashMap<>(2 ^ 16);

        // 添加超时控制和异常处理的并行处理
        List<CompletableFuture<Void>> futures = Optional.ofNullable(cpDeptList)
                .orElseGet(ArrayList::new)
                .stream()
                .map(dept -> CompletableFuture.runAsync(() -> {
                    try {
                        // 查询指定部门下的全部用户
                        Result<List<UserDto>> result = userFeign.userList(corpId, agentId, Boolean.FALSE, dept.getId());
                        // 企微查询异常
                        if (!result.success()) {
                            log.error("通讯录同步-部门：{}{},查询企微用户异常：{}", dept.getName(), dept.getId(),
                                    result.getMsg() + result.getCode());
                            return;
                        }
                        if (CollUtil.isNotEmpty(result.getResult())) {
                            // 线程安全地添加用户数据
                            result.getResult().forEach(user -> {
                                cpUserMap.put(user.getUserid(), user);
                            });
                        }
                    } catch (Exception e) {
                        log.error("同步部门用户数据失败，部门：{}{}", dept.getName(), dept.getId(), e);
                    }
                }, commonExecutor))
                .collect(Collectors.toList());

        try {
            // 等待所有任务完成，设置超时时间防止无限等待
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(AddressbookConstant.CONCURRENT_TASK_TIMEOUT_MINUTES, TimeUnit.MINUTES);
        } catch (TimeoutException e) {
            log.error("用户同步任务超时", e);
            // 取消未完成的任务
            futures.forEach(future -> future.cancel(true));
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("用户同步任务超时");
        } catch (InterruptedException e) {
            log.error("用户同步任务被中断", e);
            Thread.currentThread().interrupt();
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("用户同步任务被中断");
        } catch (ExecutionException e) {
            log.error("用户同步任务执行失败", e.getCause());
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("用户同步任务执行失败: " + e.getCause().getMessage());
        }
        // 此处为所有企微中的用户
        Collection<UserDto> cpUserList = cpUserMap.values();
        // 为空则不处理
        if (CollUtil.isEmpty(cpUserList)) {
            return;
        }

        List<CpUser> userList = new ArrayList<>();
        for (UserDto cpUser : cpUserList) {
            CpUser user = JacksonUtils.toObj(JacksonUtils.toJson(cpUser), CpUser.class);
            userList.add(user);
        }
        // 创建一个新的TransactionTemplate并设置传播行为为REQUIRES_NEW
        TransactionTemplate newTransactionTemplate = new TransactionTemplate(
                transactionTemplate.getTransactionManager());
        newTransactionTemplate
                .setPropagationBehavior(org.springframework.transaction.TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        // 设置事务超时时间
        newTransactionTemplate.setTimeout(AddressbookConstant.TRANSACTION_TIMEOUT);

        // 使用独立事务处理用户表
        newTransactionTemplate.execute(status -> {
            try {
                log.info("开始清除和保存用户数据，用户数量：{}", userList.size());
                // 清除用户
                userMapper.truncate();
                // 保存用户
                userService.saveBatch(userList);
                log.info("同步用户成功{}条", userList.size());
                return true;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("同步用户失败", e);
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("同步用户失败: " + e.getMessage());
            }
        });

        List<CpUserDepartmentRelation> userDeptRelationList = new ArrayList<>();
        // 用户部门关系
        for (CpUser user : userList) {
            // 部门列表
            List<Long> deptIdList = user.getDepartment();
            // 是否是部门负责人
            List<Integer> isLeaderInDept = user.getIsLeaderInDept();
            for (int i = 0; i < deptIdList.size(); i++) {
                Long deptId = deptIdList.get(i);
                Integer isLeader = isLeaderInDept.get(i);
                CpUserDepartmentRelation userDeptRelation = CpUserDepartmentRelation.builder()
                        .userid(user.getUserid())
                        .departmentid(deptId)
                        .isLeader(isLeader)
                        .build();
                userDeptRelationList.add(userDeptRelation);
            }
        }

        // 使用独立事务处理用户部门关系表
        newTransactionTemplate.execute(status -> {
            try {
                log.info("开始清除和保存用户部门关系数据，关系数量：{}", userDeptRelationList.size());
                // 清除用户部门关联
                userDepartmentRelationMapper.truncate();
                // 保存用户部门关联
                userDepartmentRelationService.saveBatch(userDeptRelationList);
                log.info("同步用户部门关系成功{}条", userDeptRelationList.size());
                return true;
            } catch (Exception e) {
                status.setRollbackOnly();
                log.error("同步用户部门关系失败", e);
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("同步用户部门关系失败: " + e.getMessage());
            }
        });
    }

    /**
     * 部门同步
     */
    private List<CpDepartment> deptSync(String corpId, String agentId) {
        try {
            log.info("开始同步部门数据，corpId: {}, agentId: {}", corpId, agentId);

            // 查询全部部门
            Result<List<DepartmentDto>> result = deptFeign.departmentList(corpId, agentId, CommonConstant.ROOT_DEPT_ID);
            // 企微查询异常
            if (!result.success()) {
                throw new ServiceException("通讯录同步-查询企微部门异常：" + result.getMsg(), result.getCode());
            }

            List<DepartmentDto> cpDepartList = result.getResult();
            // 为空则不处理
            if (CollUtil.isEmpty(cpDepartList)) {
                log.info("企微部门数据为空，跳过同步");
                return new ArrayList<>();
            }

            log.info("从企微获取到部门数据{}条", cpDepartList.size());

            Map<Long, DepartmentDto> deptMap = cpDepartList.stream()
                    .collect(Collectors.toMap(DepartmentDto::getId, Function.identity()));

            List<CpDepartment> deptList = new ArrayList<>();
            for (DepartmentDto deptDto : cpDepartList) {
                CpDepartment dept = JacksonUtils.toObj(JacksonUtils.toJson(deptDto), CpDepartment.class);

                // 设置path、idPath
                setPath(deptDto, deptMap, null, dept);

                deptList.add(dept);
            }

            // 使用独立事务处理部门数据
            TransactionTemplate deptTransactionTemplate = new TransactionTemplate(
                    transactionTemplate.getTransactionManager());
            deptTransactionTemplate.setPropagationBehavior(
                    org.springframework.transaction.TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            deptTransactionTemplate.setTimeout(AddressbookConstant.TRANSACTION_TIMEOUT);

            deptTransactionTemplate.execute(status -> {
                try {
                    log.info("开始清除和保存部门数据，部门数量：{}", deptList.size());
                    // 清空部门
                    departmentMapper.truncate();
                    // 保存部门
                    departmentService.saveBatch(deptList);
                    log.info("同步部门成功{}条", deptList.size());
                    return true;
                } catch (Exception e) {
                    status.setRollbackOnly();
                    log.error("同步部门失败", e);
                    throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("同步部门失败: " + e.getMessage());
                }
            });

            return deptList;
        } catch (Exception e) {
            log.error("部门同步过程发生异常", e);
            throw e;
        }
    }

    /**
     * 移除旧部门标签关联
     */
    private void removeOldDeptTagRelation(List<CpDepartment> oldDeptList) {
        // 无需调用企微标签删除旧组织架构 在部门删除时 标签就会移除对应的部门

        // 移除旧部门标签关联标准表
        List<Long> oldDeptIdList = oldDeptList.stream().map(CpDepartment::getId).collect(Collectors.toList());
        LambdaQueryWrapper<CpDepartmentTagRelation> wrapper = Wrappers.lambdaQuery(CpDepartmentTagRelation.class);
        wrapper.in(CpDepartmentTagRelation::getDepartmentid, oldDeptIdList);
        departmentTagRelationService.remove(wrapper);
    }

    /**
     * 处理新部门 ：删除旧组织架构 并将新组织架构的二级部门pid设为1 删除新组织架构根部门
     */
    private void processNewDept(List<CpDepartment> newDeptList, List<CpDepartment> oldDeptList, CpDepartment newDept,
                                String corpId) {
        // 删除旧组织架构
        delOldDeptToCp(oldDeptList, corpId);

        // 获取新组织架构的二级部门id列表
        List<Long> newDeptIdLvl2List = newDeptList.stream().filter(item -> newDept.getId().equals(item.getParentid()))
                .map(CpDepartment::getId).collect(Collectors.toList());

        // 将新组织架构的二级部门pid设为1
        // 去除新组织架构根部门
        for (Long deptId : newDeptIdLvl2List) {
            // 更新至企微
            DepartmentSaveQo updateQo = new DepartmentSaveQo();
            updateQo.setId(deptId);
            updateQo.setParentid(CommonConstant.ROOT_DEPT_ID);
            Result<Object> deptUpdateResult = deptFeign.departmentUpdate(corpId, updateQo);
            if (!deptUpdateResult.success()) {
                // 设为失败
                log.error("将新组织架构的二级部门的pid设为1失败：{}", deptUpdateResult.getMsg());
            }
        }

        // 删除企微新组织架构根部门
        Result<?> deptDeleteResult = deptFeign.departmentDelete(corpId, newDept.getId());
        if (!deptDeleteResult.success()) {
            // 设为失败
            log.error("删除企微新组织架构根部门失败：{}", deptDeleteResult.getMsg());
        }

        List<CpDepartment> updateList = new ArrayList<>();
        // 移除新组织架构根部门
        newDeptList.removeIf(department -> newDept.getId().equals(department.getId()));
        // 更新标准表
        for (CpDepartment department : newDeptList) {
            CpDepartment update = CpDepartment.builder().id(department.getId()).build();
            // 将新组织架构的二级部门pid设为1
            if (newDeptIdLvl2List.contains(department.getId())) {
                update.setParentid(CommonConstant.ROOT_DEPT_ID);
            }
            // 更新path
            String newPath = department.getPath().replace(StrPool.SLASH + newDept.getName(), "");
            String newIdPath = department.getIdPath().replace(StrPool.SLASH + newDept.getId(), "");
            update.setPath(newPath);
            update.setIdPath(newIdPath);
            updateList.add(update);
        }

        // 更新标准表
        departmentService.updateBatchById(updateList);

        // 去除标准表新组织架构根部门
        departmentService.removeById(newDept.getId());
    }

    /**
     * 删除企微组织架构
     */
    private void delOldDeptToCp(List<CpDepartment> oldDeptList, String corpId) {
        // 删除旧组织架构
        // 部门id对应子部门id列表
        Map<Long, List<Long>> oldPidCidMap = oldDeptList.stream().collect(
                Collectors.groupingBy(CpDepartment::getParentid,
                        Collectors.mapping(CpDepartment::getId, Collectors.toList())));

        // 获取1级部门
        List<CpDepartment> lvl1List = oldDeptList.stream()
                .filter(item -> item.getParentid() == CommonConstant.ROOT_DEPT_ID).collect(Collectors.toList());
        // 经测试部门接口企微只支持5个线程的并发 超过的话会报45033-接口并发调用超过限制
        List<List<CpDepartment>> split = ListUtil.splitAvg(lvl1List, AddressbookConstant.PROCESS_THREAD_COUNT_5);
        List<Future<Integer>> futureList = new ArrayList<>();
        for (List<CpDepartment> departmentList : split) {
            if (CollUtil.isEmpty(departmentList)) {
                continue;
            }
            List<Long> deptIdList = departmentList.stream().map(CpDepartment::getId).collect(Collectors.toList());

            // 多线程删除企微旧组织架构
            Future<Integer> future = departmentService.asyncDelDeptToCp(corpId, deptIdList, oldPidCidMap);
            futureList.add(future);
        }
        // 校验全部任务执行完
        verifyTaskFinishWithTimeout(futureList, "删除部门");
        log.info("同步删除部门至企微完成");

        List<Long> oldDeptIdList = oldDeptList.stream().map(CpDepartment::getId).collect(Collectors.toList());
        // 删除标准表旧组织架构
        departmentService.removeByIds(oldDeptIdList);
    }

    /**
     * 用户移除以前的部门关联
     */
    private void userRemoveOldDept(List<Long> newIdList, List<CpDepartment> oldDeptList, String corpId,
                                   Long newDeptId) {

        // 查询用户
        List<CpUser> userList = userService.list();
        for (CpUser user : userList) {
            List<Long> deptIdList = user.getDepartment();
            // 移除非新组织架构的部门
            deptIdList.removeIf(item -> !newIdList.contains(item));

            // 将挂载在企微新组织架构根部门的用户的部门设置为1
            int index = deptIdList.indexOf(newDeptId);
            if (index != -1) {
                deptIdList.set(index, CommonConstant.ROOT_DEPT_ID);
            }
        }

        List<Future<Integer>> futureList = new ArrayList<>();
        // 拆分多线程执行
        List<List<CpUser>> split = ListUtil.splitAvg(userList, AddressbookConstant.PROCESS_THREAD_COUNT_16);
        for (List<CpUser> userUpdateList : split) {
            if (CollUtil.isEmpty(userUpdateList)) {
                continue;
            }

            // 异步方式同步部门至企微
            Future<Integer> future = userService.asyncUpdateUserDeptToCp(corpId, userUpdateList);
            futureList.add(future);
        }

        // 校验全部任务执行完
        verifyTaskFinishWithTimeout(futureList, "用户移除部门关联");
        log.info("用户移除以前的部门关联至企微完成");

        // 更新用户标准表
        userService.updateBatchById(userList);

        // 删除旧的用户部门关联
        LambdaQueryWrapper<CpUserDepartmentRelation> wrapper = Wrappers.lambdaQuery(CpUserDepartmentRelation.class);
        List<Long> oldDeptIds = oldDeptList.stream().map(CpDepartment::getId).collect(Collectors.toList());
        wrapper.in(CpUserDepartmentRelation::getDepartmentid, oldDeptIds);
        userDepartmentRelationService.remove(wrapper);
    }

    /**
     * 标签恢复
     */
    private void tagRecover() {
        // 无需调用企微标签删除新组织架构 在部门删除时 标签就会移除对应的部门

        // 清空标签标准表
        tagMapper.truncate();
        departmentTagRelationBakMapper.truncate();
        userTagRelationMapper.truncate();

        // 复制备份表到标准表
        tagMapper.copyBakToStandard();
        departmentTagRelationMapper.copyBakToStandard();
        userTagRelationMapper.copyBakToStandard();
    }

    /**
     * 恢复用户
     */
    private void userRecover(String corpId) {
        // 查询备份
        List<CpUserBak> userBakList = userBakService.list();

        // 同步至企微
        List<Future<Integer>> futureList = new ArrayList<>();
        // 拆分多线程执行
        List<List<CpUserBak>> split = ListUtil.splitAvg(userBakList, AddressbookConstant.PROCESS_THREAD_COUNT_16);
        for (List<CpUserBak> userOperateList : split) {
            if (CollUtil.isEmpty(userOperateList)) {
                continue;
            }

            // 异步方式同步部门至企微
            Future<Integer> future = userService.asyncUserBakToCp(corpId, userOperateList);
            futureList.add(future);
        }

        // 校验全部任务执行完
        verifyTaskFinish(futureList);
        log.info("恢复用户至企微完成");

        // 先清空标准表
        userMapper.truncate();
        userDepartmentRelationMapper.truncate();

        // 复制备份表到标准表
        userMapper.copyBakToStandard();
        userDepartmentRelationMapper.copyBakToStandard();
    }

    /**
     * 查询是否有数据
     */
    private boolean isExistDepartmentBak() {
        LambdaQueryWrapper<CpDepartmentBak> deptBakWrapper = Wrappers.lambdaQuery(CpDepartmentBak.class);
        deptBakWrapper.select(CpDepartmentBak::getId);
        deptBakWrapper.last("limit 1");
        CpDepartmentBak departmentBak = departmentBakService.getBaseMapper().selectOne(deptBakWrapper);
        return departmentBak != null && departmentBak.getId() != null;
    }

    /**
     * 处理标签与用户、部门的关联
     */
    private void tagRelationOperate(String corpId) {

        // 处理用户标签关系
        userTagRelationOperate(corpId, null);

        // 处理部门标签关系
        deptTagRelationOperate(corpId);
    }

    /**
     * 处理用户标签关系
     */
    private void userTagRelationOperate(String corpId, List<Long> idList) {
        // 获取用户标签未处理的数据
        LambdaQueryWrapper<CpUserTagRelationOperate> userTagQueryWrapper = Wrappers
                .lambdaQuery(CpUserTagRelationOperate.class);
        userTagQueryWrapper.eq(CpUserTagRelationOperate::getOperateStatus, OperateStatusEnum.UNTREATED.getCode());
        // 指定id查询
        if (CollUtil.isNotEmpty(idList)) {
            userTagQueryWrapper.in(CpUserTagRelationOperate::getId, idList);
        }

        List<CpUserTagRelationOperate> userTagList = userTagRelationOperateService.list(userTagQueryWrapper);

        // 如果为空，则不处理
        if (CollUtil.isEmpty(userTagList)) {
            return;
        }

        // 查询标签
        List<CpTag> tagList = tagService
                .listByIds(userTagList.stream().map(CpUserTagRelationOperate::getTagid).collect(Collectors.toList()));
        // map
        Map<Long, CpTag> tagMap = tagList.stream().collect(Collectors.toMap(CpTag::getTagid, Function.identity()));

        // 按标签分组
        Map<Long, List<CpUserTagRelationOperate>> tagRelationMap = userTagList.stream()
                .collect(Collectors.groupingBy(CpUserTagRelationOperate::getTagid));
        // 转为list
        List<List<CpUserTagRelationOperate>> collect = new ArrayList<>(tagRelationMap.values());

        List<Future<Integer>> futureList = new ArrayList<>();
        // 拆分多线程执行
        List<List<List<CpUserTagRelationOperate>>> split = ListUtil.splitAvg(collect,
                AddressbookConstant.PROCESS_THREAD_COUNT_5);
        for (List<List<CpUserTagRelationOperate>> lists : split) {
            if (CollUtil.isEmpty(lists)) {
                continue;
            }

            // 异步方式同步至企微
            Future<Integer> future = tagService.asyncUserTagToCp(corpId, lists, tagMap);
            futureList.add(future);
        }

        // 校验全部任务执行完
        verifyTaskFinish(futureList);
        log.info("同步标签关系至企微完成");

        // 操作成功的插入标准表
        List<CpUserTagRelationOperate> successList = userTagList.stream()
                .filter(item -> OperateStatusEnum.SUCCESS.getCode() == item.getOperateStatus())
                .collect(Collectors.toList());

        // 处理标准表
        processUserTagStandard(successList);

        // 处理操作表
        processUserTagOperate(userTagList);

        long failCount = userTagList.stream()
                .filter(item -> OperateStatusEnum.FAIL.getCode() == item.getOperateStatus()).count();
        log.info("处理用户标签成功{}条，失败{}条", successList.size(), failCount);
    }

    /**
     * 处理操作表
     */
    private void processUserTagOperate(List<CpUserTagRelationOperate> userTagList) {
        // 不为未处理的都更新
        List<CpUserTagRelationOperate> opUpdateList = userTagList.stream()
                .filter(item -> OperateStatusEnum.UNTREATED.getCode() != item.getOperateStatus())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(opUpdateList)) {
            userTagRelationOperateService.updateBatchById(opUpdateList);
        }
    }

    /**
     * 处理标准表
     */
    private void processUserTagStandard(List<CpUserTagRelationOperate> successList) {
        if (CollUtil.isNotEmpty(successList)) {
            // 用户id列表
            List<String> userIdList = successList.stream().map(CpUserTagRelationOperate::getUserid).distinct()
                    .collect(Collectors.toList());
            List<CpUserTagRelation> userTagList;
            // 查询标准表 如果数量小于1000，则按条件查询
            if (userIdList.size() < AddressbookConstant.IN_QUERY_SIZE) {
                userTagList = userTagRelationService.listTagRelationByUseridList(userIdList);
            } else {
                userTagList = userTagRelationService.list();
            }

            Map<String, CpUserTagRelation> userTagMap = userTagList.stream().collect(
                    Collectors.toMap(item -> item.getTagid() + "-" + item.getUserid(), Function.identity()));

            List<CpUserTagRelation> userTagSaveList = new ArrayList<>();
            for (CpUserTagRelationOperate op : successList) {
                CpUserTagRelation userTag = userTagMap.get(op.getTagid() + "-" + op.getUserid());

                // 不存在则新增
                if (userTag == null) {
                    CpUserTagRelation userTagParam = BeanUtil.copyProperties(op, CpUserTagRelation.class);
                    userTagSaveList.add(userTagParam);
                }
            }
            // 插入标准表
            if (CollUtil.isNotEmpty(userTagSaveList)) {
                userTagRelationService.saveBatch(userTagSaveList);
            }
        }
    }

    /**
     * 处理部门标签关系
     */
    private void deptTagRelationOperate(String corpId) {
        // 获取部门标签未处理的数据
        LambdaQueryWrapper<CpDepartmentTagRelationOperate> deptTagQueryWrapper = Wrappers
                .lambdaQuery(CpDepartmentTagRelationOperate.class);
        deptTagQueryWrapper.eq(CpDepartmentTagRelationOperate::getOperateStatus, OperateStatusEnum.UNTREATED.getCode());
        List<CpDepartmentTagRelationOperate> deptTagList = departmentTagRelationOperateService
                .list(deptTagQueryWrapper);

        // 如果为空，则不处理
        if (CollUtil.isEmpty(deptTagList)) {
            return;
        }

        // 查询标签
        List<CpTag> tagList = tagService.listByIds(
                deptTagList.stream().map(CpDepartmentTagRelationOperate::getTagid).collect(Collectors.toList()));
        // map
        Map<Long, CpTag> tagMap = tagList.stream().collect(Collectors.toMap(CpTag::getTagid, Function.identity()));

        // 按标签分组
        Map<Long, List<CpDepartmentTagRelationOperate>> tagRelationMap = deptTagList.stream()
                .collect(Collectors.groupingBy(CpDepartmentTagRelationOperate::getTagid));
        // 转为list
        List<List<CpDepartmentTagRelationOperate>> collect = new ArrayList<>(tagRelationMap.values());

        List<Future<Integer>> futureList = new ArrayList<>();
        // 拆分多线程执行
        List<List<List<CpDepartmentTagRelationOperate>>> split = ListUtil.splitAvg(collect,
                AddressbookConstant.PROCESS_THREAD_COUNT_5);
        for (List<List<CpDepartmentTagRelationOperate>> lists : split) {
            if (CollUtil.isEmpty(lists)) {
                continue;
            }

            // 异步方式同步至企微
            Future<Integer> future = tagService.asyncDeptTagToCp(corpId, lists, tagMap);
            futureList.add(future);
        }

        // 校验全部任务执行完
        verifyTaskFinish(futureList);
        log.info("同步标签至企微完成");

        // 操作成功的插入标准表
        List<CpDepartmentTagRelationOperate> successList = deptTagList.stream()
                .filter(item -> OperateStatusEnum.SUCCESS.getCode() == item.getOperateStatus())
                .collect(Collectors.toList());

        // 处理标准表
        processDepartmentTagStandard(successList);

        // 处理操作表
        processDepartmentTagOperate(deptTagList);

        long failCount = deptTagList.stream()
                .filter(item -> OperateStatusEnum.FAIL.getCode() == item.getOperateStatus()).count();
        log.info("处理部门标签成功{}条，失败{}条", successList.size(), failCount);
    }

    /**
     * 处理操作表
     */
    private void processDepartmentTagOperate(List<CpDepartmentTagRelationOperate> deptTagList) {
        // 不为未处理的都更新
        List<CpDepartmentTagRelationOperate> opUpdateList = deptTagList.stream()
                .filter(item -> OperateStatusEnum.UNTREATED.getCode() != item.getOperateStatus())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(opUpdateList)) {
            departmentTagRelationOperateService.updateBatchById(opUpdateList);
        }
    }

    /**
     * 处理标准表
     */
    private void processDepartmentTagStandard(List<CpDepartmentTagRelationOperate> successList) {
        if (CollUtil.isNotEmpty(successList)) {
            // 查询标准表
            List<CpDepartmentTagRelation> userTagList = departmentTagRelationService.list();
            Map<String, CpDepartmentTagRelation> userTagMap = userTagList.stream().collect(
                    Collectors.toMap(item -> item.getTagid() + "-" + item.getDepartmentid(), Function.identity()));

            List<CpDepartmentTagRelation> departmentTagSaveList = new ArrayList<>();
            for (CpDepartmentTagRelationOperate op : successList) {
                CpDepartmentTagRelation userTag = userTagMap.get(op.getTagid() + "-" + op.getDepartmentid());

                // 不存在则新增
                if (userTag == null) {
                    CpDepartmentTagRelation userTagParam = BeanUtil.copyProperties(op, CpDepartmentTagRelation.class);
                    departmentTagSaveList.add(userTagParam);
                }
            }
            // 插入标准表
            if (CollUtil.isNotEmpty(departmentTagSaveList)) {
                departmentTagRelationService.saveBatch(departmentTagSaveList);
            }
        }
    }

    /**
     * 恢复部门
     */
    private void deptRecover(String corpId, Long newDeptId) {
        // 查询组织架构
        List<CpDepartment> list = departmentService.list();

        // 部门id对应子部门id列表
        Map<Long, List<Long>> pidCidMap = list.stream().collect(
                Collectors.groupingBy(CpDepartment::getParentid,
                        Collectors.mapping(CpDepartment::getId, Collectors.toList())));

        // 获取新组织架构下的1级部门
        List<CpDepartment> lvl1List = list.stream().filter(item -> item.getParentid().equals(newDeptId))
                .collect(Collectors.toList());
        // 经测试部门接口企微只支持5个线程的并发 超过的话会报45033-接口并发调用超过限制
        List<List<CpDepartment>> split = ListUtil.splitAvg(lvl1List, AddressbookConstant.PROCESS_THREAD_COUNT_5);
        List<Future<Integer>> futureList = new ArrayList<>();
        for (List<CpDepartment> departmentList : split) {
            if (CollUtil.isEmpty(departmentList)) {
                continue;
            }

            List<Long> deptIdList = departmentList.stream().map(CpDepartment::getId).collect(Collectors.toList());
            // 多线程删除新组织架构
            Future<Integer> future = departmentService.asyncDelDeptToCp(corpId, deptIdList, pidCidMap);
            futureList.add(future);
        }
        // 校验全部任务执行完
        verifyTaskFinish(futureList);

        // 删除新组织架构根部门
        try {
            Result<?> result = deptFeign.departmentDelete(corpId, newDeptId);
            if (!result.success()) {
                log.error("删除部门[{}] 失败：{}", newDeptId, result.getMsg());
            } else {
                log.info("删除部门[{}] 成功", newDeptId);
            }
        } catch (Exception e) {
            log.error("feign调用异常：{}", e.getMessage(), e);
        }

        log.info("同步删除部门至企微完成");

        // 先清空标准表
        departmentMapper.truncate();

        // 复制备份表到标准表
        departmentMapper.copyBakToStandard();
    }

    /**
     * 备份标签
     */
    private void tagBak(String corpId) {
        // 查询全部标签
        Result<List<TagDto>> result = tagFeign.tagList(corpId, null);
        // 企微查询异常
        if (!result.success()) {
            throw new ServiceException("查询企微标签异常：" + result.getMsg(), result.getCode());
        }

        List<TagDto> cpTagList = result.getResult();
        // 标签为空则不处理
        if (CollUtil.isEmpty(cpTagList)) {
            return;
        }

        // tagid对应用户id、部门id列表
        Map<Long, List<String>> tagUserMap = new HashMap<>();
        Map<Long, List<Long>> tagDeptMap = new HashMap<>();
        // 查询标签关联
        getTagRelation(corpId, cpTagList, tagUserMap, tagDeptMap);

        List<CpTagBak> tagBakList = new ArrayList<>();
        List<CpUserTagRelationBak> userTagRelationBakList = new ArrayList<>();
        List<CpDepartmentTagRelationBak> deptTagRelationBakList = new ArrayList<>();
        for (TagDto wxCpTag : cpTagList) {
            Long tagId = wxCpTag.getTagid();
            // 标签备份
            CpTagBak tagBak = CpTagBak.builder()
                    .tagname(wxCpTag.getTagname())
                    .tagid(tagId).build();
            tagBakList.add(tagBak);

            // 标签用户关系备份
            List<String> userIdList = tagUserMap.get(tagId);
            if (CollUtil.isNotEmpty(userIdList)) {
                for (String userId : userIdList) {
                    CpUserTagRelationBak userTagRelation = CpUserTagRelationBak.builder()
                            .tagid(tagId)
                            .userid(userId).build();
                    userTagRelationBakList.add(userTagRelation);
                }
            }

            // 标签部门关系备份
            List<Long> deptIdList = tagDeptMap.get(tagId);
            if (CollUtil.isNotEmpty(deptIdList)) {
                for (Long deptId : deptIdList) {
                    CpDepartmentTagRelationBak deptTagRelation = CpDepartmentTagRelationBak.builder()
                            .tagid(tagId)
                            .departmentid(deptId).build();
                    deptTagRelationBakList.add(deptTagRelation);
                }
            }
        }

        // 保存标签备份
        tagBakService.saveBatch(tagBakList);
        log.info("备份标签成功{}条", tagBakList.size());

        // 保存标签用户关系备份
        if (CollUtil.isNotEmpty(userTagRelationBakList)) {
            userTagRelationBakService.saveBatch(userTagRelationBakList);
            log.info("备份标签用户关系成功{}条", userTagRelationBakList.size());
        }

        // 标签部门关系备份
        if (CollUtil.isNotEmpty(deptTagRelationBakList)) {
            departmentTagRelationBakService.saveBatch(deptTagRelationBakList);
            log.info("备份标签部门关系成功{}条", deptTagRelationBakList.size());
        }
    }

    /**
     * 查询标签关联（带超时控制）
     */
    private void getTagRelationWithTimeout(String corpId, List<TagDto> cpTagList, Map<Long, List<String>> tagUserMap,
                                           Map<Long, List<Long>> tagDeptMap) {
        List<Future<Integer>> futureList = new ArrayList<>();
        // 拆分多线程执行
        List<List<TagDto>> split = ListUtil.splitAvg(cpTagList, AddressbookConstant.PROCESS_THREAD_COUNT_5);
        for (List<TagDto> wxCpTags : split) {
            if (CollUtil.isEmpty(wxCpTags)) {
                continue;
            }

            Future<Integer> future = tagService.asyncGetTagUser(corpId, wxCpTags, tagUserMap, tagDeptMap);
            futureList.add(future);
        }

        // 校验全部任务执行完（带超时控制）
        verifyTaskFinishWithTimeout(futureList, "查询标签关联");
    }

    /**
     * 查询标签关联（原方法保持兼容）
     */
    private void getTagRelation(String corpId, List<TagDto> cpTagList, Map<Long, List<String>> tagUserMap,
                                Map<Long, List<Long>> tagDeptMap) {
        List<Future<Integer>> futureList = new ArrayList<>();
        // 拆分多线程执行
        List<List<TagDto>> split = ListUtil.splitAvg(cpTagList, AddressbookConstant.PROCESS_THREAD_COUNT_5);
        for (List<TagDto> wxCpTags : split) {
            if (CollUtil.isEmpty(wxCpTags)) {
                continue;
            }

            Future<Integer> future = tagService.asyncGetTagUser(corpId, wxCpTags, tagUserMap, tagDeptMap);
            futureList.add(future);
        }

        // 校验全部任务执行完
        verifyTaskFinish(futureList);
    }

    /**
     * 备份用户
     */
    private void userBak(String corpId, String agentId) {
        // 查询全部用户
        Result<List<UserDto>> result = userFeign.userList(corpId, agentId, Boolean.TRUE, CommonConstant.ROOT_DEPT_ID);
        // 企微查询异常
        if (!result.success()) {
            throw new ServiceException("备份用户-查询企微用户异常：" + result.getMsg(), result.getCode());
        }

        List<UserDto> cpUserList = result.getResult();
        // 为空则不处理
        if (CollUtil.isEmpty(cpUserList)) {
            return;
        }

        List<CpUserBak> userBakList = JacksonUtils.toObj(JacksonUtils.toJson(cpUserList), new TypeReference<>() {
        });
        // 保存用户备份
        userBakService.saveBatch(userBakList);
        log.info("备份用户成功{}条", userBakList.size());

        List<CpUserDepartmentRelationBak> userDeptRelationBakList = new ArrayList<>();
        // 用户部门关系备份
        for (CpUserBak userBak : userBakList) {
            // 部门列表
            List<Long> deptIdList = userBak.getDepartment();
            // 是否是部门负责人
            List<Integer> isLeaderInDept = userBak.getIsLeaderInDept();
            for (int i = 0; i < deptIdList.size(); i++) {
                Long deptId = deptIdList.get(i);
                Integer isLeader = isLeaderInDept.get(i);
                CpUserDepartmentRelationBak userDeptRelationBak = CpUserDepartmentRelationBak.builder()
                        .userid(userBak.getUserid())
                        .departmentid(deptId)
                        .isLeader(isLeader)
                        .build();
                userDeptRelationBakList.add(userDeptRelationBak);
            }
        }
        userDepartmentRelationBakService.saveBatch(userDeptRelationBakList);
        log.info("备份用户部门关系成功{}条", userDeptRelationBakList.size());
    }

    /**
     * 备份部门
     */
    private void deptBak(String corpId, String agentId) {
        // 查询全部部门
        Result<List<DepartmentDto>> result = deptFeign.departmentList(corpId, agentId, CommonConstant.ROOT_DEPT_ID);
        // 企微查询异常
        if (!result.success()) {
            throw new ServiceException("备份部门-查询企微部门异常：" + result.getMsg(), result.getCode());
        }

        List<DepartmentDto> cpDepartList = result.getResult();
        // 为空则不处理
        if (CollUtil.isEmpty(cpDepartList)) {
            return;
        }

        Map<Long, DepartmentDto> deptMap = cpDepartList.stream()
                .collect(Collectors.toMap(DepartmentDto::getId, Function.identity()));

        List<CpDepartmentBak> deptBakList = new ArrayList<>();
        for (DepartmentDto deptDto : cpDepartList) {
            CpDepartmentBak deptBak = BeanUtil.copyProperties(deptDto, CpDepartmentBak.class);
            // 设置path、idPath
            setPath(deptDto, deptMap, deptBak, null);

            deptBakList.add(deptBak);
        }

        // 保存部门备份
        departmentBakService.saveBatch(deptBakList);
        log.info("备份部门成功{}条", deptBakList.size());
    }

    /**
     * 设置path、idPath
     */
    private void setPath(DepartmentDto wxCpDepart, Map<Long, DepartmentDto> deptMap, CpDepartmentBak deptBak,
                         CpDepartment dept) {
        StringBuilder path = new StringBuilder(wxCpDepart.getName());
        StringBuilder idPath = new StringBuilder(wxCpDepart.getId().toString());
        DepartmentDto current = wxCpDepart;
        while (ObjectUtil.isNotNull(current)) {
            // 遍历父级部门，拼接path、idPath
            Long parentId = current.getParentid();
            if (deptMap.containsKey(parentId)) {
                current = deptMap.get(parentId);
                if (ObjectUtil.isNotNull(current)) {
                    path.insert(0, current.getName() + "/");
                    idPath.insert(0, current.getId() + "/");
                }
            } else {
                break;
            }
        }
        if (deptBak != null) {
            deptBak.setPath(path + "/");
            deptBak.setIdPath(idPath + "/");
        }
        if (dept != null) {
            dept.setPath(path + "/");
            dept.setIdPath(idPath + "/");
        }
    }

    /**
     * 处理部门
     */
    private void departmentOperate(String corpId, String agentId) {
        // 获取未处理的数据
        LambdaQueryWrapper<CpDepartmentOperate> deptOpWrapper = Wrappers.lambdaQuery(CpDepartmentOperate.class);
        deptOpWrapper.eq(CpDepartmentOperate::getOperateStatus, OperateStatusEnum.UNTREATED.getCode());
        List<CpDepartmentOperate> deptOpList = departmentOperateService.list(deptOpWrapper);

        if (CollUtil.isEmpty(deptOpList)) {
            return;
        }

        // 查询部门
        List<CpDepartment> deptList = departmentService.list();

        // 同步部门至企微
        syncDeptToCpAsync(corpId, agentId, deptList, deptOpList);

        // 操作成功的插入标准表
        List<CpDepartmentOperate> successList = deptOpList.stream()
                .filter(item -> OperateStatusEnum.SUCCESS.getCode() == item.getOperateStatus())
                .collect(Collectors.toList());
        // 处理标准表
        processDeptStandard(successList, deptList);
        // 更新path
        processPath(successList, deptList);

        // 更新操作表
        processDeptOperate(deptOpList);

        long failCount = deptOpList.stream().filter(item -> OperateStatusEnum.FAIL.getCode() == item.getOperateStatus())
                .count();
        log.info("处理部门成功{}条，失败{}条", successList.size(), failCount);

    }

    /**
     * 处理path
     */
    private void processPath(List<CpDepartmentOperate> successList, List<CpDepartment> departmentList) {
        List<Long> deptIdList = successList.stream().map(CpDepartmentOperate::getId).collect(Collectors.toList());
        // 需要更新path的list
        List<CpDepartment> pathList = departmentList.stream()
                .filter(department -> deptIdList.contains(department.getId())).collect(Collectors.toList());

        Map<Long, CpDepartment> deptMap = departmentList.stream()
                .collect(Collectors.toMap(CpDepartment::getId, Function.identity()));
        for (CpDepartment department : pathList) {
            StringBuilder path = new StringBuilder(department.getName());
            StringBuilder idPath = new StringBuilder(department.getId().toString());
            CpDepartment current = department;
            while (ObjectUtil.isNotNull(current)) {
                Long parentId = current.getParentid();
                if (deptMap.containsKey(parentId)) {
                    current = deptMap.get(parentId);
                    if (ObjectUtil.isNotNull(current)) {
                        path.insert(0, current.getName() + "/");
                        idPath.insert(0, current.getId() + "/");
                    }
                } else {
                    break;
                }
            }
            department.setPath(path + "/");
            department.setIdPath(idPath + "/");
        }
        departmentService.updateBatchById(pathList);
    }

    /**
     * 同步部门至企微
     *
     * @param corpId      企业id
     * @param agentId     应用id
     * @param deptDtoList 企微部门列表
     * @param deptOpList  部门操作列表
     */
    public void syncDeptToCpAsync(String corpId, String agentId, List<CpDepartment> deptDtoList,
                                  List<CpDepartmentOperate> deptOpList) {
        Map<Long, CpDepartment> departMap = deptDtoList.stream()
                .collect(Collectors.toMap(CpDepartment::getId, Function.identity()));
        List<CpDepartmentOperate> allDeptList = BeanUtil.copyToList(deptDtoList, CpDepartmentOperate.class);

        // 操作数据设置为需要处理
        deptOpList.forEach(item -> item.setNeedToProcess(YnEnum.YES.getValue()));
        List<Long> deptOpIdList = deptOpList.stream().map(CpDepartmentOperate::getId).collect(Collectors.toList());
        // 合并全部部门
        allDeptList.forEach(item -> {
            if (!deptOpIdList.contains(item.getId())) {
                deptOpList.add(item);
            }
        });
        Map<Long, List<CpDepartmentOperate>> parentIdMap = deptOpList.stream()
                .collect(Collectors.groupingBy(CpDepartmentOperate::getParentid));

        // 获取1级部门
        // 多线程操作
        List<Future<Integer>> futureList = new ArrayList<>();
        List<CpDepartmentOperate> lvl1List = deptOpList.stream()
                .filter(item -> item.getParentid() == CommonConstant.ROOT_DEPT_ID).collect(Collectors.toList());
        // 经测试该接口企微只支持5个线程的并发 超过的话会报45033-接口并发调用超过限制
        // 拆分成每5个一组
        List<List<CpDepartmentOperate>> split = CollUtil.split(lvl1List, AddressbookConstant.PROCESS_THREAD_COUNT_5);
        for (List<CpDepartmentOperate> list : split) {
            for (CpDepartmentOperate operate : list) {
                List<CpDepartmentOperate> operateList = new ArrayList<>();

                // 添加子部门
                addDeptOpChildren(operate, parentIdMap, operateList);

                // 不存在需要处理的数据则跳过
                boolean needToProcess = operateList.stream()
                        .anyMatch(item -> YnEnum.YES.getValue() == item.getNeedToProcess());
                if (!needToProcess) {
                    continue;
                }

                // 同步部门至企微
                Future<Integer> future = departmentService.asyncDeptToCp(corpId, agentId, operateList, departMap);
                futureList.add(future);
            }

            // 校验全部任务执行完
            verifyTaskFinish(futureList);
        }
        log.info("同步部门至企微完成");

        // 移除不需处理的数据
        deptOpList.removeIf(item -> YnEnum.NO.getValue() == item.getNeedToProcess());
    }

    /**
     * 增加子部门
     */
    private static void addDeptOpChildren(CpDepartmentOperate operate, Map<Long, List<CpDepartmentOperate>> parentIdMap,
                                          List<CpDepartmentOperate> operateList) {
        operateList.add(operate);

        List<CpDepartmentOperate> operates = parentIdMap.get(operate.getId());
        if (CollUtil.isNotEmpty(operates)) {
            for (CpDepartmentOperate departmentOperate : operates) {
                addDeptOpChildren(departmentOperate, parentIdMap, operateList);
            }
        }
    }

    /**
     * 校验全部任务执行完（带超时控制）
     */
    private void verifyTaskFinishWithTimeout(List<Future<Integer>> futureList, String taskName) {
        if (CollUtil.isEmpty(futureList)) {
            return;
        }

        log.info("开始等待{}任务完成，任务数量: {}", taskName, futureList.size());

        for (int i = 0; i < futureList.size(); i++) {
            Future<Integer> future = futureList.get(i);
            try {
                // 为每个任务设置超时时间
                future.get(AddressbookConstant.CONCURRENT_TASK_TIMEOUT_MINUTES, TimeUnit.MINUTES);
            } catch (TimeoutException e) {
                log.error("{}任务超时，任务索引: {}", taskName, i, e);
                // 取消未完成的任务
                futureList.forEach(f -> f.cancel(true));
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException(taskName + "任务超时");
            } catch (InterruptedException e) {
                log.error("{}任务被中断，任务索引: {}", taskName, i, e);
                Thread.currentThread().interrupt();
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException(taskName + "任务被中断");
            } catch (ExecutionException e) {
                log.error("{}任务执行失败，任务索引: {}", taskName, i, e.getCause());
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param(taskName + "任务执行失败: " + e.getMessage()).toServiceException();
            }
        }

        log.info("{}任务全部完成", taskName);
    }

    /**
     * 校验全部任务执行完（原方法保持兼容）
     */
    private static void verifyTaskFinish(List<Future<Integer>> futureList) {
        futureList.forEach(future -> {
            try {
                future.get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            } catch (ExecutionException e) {
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param(e.getMessage()).toServiceException();
            }
        });
    }

    /**
     * 更新操作表
     *
     * @param deptOpList 部门操作列表
     */
    private void processDeptOperate(List<CpDepartmentOperate> deptOpList) {
        // 不为未处理的都更新
        List<CpDepartmentOperate> deptOpUpdateList = deptOpList.stream()
                .filter(item -> OperateStatusEnum.UNTREATED.getCode() != item.getOperateStatus())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(deptOpUpdateList)) {
            departmentOperateService.updateBatchById(deptOpUpdateList);
        }
    }

    /**
     * 处理标准表
     *
     * @param successList    处理成功列表
     * @param departmentList 部门列表
     */
    private void processDeptStandard(List<CpDepartmentOperate> successList, List<CpDepartment> departmentList) {
        if (CollUtil.isEmpty(successList)) {
            return;
        }

        Map<Long, CpDepartment> deptMap = departmentList.stream()
                .collect(Collectors.toMap(CpDepartment::getId, Function.identity()));

        List<CpDepartment> departmentSaveList = new ArrayList<>();
        List<CpDepartment> departmentUpdateList = new ArrayList<>();
        for (CpDepartmentOperate deptOp : successList) {
            CpDepartment department = deptMap.get(deptOp.getId());
            CpDepartment departmentParam = BeanUtil.copyProperties(deptOp, CpDepartment.class);

            // 不存在则新增
            if (department == null) {
                departmentSaveList.add(departmentParam);

                // 存在则更新
            } else {
                departmentUpdateList.add(departmentParam);
            }
        }
        // 插入标准表
        if (CollUtil.isNotEmpty(departmentSaveList)) {
            departmentService.saveBatch(departmentSaveList);

            // 全部列表添加新增数据
            departmentList.addAll(departmentSaveList);
        }
        // 更新标准表
        if (CollUtil.isNotEmpty(departmentUpdateList)) {
            departmentService.updateBatchById(departmentUpdateList);

            List<Long> updateIdList = departmentUpdateList.stream().map(CpDepartment::getId)
                    .collect(Collectors.toList());
            // 全部列表移除旧数据
            departmentList.removeIf(item -> updateIdList.contains(item.getId()));
            // 全部列表更新数据
            departmentList.addAll(departmentUpdateList);
        }
    }

    /**
     * 处理用户
     *
     * @param corpId   企业id
     * @param userid   用户id
     * @param toInvite 是否邀请
     */
    private void userOperate(String corpId, String userid, boolean toInvite) {
        // 获取未处理的数据
        LambdaQueryWrapper<CpUserOperate> userOpWrapper = Wrappers.lambdaQuery(CpUserOperate.class);
        userOpWrapper.eq(CpUserOperate::getOperateStatus, OperateStatusEnum.UNTREATED.getCode());
        // 指定用户id
        if (StringUtils.isNotBlank(userid)) {
            userOpWrapper.eq(CpUserOperate::getUserid, userid);
        }
        List<CpUserOperate> userOpList = userOperateService.list(userOpWrapper);
        if (CollUtil.isEmpty(userOpList)) {
            return;
        }

        List<String> userIdList = userOpList.stream().map(CpUserOperate::getUserid).collect(Collectors.toList());
        // 查询用户
        List<CpUser> userList;
        // 如果数量小于1000，则按条件查询
        if (userIdList.size() < AddressbookConstant.IN_QUERY_SIZE) {
            userList = userService.listByIds(userIdList);
        } else {
            userList = userService.list();
        }

        // 同步用户至企微
        syncUserToCp(userOpList, userList, corpId, toInvite);

        // 操作成功的插入标准表
        List<CpUserOperate> successList = userOpList.stream()
                .filter(item -> OperateStatusEnum.SUCCESS.getCode() == item.getOperateStatus())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(successList)) {
            // 处理标准表
            processUserStandard(successList, userList);

            // 处理用户部门关联
            processUserDeptRelation(successList);
        }

        // 处理操作表
        processUserOperate(userOpList);

        long failCount = userOpList.stream().filter(item -> OperateStatusEnum.FAIL.getCode() == item.getOperateStatus())
                .count();
        log.info("处理用户成功{}条，失败{}条", successList.size(), failCount);
    }

    /**
     * 处理操作表
     *
     * @param userOpList 用户操作表
     */
    private void processUserOperate(List<CpUserOperate> userOpList) {
        // 不为未处理的都更新
        List<CpUserOperate> userOpUpdateList = userOpList.stream()
                .filter(item -> OperateStatusEnum.UNTREATED.getCode() != item.getOperateStatus())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(userOpUpdateList)) {
            userOperateService.updateBatchById(userOpUpdateList);
        }
    }

    /**
     * 处理用户部门关联
     *
     * @param successList 处理成功列表
     */
    private void processUserDeptRelation(List<CpUserOperate> successList) {
        // 删除旧的用户部门关联
        List<String> userIds = successList.stream().map(CpUserOperate::getUserid).collect(Collectors.toList());
        LambdaQueryWrapper<CpUserDepartmentRelation> queryWrapper = Wrappers
                .lambdaQuery(CpUserDepartmentRelation.class);
        queryWrapper.in(CpUserDepartmentRelation::getUserid, userIds);
        userDepartmentRelationService.remove(queryWrapper);
        // 新增用户部门关联
        List<CpUserDepartmentRelation> userDeptRelationSaveList = new ArrayList<>();
        for (CpUserOperate userOperate : successList) {
            List<Long> department = userOperate.getDepartment();
            for (Long deptId : department) {
                CpUserDepartmentRelation userDeptRelation = new CpUserDepartmentRelation();
                userDeptRelation.setUserid(userOperate.getUserid());
                userDeptRelation.setDepartmentid(deptId);
                // 默认非部门负责人
                userDeptRelation.setIsLeader(YnEnum.NO.getValue());
                userDeptRelationSaveList.add(userDeptRelation);
            }
        }
        userDepartmentRelationService.saveBatch(userDeptRelationSaveList);
    }

    /**
     * 操作标准表
     *
     * @param successList 处理成功列表
     * @param userList    用户列表
     */
    private void processUserStandard(List<CpUserOperate> successList, List<CpUser> userList) {
        Map<String, CpUser> userMap = userList.stream()
                .collect(Collectors.toMap(CpUser::getUserid, Function.identity()));

        List<CpUser> userSaveList = new ArrayList<>();
        List<CpUser> userUpdateList = new ArrayList<>();

        // 处理用户是否部门负责人
        processUserIsLeader(successList, userMap, userSaveList, userUpdateList);

        // 插入标准表
        if (CollUtil.isNotEmpty(userSaveList)) {
            userService.saveBatch(userSaveList);
            // 插入用户敏感信息
            List<CpUserSensitiveInfo> sensitiveInfoList = BeanUtil.copyToList(userSaveList, CpUserSensitiveInfo.class);
            cpUserSensitiveInfoService.saveOrUpdateBatch(sensitiveInfoList);
        }
        // 更新标准表
        if (CollUtil.isNotEmpty(userUpdateList)) {
            userService.updateBatchById(userUpdateList);
        }
    }

    /**
     * 处理用户是否部门负责人
     */
    private void processUserIsLeader(List<CpUserOperate> successList, Map<String, CpUser> userMap,
                                     List<CpUser> userSaveList, List<CpUser> userUpdateList) {
        for (CpUserOperate userOp : successList) {
            CpUser userParam = JacksonUtils.toObj(JacksonUtils.toJson(userOp), CpUser.class);

            CpUser user = userMap.get(userOp.getUserid());
            // 不存在则新增
            if (user == null) {
                // 默认未激活
                userParam.setStatus(UserStatusEnum.INACTIVE.getCode());
                List<Integer> isLeaderInDept = new ArrayList<>();
                // 默认非部门负责人
                for (int i = 0; i < userOp.getDepartment().size(); i++) {
                    isLeaderInDept.add(YnEnum.NO.getValue());
                }
                userParam.setIsLeaderInDept(isLeaderInDept);
                userSaveList.add(userParam);

                // 存在则更新
            } else {

                // 旧用户部门信息
                List<Long> oldDept = user.getDepartment();
                List<Integer> oldIsLeaderInDept = user.getIsLeaderInDept();
                List<Integer> isLeaderInDept = new ArrayList<>();
                // 设置部门负责人
                for (Long deptId : user.getDepartment()) {
                    // 如果存在则照旧
                    if (oldDept.contains(deptId)) {
                        isLeaderInDept.add(oldIsLeaderInDept.get(oldDept.indexOf(deptId)));
                    } else {
                        // 如果不存在则默认非部门负责人
                        isLeaderInDept.add(YnEnum.NO.getValue());
                    }
                }
                userParam.setIsLeaderInDept(isLeaderInDept);
                userUpdateList.add(userParam);
            }
        }
    }

    /**
     * 同步至企微
     *
     * @param userOpList 用户操作表
     * @param userList   用户列表
     * @param corpId     企业id
     * @param toInvite   是否邀请
     */
    private void syncUserToCp(List<CpUserOperate> userOpList, List<CpUser> userList, String corpId, boolean toInvite) {
        Map<String, CpUser> userMap = userList.stream()
                .collect(Collectors.toMap(CpUser::getUserid, Function.identity()));

        List<Future<Integer>> futureList = new ArrayList<>();
        // 拆分多线程执行
        List<List<CpUserOperate>> split = ListUtil.splitAvg(userOpList, AddressbookConstant.PROCESS_THREAD_COUNT_16);
        for (List<CpUserOperate> userOperateList : split) {
            if (CollUtil.isEmpty(userOperateList)) {
                continue;
            }

            // 异步方式同步部门至企微
            Future<Integer> future = userService.asyncUserOperateToCp(corpId, userOperateList, userMap, toInvite);
            futureList.add(future);
        }

        // 校验全部任务执行完
        verifyTaskFinish(futureList);
        log.info("同步用户至企微完成");
    }

    /**
     * 处理标签
     *
     * @param corpId 企业id
     */
    private void tagOperate(String corpId) {
        // 获取未处理的数据
        LambdaQueryWrapper<CpTagOperate> tagOpWrapper = Wrappers.lambdaQuery(CpTagOperate.class);
        tagOpWrapper.eq(CpTagOperate::getOperateStatus, OperateStatusEnum.UNTREATED.getCode());
        List<CpTagOperate> tagOpList = tagOperateService.list(tagOpWrapper);

        if (CollUtil.isEmpty(tagOpList)) {
            return;
        }

        // 获取标签
        List<CpTag> tagList;
        // 如果数量小于1000，则按条件查询
        List<Long> tagidList = tagOpList.stream().map(CpTagOperate::getTagid).filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (tagidList.size() < AddressbookConstant.IN_QUERY_SIZE) {
            tagList = tagService.listByIds(tagidList);
        } else {
            tagList = tagService.list();
        }

        // 同步标签至企微
        syncTagToCp(corpId, tagList, tagOpList);

        // 操作成功的插入标准表
        List<CpTagOperate> successList = tagOpList.stream()
                .filter(item -> OperateStatusEnum.SUCCESS.getCode() == item.getOperateStatus())
                .collect(Collectors.toList());
        // 处理标准表
        processTagStandard(successList, tagList);

        // 处理操作表
        processTagOperate(tagOpList);

        long failCount = tagOpList.stream().filter(item -> OperateStatusEnum.FAIL.getCode() == item.getOperateStatus())
                .count();
        log.info("处理标签成功{}条，失败{}条", successList.size(), failCount);
    }

    /**
     * 处理标签操作表
     *
     * @param tagOpList 标签操作列表
     */
    private void processTagOperate(List<CpTagOperate> tagOpList) {
        // 不为未处理的都更新
        List<CpTagOperate> tagOpUpdateList = tagOpList.stream()
                .filter(item -> OperateStatusEnum.UNTREATED.getCode() != item.getOperateStatus())
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(tagOpUpdateList)) {
            tagOperateService.updateBatchById(tagOpUpdateList);
        }
    }

    /**
     * 处理标准表
     *
     * @param successList 处理成功列表
     * @param tagList     标签列表
     */
    private void processTagStandard(List<CpTagOperate> successList, List<CpTag> tagList) {
        if (CollUtil.isNotEmpty(successList)) {
            Map<Long, CpTag> deptMap = tagList.stream().collect(Collectors.toMap(CpTag::getTagid, Function.identity()));

            List<CpTag> tagSaveList = new ArrayList<>();
            List<CpTag> tagUpdateList = new ArrayList<>();
            for (CpTagOperate tagOp : successList) {
                CpTag tag = deptMap.get(tagOp.getTagid());
                CpTag tagParam = BeanUtil.copyProperties(tagOp, CpTag.class);

                // 不存在则新增
                if (tag == null) {
                    tagSaveList.add(tagParam);

                    // 存在则更新
                } else {
                    tagUpdateList.add(tagParam);
                }
            }
            // 插入标准表
            if (CollUtil.isNotEmpty(tagSaveList)) {
                tagService.saveBatch(tagSaveList);
            }
            // 更新标准表
            if (CollUtil.isNotEmpty(tagUpdateList)) {
                tagService.updateBatchById(tagUpdateList);
            }
        }
    }

    /**
     * 同步标签至企微
     *
     * @param corpId    应用id
     * @param tagList   标签列表
     * @param tagOpList 标签操作列表
     */
    private void syncTagToCp(String corpId, List<CpTag> tagList, List<CpTagOperate> tagOpList) {
        Map<Long, CpTag> tagMap = tagList.stream().collect(Collectors.toMap(CpTag::getTagid, Function.identity()));

        // 拆分多线程执行
        List<List<CpTagOperate>> split = ListUtil.splitAvg(tagOpList, AddressbookConstant.PROCESS_THREAD_COUNT_5);
        // 经测试该接口企微只支持5个线程的并发 超过的话会报45033-接口并发调用超过限制
        List<Future<Integer>> futureList = new ArrayList<>();
        for (List<CpTagOperate> tagOperateList : split) {
            if (CollUtil.isEmpty(tagOperateList)) {
                continue;
            }

            // 异步方式同步部门至企微
            Future<Integer> future = tagService.asyncTagToCp(corpId, tagOperateList, tagMap);
            futureList.add(future);
        }
        // 校验全部任务执行完
        verifyTaskFinish(futureList);

        log.info("同步标签至企微完成");
    }

}

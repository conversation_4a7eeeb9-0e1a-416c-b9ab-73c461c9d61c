package com.lanshan.base.commonservice.enums;

import lombok.Getter;

/**
 * 群聊消息类型枚举
 * <AUTHOR>
 */
@Getter
public enum GroupChatMessageTypeEnum {
    /**
     * 1:文本 2:图片 3:文件
     */
    TEXT(1, "文本"),
    IMAGE(2, "图片"),
    FILE(3, "文件");

    /**
     * 群聊消息类型CODE
     */
    private final int code;
    /**
     * 群聊消息类型msg
     */
    private final String msg;

    GroupChatMessageTypeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}

package com.lanshan.base.commonservice.addressbook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@ApiModel(value = "用户身份信息VO")
@Data
public class UserIdentityInfoVO implements Serializable {

    private static final long serialVersionUID = 7123517020062683106L;

    @ApiModelProperty(value = "用户绑定表主键")
    private Long userInfoBindId;

    @ApiModelProperty(value = "微信openid")
    private String openId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "身份证号")
    private String identityNumber;

    @ApiModelProperty(value = "工号")
    private String userId;

    @ApiModelProperty(value = "证件图片")
    private String certificateImg;

    @ApiModelProperty(value = "是否默认（切换身份后设置默认，下次进入默认显示此身份）")
    private Boolean isDefault;

    @ApiModelProperty(value = "认证状态")
    private Boolean authStatus;

    @ApiModelProperty(value = "开通状态")
    private Boolean openStatus;

    @ApiModelProperty(value = "手机号")
    private String phoneNo;

    @ApiModelProperty(value = "用户类型(0：其他人员 1：教职工 2：本科生 3：研究生)")
    private String userType;

    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "身份证号脱敏")
    private String identityNumberWrapper;

    @ApiModelProperty(value = "学院名称")
    private String collegeName;

    @ApiModelProperty(value = "专业名称")
    private String majorName;
}

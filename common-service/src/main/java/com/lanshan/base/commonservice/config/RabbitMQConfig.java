package com.lanshan.base.commonservice.config;

import com.lanshan.base.starter.rabbitmq.properties.CallbackMQProperties;
import org.springframework.amqp.core.*;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * RabbitMQ配置
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Configuration
@RefreshScope
public class RabbitMQConfig {

	//回调MQ配置
	@Resource
	private CallbackMQProperties callbackMQProperties;

	@Bean
	public Exchange callbackDirectExchange() {
		return ExchangeBuilder.directExchange(callbackMQProperties.getExchange()).build();
	}

	@Bean
	public Queue addressBookCallbackQueue() {
		return QueueBuilder.durable(callbackMQProperties.getAddressBook().getQueue()).build();
	}

	@Bean
	public Binding addressBookCallbackBinding(Queue addressBookCallbackQueue, Exchange callbackDirectExchange) {
		return BindingBuilder.bind(addressBookCallbackQueue).to(callbackDirectExchange).with(callbackMQProperties.getAddressBook().getRoutingKey()).noargs();
	}

}

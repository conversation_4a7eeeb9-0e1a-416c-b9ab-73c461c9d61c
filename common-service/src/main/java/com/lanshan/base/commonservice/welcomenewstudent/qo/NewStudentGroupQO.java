package com.lanshan.base.commonservice.welcomenewstudent.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 新生群聊查询参数
 */
@ApiModel(value = "新生群聊查询参数")
@EqualsAndHashCode(callSuper = true)
@Data
public class NewStudentGroupQO extends PageQo {

    @ApiModelProperty(value = "群名称")
    private String groupName;

    @ApiModelProperty(value = "群主")
    private String ownerName;

    @ApiModelProperty(value = "用户id")
    private String ownerUserid;

    @ApiModelProperty(value = "部门名称")
    private String deptName;
}

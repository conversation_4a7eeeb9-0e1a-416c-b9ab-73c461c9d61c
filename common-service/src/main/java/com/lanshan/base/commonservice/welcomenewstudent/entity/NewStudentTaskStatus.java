package com.lanshan.base.commonservice.welcomenewstudent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;


/**
 * 新生任务状态表(NewStudentTaskStatus)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class NewStudentTaskStatus extends Model<NewStudentTaskStatus> {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 身份证号
     */
    private String userid;
    /**
     * 新生任务id
     */
    private Long newStuTaskId;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}


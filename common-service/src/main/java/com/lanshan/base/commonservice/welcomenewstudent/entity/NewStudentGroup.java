package com.lanshan.base.commonservice.welcomenewstudent.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.lanshan.base.commonservice.typehandler.JsonbTypeHandler;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 新生群表(NewStudentGroup)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class NewStudentGroup extends Model<NewStudentGroup> {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 群聊id
     */
    private String chatId;
    /**
     * 院系代码
     */
    private String groupCode;
    /**
     * 群名称
     */
    private String groupName;
    /**
     * 群主id
     */
    private String ownerUserid;
    /**
     * 群主名称
     */
    private String ownerName;
    /**
     * 加入群的用户列表
     */
    @TableField(typeHandler = JsonbTypeHandler.class)
    private List<String> joinInUser;
    /**
     * 入学年份
     */
    private String year;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 最后更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateDate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}


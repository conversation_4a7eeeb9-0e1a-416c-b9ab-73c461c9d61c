package com.lanshan.base.commonservice.group.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @program: wx-demo
 * @Description:
 * @author: JZ
 * @createTime: 2025-05-21 10:54
 */
@Data
public class UpdateGroupOwnerDto {
    /**
     * 群聊id
     */
    @NotEmpty(message = "群聊id不能为空")
    private String chatId;

    /**
     * agentId应用ID
     */
    private String agentId;

    /**
     * 群主ID
     */
    private String groupOwnerNo;
    /**
     * 群主名称
     */
    private String groupOwner;
}

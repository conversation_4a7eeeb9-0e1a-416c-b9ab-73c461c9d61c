package com.lanshan.base.commonservice.schooldata.northwestpolitics.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.lanshan.base.commonservice.common.annotation.ExcelHeader;
import lombok.Data;

import java.io.Serializable;

/**
 * 硕士研究生学籍信息
 * <AUTHOR> yang.
 * @since 2025-05-26
 */

@Data
public class MasterStatusInfoDTO implements Serializable {


    private static final long serialVersionUID = 439985524748850896L;
    //学号

    @ExcelHeader(title = "学号")
    private String xh;

    //学生类别
    @ExcelHeader(title = "学生类别")
    private String xslb;

    //是否在校
    @ExcelHeader(title = "是否在校")
    private String sfzx;



}

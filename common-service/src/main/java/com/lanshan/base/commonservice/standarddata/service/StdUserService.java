package com.lanshan.base.commonservice.standarddata.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.api.page.PageResult;
import com.lanshan.base.commonservice.standarddata.dto.StdUserPageDTO;
import com.lanshan.base.commonservice.standarddata.po.StdUser;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.standarddata.vo.StdUserVO;

/**
 * <p>
 * 用户信息标准表 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-06-04
 */
public interface StdUserService extends IService<StdUser> {

    /**
     * 用户分页列表
     * <AUTHOR> yang.
     * @since 2025/6/4 10:43
     */
    Page<StdUserVO> getUserPageList(StdUserPageDTO dto);
}

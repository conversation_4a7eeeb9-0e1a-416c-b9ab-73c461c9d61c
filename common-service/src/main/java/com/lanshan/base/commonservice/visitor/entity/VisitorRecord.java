package com.lanshan.base.commonservice.visitor.entity;

import java.util.Date;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 访客记录表(VisitorRecord)实体
 */
@Data
public class VisitorRecord implements Serializable {
    private static final long serialVersionUID = -86806125841884230L;

    @ApiModelProperty("主键")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("访客姓名")
    private String name;

    @ApiModelProperty("手机号码")
    private String mobile;

    @ApiModelProperty("证件类型 1：身份证 2：护照")
    private Integer certificateType;

    @ApiModelProperty("证件号码")
    private String certificateNo;

    @ApiModelProperty("进校方式 1：驾车 2：步行")
    private Integer enterType;

    @ApiModelProperty("车牌号")
    private String plateNo;

    @ApiModelProperty("事由")
    private String reason;

    @ApiModelProperty("类型 1：预约 2：邀约")
    private Integer type;

    @ApiModelProperty("状态 0：待审批 1：已通过 2：已拒绝 3：已取消 4：已失效")
    private Integer status;

    @ApiModelProperty("被访问人姓名")
    private String intervieweeName;

    @ApiModelProperty("被访问人手机号")
    private String intervieweeMobile;

    @ApiModelProperty("被访问人学工号")
    private String intervieweeUserid;

    @ApiModelProperty("拜访校区")
    private String campus;

    @ApiModelProperty("拜访单位id")
    private Long deptId;

    @ApiModelProperty("拜访单位名称")
    private String deptName;

    @ApiModelProperty("来访时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("随行人名称")
    private String entourageName;

    @ApiModelProperty("随行人数")
    private Integer entourageCount;

    @ApiModelProperty("审核人学工号")
    private String auditorUserid;

    @ApiModelProperty("审核人姓名")
    private String auditorName;

    @ApiModelProperty("创建时间")
    private Date createDate;
}


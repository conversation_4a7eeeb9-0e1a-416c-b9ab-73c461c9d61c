package com.lanshan.base.commonservice.task;

import cn.hutool.core.collection.CollectionUtil;
import com.lanshan.base.api.dto.message.TextMsgBody;
import com.lanshan.base.api.feign.message.MessageFeign;
import com.lanshan.base.commonservice.addressbook.entity.UserRemoveQueue;
import com.lanshan.base.commonservice.addressbook.service.UserRemoveQueueService;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.constant.BatchUserConstant;
import com.lanshan.base.commonservice.task.batch.dto.JobExecCompDto;
import com.lanshan.base.commonservice.task.batch.enums.BatchTypeEnum;
import com.lanshan.base.commonservice.task.batch.service.BatchJobExecService;
import com.lanshan.base.commonservice.task.batch.vo.JobExecVo;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: capability-platform-base
 * @Description:
 * @author: JZ
 * @createTime: 2024-12-10 19:25
 */
@Slf4j
@Component
public class BatchUserTask {

    @Autowired
    private UserService userService;

    @Autowired
    BatchJobExecService batchJobExecService;

    @Autowired
    private UserRemoveQueueService userRemoveQueueService;
    @Value("${qwzs.corpId:}")
    private String removeCorpId;
    @Value("${qwzs.agentId:}")
    private String removeAgentId;
    @Autowired
    private MessageFeign messageFeign;


    @XxlJob("batchDelUserInfo")
    public void batchDelUserInfo() {
        log.info("batchDelUserInfo-------------------------------------");
        List<UserRemoveQueue> removeUsers = userRemoveQueueService.getDeleteUser();
        if (CollectionUtil.isEmpty(removeUsers)) {
            log.info("batchDelUser 无需处理-------------------------------------");
            return;
        }
        log.info("list :{} ", removeUsers);

        List<String> removeUserIds = removeUsers.stream()
                .map(UserRemoveQueue::getUserId)
                .collect(Collectors.toList());
        try {
            userService.batchDelUserV2(removeUserIds);
            userRemoveQueueService.updateQueueStatus(removeUserIds);
            for (UserRemoveQueue queue : removeUsers) {
//                系统检测到您的状态为[毕业/离职/退休]，将于2025年10月10日[移除企业微信/迁移到 校友/计算机2401班]
                StringBuilder sb = new StringBuilder("系统检测到您的状态为[");
                sb.append(dealAutoType(queue.getAutoType()));
                sb.append("]，将于");
                sb.append(queue.getDealTime());
                sb.append("[");
                sb.append(queue.getDealDetail());
                sb.append("]");
                TextMsgBody textMsgBody = new TextMsgBody();
                textMsgBody.setMsgType("text");
                textMsgBody.setContent(sb.toString());
                textMsgBody.setToUser(queue.getUserId());
                textMsgBody.setAgentId(Integer.valueOf(removeAgentId));
                try {
                    messageFeign.sendText(removeCorpId, removeAgentId, textMsgBody);
                } catch (Exception e) {
                    log.info("发送企业微信消息失败，corpId:{},agentId:{},msgBody:{}", removeCorpId, removeAgentId, textMsgBody);
                    log.error("发送企业微信消息失败", e);
                }
            }
        } catch (Exception e) {
            log.error("批量删除用户失败 ：", e);
        }

    }

    private String dealAutoType(Integer autoType) {
        if (autoType == 1) {
            return "毕业";
        } else if (autoType == 2) {
            return "离职";
        } else {
            return "退休";
        }
    }

    @XxlJob("userBatchJobWriteBack")
    public void userBatchJobWriteBack() {
        JobExecCompDto jobExecCompDto = new JobExecCompDto();
        jobExecCompDto.setExecBeanName(BatchTypeEnum.QW_BATCH_REMOVE_USER.getBatchExecutorName());
        jobExecCompDto.setExecStatus(BatchUserConstant.BATCH_TWO);
        jobExecCompDto.setBusinessStatus(BatchUserConstant.BATCH_ZERO);
        List<JobExecVo> jobExecVos = batchJobExecService.selectJobComp(jobExecCompDto);
        jobExecVos.forEach(item -> {
            switch (Objects.requireNonNull(BatchTypeEnum.getBatchTypeEnum(item.getBatchType()))) {
                case QW_BATCH_REMOVE_USER:
                    deleteUser(item);
                    break;
                case QW_BATCH_UPDATE_USER:
                    updateUser(item);
                    break;
                default:
                    return;
            }

        });
    }

    private void updateUser(JobExecVo item) {
        try {
            userService.updateUser(item);
        } catch (Exception e) {
            log.error("执行jobexec完成后更新数据异常:" + item.getId(), e);
        }
    }

    private void deleteUser(JobExecVo item) {
        try {
            userService.deleteUser(item);
        } catch (Exception e) {
            log.error("执行jobexec完成后更新数据异常:" + item.getId(), e);
        }
    }
}

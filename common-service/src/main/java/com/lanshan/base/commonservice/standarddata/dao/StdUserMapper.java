package com.lanshan.base.commonservice.standarddata.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.commonservice.standarddata.dto.StdUserPageDTO;
import com.lanshan.base.commonservice.standarddata.po.StdUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.standarddata.vo.StdUserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户信息标准表 Mapper 接口
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-06-04
 */
@Mapper
public interface StdUserMapper extends BaseMapper<StdUser> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<StdUser> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<StdUser> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<StdUser> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<StdUser> entities);

    List<StdUserVO> getUserPageList(Page<?> page, @Param("dto") StdUserPageDTO dto);
}

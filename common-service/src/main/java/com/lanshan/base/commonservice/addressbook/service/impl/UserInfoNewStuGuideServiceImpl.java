package com.lanshan.base.commonservice.addressbook.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.addressbook.converter.UserInfoNewStuGuideConverter;
import com.lanshan.base.commonservice.addressbook.dao.UserInfoNewStuGuideDao;
import com.lanshan.base.commonservice.addressbook.entity.UserInfoNewStuGuide;
import com.lanshan.base.commonservice.addressbook.qo.UserInfoNewStuGuideQO;
import com.lanshan.base.commonservice.addressbook.service.UserInfoNewStuGuideService;
import com.lanshan.base.commonservice.addressbook.vo.UserInfoNewStuGuideVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 新生指南表(UserInfoNewStuGuide)表服务实现类
 *
 * <AUTHOR>
 */
@Service("userInfoNewStuGuideService")
public class UserInfoNewStuGuideServiceImpl extends ServiceImpl<UserInfoNewStuGuideDao, UserInfoNewStuGuide> implements UserInfoNewStuGuideService {

    @Override
    public IPage<UserInfoNewStuGuideVO> pageByParam(UserInfoNewStuGuideQO qo) {
        return this.baseMapper.pageByParam(Page.<UserInfoNewStuGuide>of(qo.getPage(), qo.getSize()), qo);
    }

    @Override
    public List<UserInfoNewStuGuideVO> listAll() {
        List<UserInfoNewStuGuide> userInfoNewStuGuides = this.list(Wrappers.lambdaQuery(UserInfoNewStuGuide.class)
                .eq(UserInfoNewStuGuide::getStatus, true)
                .orderByAsc(UserInfoNewStuGuide::getSort)
                .orderByDesc(UserInfoNewStuGuide::getUpdateDate)
        );
        return UserInfoNewStuGuideConverter.INSTANCE.toVO(userInfoNewStuGuides);
    }

    @Override
    public Integer getMaxSort() {
        UserInfoNewStuGuide one = this.getOne(Wrappers.lambdaQuery(UserInfoNewStuGuide.class).orderByDesc(UserInfoNewStuGuide::getSort).last("limit 1"));
        return one == null ? 0 : one.getSort();
    }
}


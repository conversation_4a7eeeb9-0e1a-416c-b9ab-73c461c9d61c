package com.lanshan.base.commonservice.welcomenewstudent.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 新生欢迎配置类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Configuration
@EnableAsync
public class WelcomeNewStudentConfig {

    /**
     * 新生任务专用线程池
     * 使用专用线程池，避免使用默认的 ForkJoinPool
     */
    @Bean("newStudentTaskExecutor")
    public Executor newStudentTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数
        executor.setCorePoolSize(5);

        // 最大线程数
        executor.setMaxPoolSize(20);

        // 队列容量
        executor.setQueueCapacity(100);

        // 线程名前缀
        executor.setThreadNamePrefix("NewStudent-Task-");

        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);

        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间
        executor.setAwaitTerminationSeconds(30);

        executor.initialize();

        log.info("新生任务线程池初始化完成，核心线程数：{}，最大线程数：{}，队列容量：{}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());

        return executor;
    }

    /**
     * 企微API调用专用线程池
     * 分离企微API调用，避免阻塞主业务线程
     */
    @Bean("wxApiTaskExecutor")
    public Executor wxApiTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数 - 企微API调用相对较慢，使用较少的线程
        executor.setCorePoolSize(3);

        // 最大线程数
        executor.setMaxPoolSize(10);

        // 队列容量
        executor.setQueueCapacity(50);

        // 线程名前缀
        executor.setThreadNamePrefix("WxApi-Task-");

        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(120);

        // 拒绝策略：抛出异常
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间
        executor.setAwaitTerminationSeconds(60);

        executor.initialize();

        log.info("企微API线程池初始化完成，核心线程数：{}，最大线程数：{}，队列容量：{}",
                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());

        return executor;
    }
}

package com.lanshan.base.commonservice.addressbook.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.addressbook.entity.CpUserImportTagLogDetail;
import com.lanshan.base.commonservice.addressbook.qo.CpUserImportTagLogDetailQO;
import com.lanshan.base.commonservice.addressbook.vo.CpUserImportTagLogDetailVO;

/**
 * 企业微信用户导入标签日志详情服务接口
 */
public interface CpUserImportTagLogDetailService extends IService<CpUserImportTagLogDetail> {

    /**
     * 分页查询
     * @param page 分页对象
     * @param qo 查询对象
     * @return 分页结果
     */
    IPage<CpUserImportTagLogDetailVO> page(IPage<CpUserImportTagLogDetail> page, CpUserImportTagLogDetailQO qo);

    /**
     * 新增
     * @param qo 查询对象
     * @return 是否成功
     */
    boolean add(CpUserImportTagLogDetailQO qo);

    /**
     * 修改
     * @param qo 查询对象
     * @return 是否成功
     */
    boolean update(CpUserImportTagLogDetailQO qo);

    /**
     * 获取详情
     * @param id ID
     * @return 详情
     */
    CpUserImportTagLogDetailVO getDetail(Long id);
}
package com.lanshan.base.commonservice.standarddata.vo;

import com.lanshan.base.commonservice.standarddata.po.StdDepartment;
import com.lanshan.base.commonservice.standarddata.po.StdUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * TODO
 * <AUTHOR> yang.
 * @since  2022/12/20 10:49
 * @version  1.0
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class StdUserVO extends StdUser {
    private static final long serialVersionUID = 5392158685037702132L;

    private List<StdDepartment>  departmentVoList;

    private String mainDepartmentName;

    private String mainPath;
}

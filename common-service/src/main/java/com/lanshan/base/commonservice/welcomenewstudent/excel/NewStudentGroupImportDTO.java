package com.lanshan.base.commonservice.welcomenewstudent.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 群聊导入对象
 */
@Data
public class NewStudentGroupImportDTO {

    @ExcelProperty(value = "群编码")
    private String groupCode;

    @ExcelProperty(value = "群名称")
    private String groupName;

    @ExcelProperty(value = "群主姓名")
    private String ownerName;

    @ExcelProperty(value = "群主学工号")
    private String ownerUserid;

    @ExcelProperty(value = "成员1姓名")
    private String customOneName;

    @ExcelProperty(value = "成员1学工号")
    private String customOneUserid;

    @ExcelProperty(value = "成员2姓名")
    private String customTwoName;

    @ExcelProperty(value = "成员2学工号")
    private String customTwoUserid;

    @ExcelProperty(value = "入学年份")
    private String year;

    @ExcelProperty(value = "备注")
    private String remark;

}

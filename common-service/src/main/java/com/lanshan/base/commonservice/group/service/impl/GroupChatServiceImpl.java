package com.lanshan.base.commonservice.group.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.util.StringUtil;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.enums.MsgTypeEnum;
import com.lanshan.base.api.enums.SysUserChargeDeptDataScopeEnum;
import com.lanshan.base.api.enums.YnEnum;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.qo.user.UserInfoQo;
import com.lanshan.base.api.utils.system.SecurityUtils;
import com.lanshan.base.api.vo.user.UserInfoVo;
import com.lanshan.base.commonservice.addressbook.entity.CpDepartment;
import com.lanshan.base.commonservice.addressbook.entity.CpTag;
import com.lanshan.base.commonservice.addressbook.entity.CpUserDepartmentRelation;
import com.lanshan.base.commonservice.addressbook.entity.CpUserTagRelation;
import com.lanshan.base.commonservice.addressbook.service.*;
import com.lanshan.base.commonservice.enums.GroupChatMessageTypeEnum;
import com.lanshan.base.commonservice.group.constant.GroupConstant;
import com.lanshan.base.commonservice.group.converter.GroupChatConverter;
import com.lanshan.base.commonservice.group.dao.GroupChatDao;
import com.lanshan.base.commonservice.group.entity.GroupChat;
import com.lanshan.base.commonservice.group.entity.GroupChatOperation;
import com.lanshan.base.commonservice.group.entity.GroupChatScope;
import com.lanshan.base.commonservice.group.entity.GroupChatUser;
import com.lanshan.base.commonservice.group.qo.*;
import com.lanshan.base.commonservice.group.service.GroupChatOperationService;
import com.lanshan.base.commonservice.group.service.GroupChatScopeService;
import com.lanshan.base.commonservice.group.service.GroupChatService;
import com.lanshan.base.commonservice.group.service.GroupChatUserService;
import com.lanshan.base.commonservice.group.vo.*;
import com.lanshan.base.commonservice.system.entity.SysRole;
import com.lanshan.base.commonservice.system.service.ISysUserService;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpChatService;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpChat;
import me.chanjar.weixin.cp.bean.message.WxCpAppChatMessage;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 群聊信息表(GroupChat)表服务实现类
 *
 * <AUTHOR>
 */
@Service("groupChatService")
public class GroupChatServiceImpl extends ServiceImpl<GroupChatDao, GroupChat> implements GroupChatService {

    @Resource
    private WxCpServiceFactory wxCpServiceFactory;

    @Resource
    private UserService userService;
    @Resource
    private GroupChatScopeService groupChatScopeService;
    @Resource
    private GroupChatUserService groupChatUserService;
    @Resource
    private UserTagRelationService userTagRelationService;
    @Resource
    private TagService tagService;
    @Resource
    private UserDepartmentRelationService userDepartmentRelationService;
    @Resource
    private DepartmentService departmentService;
    @Resource
    private GroupChatOperationService groupChatOperationService;
    @Resource
    private ISysUserService sysUserService;

    /**
     * 企业ID
     */
    @Value("${common.agent.config.corp-id}")
    private String corpId;

    /**
     * 分页查询群聊
     *
     * @param qo 群聊分页查询条件
     * @return 分页结果
     */
    @Override
    public IPage<GroupChatVO> pageGroupChat(GroupChatSearchQo qo) {
        Page<GroupChat> page = new Page<>(qo.getPage(), qo.getSize());
        LambdaQueryWrapper<GroupChat> queryWrapper = Wrappers.lambdaQuery(GroupChat.class);
        List<SysRole> userRoles = sysUserService.getUserRoles(SecurityUtils.getUserId());
        Set<Integer> userQwDataScope = userRoles.stream().map(SysRole::getWxDataScope).collect(Collectors.toSet());
        // 群聊名称搜索
        if (StringUtil.isNotEmpty(qo.getName())) {
            queryWrapper.like(GroupChat::getName, qo.getName());
        }
        // 群聊类型
        if (qo.getType() != null) {
            queryWrapper.eq(GroupChat::getType, qo.getType());
        }
        // 群聊状态
        if (qo.getStatus() != null) {
            queryWrapper.eq(GroupChat::getStatus, qo.getStatus());
        }
        // 群主名称或ID搜索
        if (StringUtil.isNotEmpty(qo.getOwner())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(GroupChat::getOwnerName, qo.getOwner())
                    .or()
                    .like(GroupChat::getOwner, qo.getOwner()));
        }
        //如果通讯录权限是全部则查询所有，否则只查自己
        if (!userQwDataScope.contains(SysUserChargeDeptDataScopeEnum.ALL.getCode())) {
            queryWrapper.eq(GroupChat::getCreator, SecurityUtils.getUserId());
        }
        queryWrapper.orderByDesc(GroupChat::getId);
        // 获取群聊列表
        IPage<GroupChat> pageData = this.page(page, queryWrapper);
        IPage<GroupChatVO> chatPage = new Page<>();
        if (CollUtil.isNotEmpty(pageData.getRecords())) {
            chatPage = pageData.convert(GroupChatConverter.INSTANCE::toVO);
            this.assemblyMemberCount(chatPage.getRecords());
        }
        return chatPage;
    }

    /**
     * 通过部门-标签-用户列表查询群聊是否存在重复的成员
     *
     * @param qo 部门-标签-用户列表
     * @return 群聊列表
     */
    @Override
    public ExistGroupChatVo listExistGroupChat(ListExistGroupChatQo qo) {
        ExistGroupChatVo existGroupChatVo = new ExistGroupChatVo();
        // 判断是否选择群聊接收范围
        if (CollUtil.isEmpty(qo.getDepartmentList()) &&
                CollUtil.isEmpty(qo.getTagList()) &&
                CollUtil.isEmpty(qo.getUserList())) {
            throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_NOT_CHOICE_SCOPE);
        }

        // 根据成员、部门、标签id信息获取有效成员信息列表
        Set<UserInfoVo> userInfoList = this.getUserInfoVos(qo.getDepartmentList(), qo.getTagList(), qo.getUserList());
        List<String> userIds = userInfoList.stream().map(UserInfoVo::getUserid).collect(Collectors.toList());
        existGroupChatVo.setCount(userIds.size());

        Collections.sort(userIds);
        String memberIdEncrypt = DigestUtil.md5Hex(userIds.toString(), StandardCharsets.UTF_8);
        List<GroupChat> groupChats = this.list(new LambdaQueryWrapper<GroupChat>().eq(GroupChat::getMemberIdEncrypt, memberIdEncrypt));
        if (CollUtil.isEmpty(groupChats)) {
            return existGroupChatVo;
        }
        // 填充群聊成员数量
        List<GroupChatVO> groupChatVos = GroupChatConverter.INSTANCE.toVO(groupChats);
        this.assemblyMemberCount(groupChatVos);
        existGroupChatVo.setGroupChatVoList(groupChatVos);
        return existGroupChatVo;
    }

    /**
     * 组装群聊成员数量
     *
     * @param groupChats 成员数量组装对象
     */
    private void assemblyMemberCount(List<GroupChatVO> groupChats) {
        List<Long> ids = groupChats.stream().map(GroupChatVO::getId).collect(Collectors.toList());
        // 根据群聊ID查询群聊成员数量
        List<GroupChatMemberNumVo> memberNumVos = groupChatUserService.listGroupChatMemberNum(ids);
        Map<Long, Integer> memberNumMap;
        if (CollUtil.isNotEmpty(memberNumVos)) {
            memberNumMap = memberNumVos.stream().collect(Collectors.toMap(GroupChatMemberNumVo::getId, GroupChatMemberNumVo::getNum));
        } else {
            memberNumMap = new HashMap<>(8);
        }
        for (GroupChatVO item : groupChats) {
            Integer num = memberNumMap.getOrDefault(item.getId(), null);
            if (num != null) {
                item.setMemberSize(num);
            } else {
                item.setMemberSize(0);
            }
        }
    }

    /**
     * 新建群聊
     *
     * @param vo 群聊信息
     * @throws WxErrorException 企微异常
     */
    @Override
    public String createGroup(ChatInfo vo) throws WxErrorException {
        GroupChat chat = vo.getChat();
        String owner = chat.getOwner();

        List<GroupChatScope> departmentList = vo.getDepartmentList();
        List<GroupChatScope> tagList = vo.getTagList();
        List<GroupChatScope> userList = vo.getUserList();
        // 判断是否选择群聊接收范围
        if (CollUtil.isEmpty(departmentList) &&
                CollUtil.isEmpty(tagList) &&
                CollUtil.isEmpty(userList)) {
            throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_NOT_CHOICE_SCOPE);
        }

        // 查询对应的群成员信息,并且校验群聊的建群规则
        Set<UserInfoVo> userInfoList = this.getUserInfoVos(owner, departmentList, tagList, userList);

        // 判断群主是否在成员中 群主必须是群成员之一
        List<String> userIds = userInfoList.stream().map(UserInfoVo::getUserid).collect(Collectors.toList());
        if (CharSequenceUtil.equals(GroupConstant.GROUP_CHAT_IS_OWNER, String.valueOf(chat.getOwnerType())) && !userIds.contains(owner)) {
            throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_OWNER_NOT_MEMBER);
        }

        // 加密群成员id列表
        Collections.sort(userIds);
        String memberIdEncrypt = DigestUtil.md5Hex(userIds.toString(), StandardCharsets.UTF_8);
        chat.setMemberIdEncrypt(memberIdEncrypt);
        // 开始创建群聊
        List<GroupChatScope> allChatScopeList = new ArrayList<>();
        this.addIfNotNull(allChatScopeList, departmentList, tagList, userList);
        return chatCreate(vo.getAgentId(), chat, userIds, allChatScopeList, userInfoList);
    }

    /**
     * 获取群成员信息
     *
     * @param owner          群主ID
     * @param departmentList 部门列表
     * @param tagList        标签列表
     * @param userList       用户列表
     * @return 群成员信息
     */
    @NotNull
    private Set<UserInfoVo> getUserInfoVos(String owner, List<GroupChatScope> departmentList, List<GroupChatScope> tagList, List<GroupChatScope> userList) {
        UserInfoQo userInfoQo = listGroupChatScope(departmentList, tagList, userList);
        // 将群主id添加到成员id列表中
        if (StringUtil.isNotEmpty(owner)) {
            if (CollUtil.isEmpty(userInfoQo.getUseridList())) {
                userInfoQo.setUseridList(Collections.singletonList(owner));
            } else {
                userInfoQo.getUseridList().add(owner);
            }
        }
        // 根据成员、部门、标签id信息获取有效成员信息列表
        Set<UserInfoVo> userInfoList = userService.getUsersScope(userInfoQo);
        if (CollUtil.isEmpty(userInfoList)) {
            // 企微没有找到成员信息
            throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_NOT_FIND_MEMBER);
        }

        // 实际有效的群成员id列表。至少2人，至多2000人
        if (userInfoList.size() < GroupConstant.MIN_MEMBER_SIZE || userInfoList.size() > GroupConstant.MAX_MEMBER_SIZE) {
            throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_MEMBER_SIZE_ERROR);
        }
        return userInfoList;
    }


    /**
     * 获取群成员信息
     *
     * @param departmentList 部门列表
     * @param tagList        标签列表
     * @param userList       用户列表
     * @return 群成员信息
     */
    @NotNull
    private Set<UserInfoVo> getUserInfoVos(List<GroupChatScope> departmentList, List<GroupChatScope> tagList, List<GroupChatScope> userList) {
        UserInfoQo userInfoQo = listGroupChatScope(departmentList, tagList, userList);
        // 根据成员、部门、标签id信息获取有效成员信息列表
        Set<UserInfoVo> userInfoList = userService.getUsersScope(userInfoQo);
        if (CollUtil.isNotEmpty(userInfoList)) {
            return userInfoList;
        }
        return Collections.emptySet();
    }

    /**
     * 填充群聊接收范围
     *
     * @param departmentList
     * @param tagList
     * @param userList
     * @return
     */
    private UserInfoQo listGroupChatScope(List<GroupChatScope> departmentList, List<GroupChatScope> tagList, List<GroupChatScope> userList) {
        UserInfoQo userInfoQo = new UserInfoQo();
        userInfoQo.setUseridList(userList.stream().map(GroupChatScope::getDataId).collect(Collectors.toList()));
        userInfoQo.setDepartmentidList(departmentList.stream().map(obj -> Long.parseLong(obj.getDataId())).collect(Collectors.toList()));
        userInfoQo.setTagidList(tagList.stream().map(obj -> Long.parseLong(obj.getDataId())).collect(Collectors.toList()));
        return userInfoQo;
    }


    /**
     * 将群聊数据范围列表添加到总列表中
     *
     * @param list 总列表
     * @param args 列表
     */
    @SafeVarargs
    public final void addIfNotNull(List<? super GroupChatScope> list, List<GroupChatScope>... args) {
        for (List<GroupChatScope> sublist : args) {
            if (sublist != null && !sublist.isEmpty()) {
                list.addAll(sublist);
            }
        }
    }


    /**
     * 创建群聊
     *
     * @param agentId          应用ID
     * @param chat             群聊信息
     * @param userIds          用户成员id列表
     * @param allChatScopeList 群聊数据范围列表
     * @param userInfoList     群聊成员列表
     */
    private String chatCreate(String agentId,
                            GroupChat chat,
                            List<String> userIds,
                            List<GroupChatScope> allChatScopeList,
                            Set<UserInfoVo> userInfoList) throws WxErrorException {
        WxCpChatService wxCpChatService = wxCpServiceFactory.get(corpId, agentId).getChatService();
        // 创建群聊(群聊chatId不传由企微来自动生成)
        String chatId = wxCpChatService.create(chat.getName(), chat.getOwner(), userIds, null);
        if (StringUtil.isEmpty(chatId)) {
            throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_CREAT_FAIL);
        }
        // 非指定群主,需要再去获取群主信息
        if (StringUtils.isEmpty(chat.getOwner())) {
            WxCpChat wxCpChat = wxCpChatService.get(chatId);
            this.getOwnerInfo(chat, wxCpChat, userInfoList);
        }

        chat.setChatid(chatId);
        // 前端不传默认群聊类型为普通群聊
        if (chat.getType() == null) {
            chat.setType(GroupConstant.GROUP_CHAT_NORMAL);
        }

        // 判断是否发送群聊消息
        if (StringUtil.isNotEmpty(chat.getMessage())) {
            WxCpAppChatMessage wxCpAppChatMessage = new WxCpAppChatMessage();
            wxCpAppChatMessage.setChatId(chatId);
            wxCpAppChatMessage.setMsgType(MsgTypeEnum.TEXT.value());
            wxCpAppChatMessage.setContent(chat.getMessage());
            wxCpChatService.sendMsg(wxCpAppChatMessage);
            chat.setStatus(1);
        }

        //如果不为普通群，则不新建群聊信息表
        if (!chat.getType().equals(GroupConstant.GROUP_CHAT_NORMAL)) {
            return chatId;
        }

        chat.setCreator(String.valueOf(SecurityUtils.getUserId()));
        chat.setCreatorName(SecurityUtils.getUsername());
        chat.setCreateDate(new Date());
        this.save(chat);

        // 填充建群数据范围的数据
        if (CollUtil.isNotEmpty(allChatScopeList)) {
            saveGroupChatScope(allChatScopeList, chatId);
        }

        // 填充群聊成员数据
        addChatUsers(chatId, userInfoList);
        return chatId;
    }

    /**
     * 非指定群主,需要再去获取群主信息
     *
     * @param chat         群聊信息
     * @param wxCpChat     获取的群聊信息
     * @param userInfoList 用户信息列表
     */
    private void getOwnerInfo(GroupChat chat, WxCpChat wxCpChat, Set<UserInfoVo> userInfoList) {
        Map<String, UserInfoVo> userInfoVoMap = userInfoList.stream().collect(Collectors.toMap(UserInfoVo::getUserid, obj -> obj));
        // 填充群主信息
        chat.setOwner(wxCpChat.getOwner());
        UserInfoVo userInfoVo = userInfoVoMap.getOrDefault(wxCpChat.getOwner(), null);
        if (ObjectUtil.isNotNull(userInfoVo)) {
            chat.setOwnerName(userInfoVo.getName());
        }
    }


    /**
     * 根据群聊id获取群聊信息
     *
     * @param qo 群聊主键ID
     * @return 返回群聊信息详情
     */
    @Override
    public GroupChatVO getGroupChatById(GetGroupChatQo qo) {
        GroupChat groupChat = this.getById(qo.getId());
        if (ObjectUtil.isEmpty(groupChat)) {
            return null;
        }
        GroupChatVO groupChatVO = GroupChatConverter.INSTANCE.toVO(groupChat);
        String chatId = groupChatVO.getChatid();

        // 获取群聊成员信息
        Page<GroupChatUser> page = new Page<>(qo.getPage(), qo.getSize());
        qo.setChatid(chatId);
        qo.setOwner(groupChatVO.getOwner());
        IPage<GroupChatUser> pageGroupChatUsers = groupChatUserService.pageGroupChatUser(page, qo);
        List<GroupChatMemberInfoVO> groupChatMemberInfoVos = new ArrayList<>();
        IPage<GroupChatMemberInfoVO> chatPage = new Page<>();
        if (CollUtil.isEmpty(pageGroupChatUsers.getRecords())) {
            groupChatVO.setGroupChatMemberInfo(chatPage);
            return groupChatVO;
        }

        List<String> userIds = pageGroupChatUsers.getRecords().stream().map(GroupChatUser::getUserid).collect(Collectors.toList());
        // 根据用户Ids获取用户标签信息
        List<TagVo> tagVoList = listTagVo(userIds);
        Map<String, List<TagVo>> tagVoMap;
        if (CollUtil.isNotEmpty(tagVoList)) {
            tagVoMap = tagVoList.stream().collect(Collectors.groupingBy(TagVo::getUserid));
        } else {
            tagVoMap = new HashMap<>(8);
        }

        // 根据用户部门ids获取用户部门信息
        List<DepartmentVo> departments = listDepartmentVo(userIds);
        Map<String, List<DepartmentVo>> departmentVoMap;
        if (CollUtil.isNotEmpty(departments)) {
            departmentVoMap = departments.stream().collect(Collectors.groupingBy(DepartmentVo::getUserId));
        } else {
            departmentVoMap = new HashMap<>(8);
        }

        // 填充成员信息
        pageGroupChatUsers.getRecords().forEach(chatUser -> {
            GroupChatMemberInfoVO member = new GroupChatMemberInfoVO();
            member.setName(chatUser.getUsername());
            member.setUserid(chatUser.getUserid());
            member.setGender(chatUser.getGender());
            // 成员标签
            List<TagVo> tagVos = tagVoMap.getOrDefault(chatUser.getUserid(), null);
            if (CollUtil.isNotEmpty(tagVos)) {
                member.setTagList(tagVos);
            }
            // 成员部门
            List<DepartmentVo> departmentVos = departmentVoMap.getOrDefault(chatUser.getUserid(), null);
            if (CollUtil.isNotEmpty(departmentVos)) {
                member.setDepartmentList(departmentVos);
            }
            groupChatMemberInfoVos.add(member);
        });
        if (CollUtil.isNotEmpty(pageGroupChatUsers.getRecords())) {
            chatPage.setRecords(groupChatMemberInfoVos);
            chatPage.setTotal(pageGroupChatUsers.getTotal());
        }
        groupChatVO.setGroupChatMemberInfo(chatPage);
        return groupChatVO;
    }


    /**
     * 根据用户ids获取标签信息
     *
     * @param userIds 用户ID列表
     * @return 返回用户标签信息
     */
    public List<TagVo> listTagVo(List<String> userIds) {
        List<TagVo> tagVoList = new ArrayList<>();
        // 根据用户ID来获取标签信息
        List<CpUserTagRelation> userTagRelations = userTagRelationService.listTagRelationByUseridList(userIds);
        Map<Long, String> tagMap;
        if (CollUtil.isNotEmpty(userTagRelations)) {
            List<CpTag> tagList = tagService.list();
            if (CollUtil.isNotEmpty(tagList)) {
                tagMap = tagList.stream().collect(Collectors.toMap(CpTag::getTagid, CpTag::getTagname));
            } else {
                tagMap = new HashMap<>(8);
            }
            // 根据标签ID来填充标签名称
            userTagRelations.forEach(userTagRelation -> {
                if (tagMap.getOrDefault(userTagRelation.getTagid(), null) != null) {
                    TagVo tag = new TagVo();
                    tag.setTagid(userTagRelation.getTagid());
                    tag.setUserid(userTagRelation.getUserid());
                    tag.setTagname(tagMap.get(userTagRelation.getTagid()));
                    tagVoList.add(tag);
                }
            });
        }
        return tagVoList;
    }

    /**
     * 根据用户ids获取部门信息
     *
     * @param userIds 用户ids
     * @return 返回部门信息
     */
    public List<DepartmentVo> listDepartmentVo(List<String> userIds) {
        List<DepartmentVo> departmentVoList = new ArrayList<>();
        // 根据用户ID来获取部门信息
        List<CpUserDepartmentRelation> departmentRelations = userDepartmentRelationService.list(new LambdaQueryWrapper<CpUserDepartmentRelation>().in(CpUserDepartmentRelation::getUserid, userIds));
        if (CollUtil.isEmpty(departmentRelations)) {
            return Collections.emptyList();
        }
        // 根据部门Id来填充部门名称
        List<Long> departmentIds = departmentRelations.stream().map(CpUserDepartmentRelation::getDepartmentid).collect(Collectors.toList());
        Map<Long, String> departmentMap;
        if (CollUtil.isNotEmpty(departmentIds)) {
            List<CpDepartment> departments = departmentService.listByIds(departmentIds);
            if (CollUtil.isNotEmpty(departments)) {
                departmentMap = departments.stream().collect(Collectors.toMap(CpDepartment::getId, CpDepartment::getPath));
            } else {
                departmentMap = new HashMap<>(8);
            }
            // 根据标签ID来填充标签名称
            departmentRelations.forEach(departmentRelation -> {
                if (departmentMap.getOrDefault(departmentRelation.getDepartmentid(), null) != null) {
                    DepartmentVo departmentVo = new DepartmentVo();
                    departmentVo.setId(departmentRelation.getDepartmentid());
                    departmentVo.setPath(departmentMap.get(departmentRelation.getDepartmentid()));
                    departmentVo.setUserId(departmentRelation.getUserid());
                    departmentVoList.add(departmentVo);
                }
            });
        }
        return departmentVoList;
    }

    /**
     * 添加或移除群成员
     *
     * @param vo 添加或移除群成员信息
     */
    @Override
    public void addOrRemoveMember(AddOrRemoveMemberQo vo) {
        String chatId = vo.getChatId();
        // 查询群信息
        GroupChat groupChat = this.getOne(new LambdaQueryWrapper<GroupChat>().eq(GroupChat::getChatid, chatId));

        // 获取群成员信息
        List<GroupChatUser> chatUserList = groupChatUserService.list(new LambdaQueryWrapper<GroupChatUser>().eq(GroupChatUser::getChatid, chatId));
        List<String> userIds = chatUserList.stream().map(GroupChatUser::getUserid).collect(Collectors.toList());
        // 添加成员
        if (CollUtil.isNotEmpty(vo.getAddUseridList())) {
            List<GroupChatScope> departmentList = vo.getAddUseridList().stream().filter(item -> GroupConstant.SCOPE_DEPARTMENT.equals(item.getType())).collect(Collectors.toList());
            List<GroupChatScope> tagList = vo.getAddUseridList().stream().filter(item -> GroupConstant.SCOPE_TAG.equals(item.getType())).collect(Collectors.toList());
            List<GroupChatScope> userList = vo.getAddUseridList().stream().filter(item -> GroupConstant.SCOPE_USER.equals(item.getType())).collect(Collectors.toList());
            // 查询对应的群成员信息,并且校验群聊的建群规则
            Set<UserInfoVo> userInfoList = this.getUserInfoVos(departmentList, tagList, userList);
            List<String> addUserIds = userInfoList.stream().map(UserInfoVo::getUserid).collect(Collectors.toList());

            // 先判断成员是否已经存在
            List<GroupChatUser> repeatChatUserList = chatUserList.stream().filter(chatUser -> addUserIds.contains(chatUser.getUserid())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(repeatChatUserList)) {
                // 重复的人员（直接从要添加的人员List中剔除）
                List<String> repeatUserIds = repeatChatUserList.stream().map(GroupChatUser::getUserid).collect(Collectors.toList());
                // 移除重复的人员
                addUserIds.removeAll(repeatUserIds);
            }
            // 判断添加成员是否超过2000人
            int count = chatUserList.size() + addUserIds.size();
            if (count > GroupConstant.MAX_MEMBER_SIZE) {
                throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_MEMBER_SIZE_ERROR);
            }
            // 开始添加成员
            try {
                addChatUser(addUserIds, vo.getAgentId(), chatId);
                // 添加群聊添加成员操作日志
                this.addOrRemoveOperation(chatId, null, departmentList, tagList, userList);
            } catch (WxErrorException e) {
                throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_ADD_MEMBER_ERROR);
            }
            // 添加群聊范围数据
            saveGroupChatScope(vo.getAddUseridList(), chatId);

            // 更新群的群聊成员md5加密字符
            userIds.addAll(addUserIds);
        }

        // 移除成员
        if (CollUtil.isNotEmpty(vo.getRemoveUseridList())) {
            // 判断添加成员是否超过2000人
            int count = chatUserList.size() + vo.getRemoveUseridList().size();
            if (count <= GroupConstant.MIN_MEMBER_SIZE) {
                throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_MEMBER_SIZE_ERROR);
            }
            // 开始移除成员
            try {
                removeChatUser(vo.getRemoveUseridList(), vo.getAgentId(), chatId);
            } catch (WxErrorException e) {
                throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_REMOVE_MEMBER_ERROR);
            }

            // 更新群的群聊成员md5加密字符
            List<String> removeUserIds = vo.getRemoveUseridList();
            userIds.removeAll(removeUserIds);
        }
        Collections.sort(userIds);
        String memberIdEncrypt = DigestUtil.md5Hex(userIds.toString(), StandardCharsets.UTF_8);
        groupChat.setMemberIdEncrypt(memberIdEncrypt);
        this.updateById(groupChat);
    }


    /**
     * 移除群成员
     *
     * @param removeUseridList 移除成员列表
     * @param agentId          应用ID
     * @param chatId           群聊ID
     */
    private void removeChatUser(List<String> removeUseridList, String agentId, String chatId) throws WxErrorException {
        WxCpChatService wxCpChatService = wxCpServiceFactory.get(corpId, agentId).getChatService();
        // 移除群聊人员
        wxCpChatService.update(chatId, null, null, null, removeUseridList);

        // 添加移出群聊成员操作记录
        List<GroupChatUser> groupChatUsers = groupChatUserService.list(new LambdaQueryWrapper<GroupChatUser>().eq(GroupChatUser::getChatid, chatId).in(GroupChatUser::getUserid, removeUseridList));
        if (CollUtil.isNotEmpty(groupChatUsers)) {
            List<String> removeUserNames = new ArrayList<>();
            for (GroupChatUser item : groupChatUsers) {
                removeUserNames.add(item.getUsername());
            }
            // 添加操作记录
            addOrRemoveOperation(chatId, removeUserNames, Collections.emptyList(), Collections.emptyList(), Collections.emptyList());
        }

        // 移除本地库群聊人员
        groupChatUserService.remove(new LambdaQueryWrapper<GroupChatUser>()
                .eq(GroupChatUser::getChatid, chatId).in(GroupChatUser::getUserid, removeUseridList));

    }

    /**
     * 添加成员
     *
     * @param addUseridList 添加成员列表
     * @param chatId        群聊ID
     */
    private void addChatUser(List<String> addUseridList, String agentId, String chatId) throws WxErrorException {
        WxCpChatService wxCpChatService = wxCpServiceFactory.get(corpId, agentId).getChatService();
        // 添加群聊成员
        wxCpChatService.update(chatId, null, null, addUseridList, null);

        // 保存群成员信息
        UserInfoQo userInfoQo = new UserInfoQo();
        userInfoQo.setUseridList(addUseridList);
        // 根据成员、部门、标签id信息获取有效成员信息列表
        Set<UserInfoVo> userInfoList = userService.getUsersScope(userInfoQo);
        if (CollUtil.isNotEmpty(userInfoList)) {
            addChatUsers(chatId, userInfoList);
        }
    }

    /**
     * 添加群成员
     *
     * @param chatId       群聊ID
     * @param userInfoList 成员信息列表
     */
    private void addChatUsers(String chatId, Set<UserInfoVo> userInfoList) {
        List<GroupChatUser> groupChatUserList = new ArrayList<>();
        userInfoList.forEach(userInfo -> {
            GroupChatUser groupChatUser = new GroupChatUser();
            groupChatUser.setChatid(chatId);
            groupChatUser.setUserid(userInfo.getUserid());
            groupChatUser.setUsername(userInfo.getName());
            groupChatUser.setGender(userInfo.getGender());
            groupChatUserList.add(groupChatUser);
        });
        // 添加成员信息
        groupChatUserService.saveBatch(groupChatUserList);
    }

    /**
     * 批量保存群聊范围
     *
     * @param chatScopes 群聊范围列表
     * @param chatId     群聊ID
     */
    private void saveGroupChatScope(List<GroupChatScope> chatScopes, String chatId) {
        for (GroupChatScope item : chatScopes) {
            item.setChatid(chatId);
        }
        groupChatScopeService.saveBatch(chatScopes);
    }


    /**
     * 发送群聊消息
     *
     * @param vo 群聊消息信息
     */
    @Override
    public void sendGroupChatMessage(SendGroupChatMessageQo vo) {
        Integer type = vo.getType();
        // 发送文本消息
        if (type.equals(GroupChatMessageTypeEnum.TEXT.getCode())) {
            sendTextMessage(vo.getChatIds(), vo.getAgentId(), vo.getContent());
        }
        // 发送图文消息 或 发送文件消息
        if (type.equals(GroupChatMessageTypeEnum.IMAGE.getCode())
                || type.equals(GroupChatMessageTypeEnum.FILE.getCode())) {
            try {
                MultipartFile multipartFile = vo.getFile();
                InputStream inputStream = multipartFile.getInputStream();
                String suffix = "";
                String fileName = "";
                // 获取文件后缀与文件名
                if (StringUtils.isNotEmpty(multipartFile.getOriginalFilename())) {
                    suffix = this.getFileSuffix(multipartFile.getOriginalFilename());
                    fileName = this.getFileName(multipartFile.getOriginalFilename());
                }
                // 获取file文件流
                File tempFile = FileUtil.createTempFile(fileName, suffix, false);
                FileUtil.writeFromStream(inputStream, tempFile);
                sendImageMessage(vo.getChatIds(), vo.getAgentId(), tempFile, type);
            } catch (WxErrorException e) {
                throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_SEND_MESSAGE_ERROR);
            } catch (IOException e) {
                throw new ServiceException(ExceptionCodeEnum.SYSTEM_COMMON_ERROR);
            }
        }
    }

    /**
     * 修改群聊名称或群主信息
     *
     * @param vo 修改群聊入参
     */
    @Override
    public void updateGroup(UpdateGroupChatQo vo) throws WxErrorException {
        String chatId = vo.getChatId();
        // 查询群信息
        GroupChat groupChat = this.getOne(new LambdaQueryWrapper<GroupChat>().eq(GroupChat::getChatid, chatId));
        if (ObjectUtil.isEmpty(groupChat)) {
            throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_NOT_EXIST);
        }
        // 开始修改群聊信息
        WxCpChatService wxCpChatService = wxCpServiceFactory.get(corpId, vo.getAgentId()).getChatService();
        wxCpChatService.update(chatId, vo.getName(), vo.getOwner(), null, null);

        // 添加修改群聊操作记录
        if (StringUtils.isNotEmpty(vo.getOwner()) || StringUtils.isNotEmpty(vo.getName())) {
            addGroupChatOperation(vo, groupChat, chatId);
        }

        groupChat.setOwner(vo.getOwner());
        groupChat.setOwnerName(vo.getOwnerName());
        groupChat.setName(vo.getName());
        groupChat.setRemark(vo.getRemark());
        this.updateById(groupChat);
    }

    /**
     * 添加修改群聊操作记录(修改群聊名称)
     *
     * @param vo        修改群聊入参
     * @param groupChat 群聊信息
     * @param chatId    群聊ID
     */
    private void addGroupChatOperation(UpdateGroupChatQo vo,
                                       GroupChat groupChat,
                                       String chatId) {
        GroupChatOperation groupChatOperation = new GroupChatOperation();
        // 修改群聊名称
        if (StringUtils.isNotEmpty(vo.getName())) {
            groupChatOperation.setType(GroupConstant.UPDATE_GROUP_CHAT_NAME);
            String template = String.format(GroupConstant.GROUP_CHAT_OPERATION_UPDATE_NAME, groupChat.getName(), vo.getName());
            groupChatOperation.setContent(template);
        }
        // 修改群聊群主
        if (StringUtils.isNotEmpty(vo.getOwner())) {
            groupChatOperation.setType(GroupConstant.UPDATE_GROUP_CHAT_OWNER);
            String template = String.format(GroupConstant.GROUP_CHAT_OPERATION_UPDATE_OWNER, groupChat.getOwnerName(), vo.getOwnerName());
            groupChatOperation.setContent(template);
        }
        groupChatOperation.setChatid(chatId);
        groupChatOperation.setCreateDate(new Date());
        groupChatOperation.setCreator(String.valueOf(SecurityUtils.getUserId()));
        groupChatOperation.setCreatorName(SecurityUtils.getUsername());
        groupChatOperationService.save(groupChatOperation);
    }

    /**
     * 添加修改群聊操作记录(添加或移出群聊成员)
     *
     * @param chatId         群聊ID
     * @param removeUserList 移除成员列表
     */
    private void addOrRemoveOperation(String chatId,
                                      List<String> removeUserList,
                                      List<GroupChatScope> departmentScopes,
                                      List<GroupChatScope> tagScopes,
                                      List<GroupChatScope> userScopes) {
        GroupChatOperation groupChatOperation = new GroupChatOperation();
        // 移出成员
        if (CollUtil.isNotEmpty(removeUserList)) {
            groupChatOperation.setType(GroupConstant.REMOVE_GROUP_CHAT_MEMBER);
            String template = String.format(GroupConstant.GROUP_CHAT_OPERATION_REMOVE_MEMBER, removeUserList);
            groupChatOperation.setContent(template);
        }
        // 添加成员时的操作日志
        if (CollUtil.isNotEmpty(departmentScopes) || CollUtil.isNotEmpty(tagScopes) || CollUtil.isNotEmpty(userScopes)) {
            List<String> departmentIds = departmentScopes.stream().map(GroupChatScope::getDataId).collect(Collectors.toList());
            List<String> tagIds = tagScopes.stream().map(GroupChatScope::getDataId).collect(Collectors.toList());
            List<String> userIds = userScopes.stream().map(GroupChatScope::getDataId).collect(Collectors.toList());

            // 根据ID获取部门名称
            String departmentName = "";
            if (CollUtil.isNotEmpty(departmentIds)) {
                List<String> departmentNames = departmentService.listByIds(departmentIds).stream().map(CpDepartment::getName).collect(Collectors.toList());
                departmentName = String.join(",", departmentNames);
            }
            // 根据ID获取标签名称
            String tagName = "";
            if (CollUtil.isNotEmpty(tagIds)) {
                List<String> tagNames = tagService.listByIds(tagIds).stream().map(CpTag::getTagname).collect(Collectors.toList());
                tagName = String.join(",", tagNames);
            }
            // 根据ID获取成员名称
            String userName = "";
            if (CollUtil.isNotEmpty(userIds)) {
                List<String> addUserList = userService.listUsersByUseridList(userIds).stream().map(UserInfoVo::getName).collect(Collectors.toList());
                userName = String.join(",", addUserList);
            }

            groupChatOperation.setType(GroupConstant.ADD_GROUP_CHAT_MEMBER);
            String template = String.format(GroupConstant.GROUP_CHAT_OPERATION_ADD_MEMBER, departmentName, tagName, userName);
            groupChatOperation.setContent(template);
        }
        groupChatOperation.setChatid(chatId);
        groupChatOperation.setCreateDate(new Date());
        groupChatOperation.setCreator(String.valueOf(SecurityUtils.getUserId()));
        groupChatOperation.setCreatorName(SecurityUtils.getUsername());
        groupChatOperationService.save(groupChatOperation);
    }

    /**
     * 发送图文消息
     *
     * @param chatIds 群聊ID列表
     * @param agentId 应用ID
     * @param file    图文文件流
     * @param type    发送消息的类型
     */
    public void sendImageMessage(List<String> chatIds, String agentId, File file, Integer type) throws WxErrorException {
        String msgType = type.equals(GroupChatMessageTypeEnum.IMAGE.getCode()) ? MsgTypeEnum.IMAGE.value() : MsgTypeEnum.FILE.value();
        WxMediaUploadResult result = getWxMediaUploadResult(agentId, file, msgType);
        String mediaId = result.getMediaId();
        // 开始发送图文消息
        WxCpChatService wxCpChatService = wxCpServiceFactory.get(corpId, agentId).getChatService();
        // 根据类型发送消息
        chatIds.forEach(chatId -> {
            try {
                // 发送群聊图片或文件消息
                WxCpAppChatMessage wxCpAppChatMessage = new WxCpAppChatMessage();
                wxCpAppChatMessage.setChatId(chatId);
                wxCpAppChatMessage.setMsgType(msgType);
                wxCpAppChatMessage.setMediaId(mediaId);
                wxCpChatService.sendMsg(wxCpAppChatMessage);
            } catch (WxErrorException e) {
                throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_SEND_MESSAGE_ERROR);
            }
        });
        // 更新群聊消息状态
        this.update(new LambdaUpdateWrapper<GroupChat>()
                .set(GroupChat::getStatus, YnEnum.YES.value())
                .in(GroupChat::getChatid, chatIds));
    }

    /**
     * 上传临时素材
     *
     * @param agentId 应用ID
     * @param file    素材文件
     * @throws WxErrorException 企微异常
     */
    private WxMediaUploadResult getWxMediaUploadResult(String agentId, File file, String mediaType) throws WxErrorException {
        // 先上传文件至企微的临时素材库
        WxCpService wxCpService = wxCpServiceFactory.get(corpId, agentId);
        WxMediaUploadResult result = wxCpService.getMediaService()
                .upload(mediaType, file);
        if (ObjectUtil.isEmpty(result)) {
            throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_UPLOAD_MATERIAL_ERROR);
        }
        return result;
    }

    /**
     * 获取文件后缀
     *
     * @param originalFileName 文件名称
     * @return 返回文件后缀
     */
    public String getFileSuffix(String originalFileName) {
        int lastPointIndex = originalFileName.lastIndexOf(".");
        return originalFileName.substring(lastPointIndex);
    }

    /**
     * 获取文件名称
     *
     * @param originalFileName 文件名称
     * @return 返回文件名称不携带后缀
     */
    public String getFileName(String originalFileName) {
        int index = originalFileName.lastIndexOf(".");
        if (index != -1) {
            return originalFileName.substring(0, index);
        } else {
            return originalFileName;
        }
    }

    /**
     * 发送文本消息
     *
     * @param chatIds 群聊ID列表
     * @param agentId 应用ID
     * @param content 文本内容
     */
    public void sendTextMessage(List<String> chatIds, String agentId, String content) {
        if (CollUtil.isNotEmpty(chatIds)) {
            WxCpChatService wxCpChatService = wxCpServiceFactory.get(corpId, agentId).getChatService();
            chatIds.forEach(chatId -> {
                try {
                    // 发送群聊文本消息
                    WxCpAppChatMessage wxCpAppChatMessage = new WxCpAppChatMessage();
                    wxCpAppChatMessage.setChatId(chatId);
                    wxCpAppChatMessage.setMsgType(MsgTypeEnum.TEXT.value());
                    wxCpAppChatMessage.setContent(content);
                    wxCpChatService.sendMsg(wxCpAppChatMessage);
                } catch (WxErrorException e) {
                    throw new ServiceException(ExceptionCodeEnum.GROUP_CHAT_SEND_MESSAGE_ERROR);
                }
            });

            // 更新群聊消息状态
            this.update(new LambdaUpdateWrapper<GroupChat>()
                    .set(GroupChat::getStatus, YnEnum.YES.value())
                    .in(GroupChat::getChatid, chatIds));
        }
    }


}


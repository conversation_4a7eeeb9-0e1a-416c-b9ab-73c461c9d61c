package com.lanshan.base.commonservice.addressbook.service;

import com.lanshan.base.commonservice.addressbook.dto.UserIdCardDTO;
import com.lanshan.base.commonservice.addressbook.vo.UserInfoBindVO;
import me.chanjar.weixin.common.error.WxErrorException;

import java.util.List;

public interface UserFaceIdentifyService {

    /**
     * 获取人脸核身用的userIdKey
     *
     * @return userIdKey
     */
    String getUserIdKey(UserIdCardDTO dto) throws WxErrorException;

    /**
     * 获取用户的认证信息
     *
     * @param verifyResult 认证结果
     * @return 用户身份信息列表
     */
    List<UserInfoBindVO> getUserIdentifyInfo(String verifyResult) throws WxErrorException;

    /**
     * 获取用户的手机号
     * @param code 凭证
     * @return 手机号
     * @throws WxErrorException 异常
     */
    String getUserPhoneNumber(String code) throws WxErrorException;

    /**
     * 从缓存中获取用户手机号 （用户需要先调用getUserPhoneNumber获取手机号）
     * @param code
     * @return
     */
    String getUserPhoneNumberByCodeFromRedis(String code);

    /**
     * 获取用户认证信息（未人脸认证）
     *
     * @param openId    微信小程序 openId
     * @param name      姓名
     * @param idCardNum 身份证号
     * @return
     */
    List<UserInfoBindVO> getUserIdentifyInfoNoAuth(String openId, String name, String idCardNum);
}

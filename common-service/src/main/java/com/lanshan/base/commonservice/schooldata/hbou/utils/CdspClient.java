package com.lanshan.base.commonservice.schooldata.hbou.utils;

import com.alibaba.fastjson2.JSON;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.commonservice.schooldata.hbou.vo.DataBzks;
import com.lanshan.base.commonservice.schooldata.hbou.vo.DataJzgxx;
import com.lanshan.base.commonservice.schooldata.hbou.vo.DataZzjgxx;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CdspClient {
//    private final String API_DOMAIN = "https://mdm.hubstc.edu.cn";
//    private final String APP_ID = "1905157550042398721";
//    private final String SECRET = "b5ca6445300d4f679f5cc02dd19691cd";


    @Value("${hbou.addressbook.cdsp.app-id:''}")
    private String appId;

    @Value("${hbou.addressbook.cdsp.secret:''}")
    private String secret;

    @Value("${hbou.addressbook.cdsp.api-domain:''}")
    private String apiDomain;

    @Value("${hbou.addressbook.cdsp.jzg-url:''}")
    private String jzgUrl;

    @Value("${hbou.addressbook.cdsp.zzjg-url:''}")
    private String zzjgUrl;

    @Value("${hbou.addressbook.cdsp.bzks-url:''}")
    private String bzksUrl;


    private final HbouHttpClient httpClient;

    @Autowired
    public CdspClient(HbouHttpClient httpClient) {
        this.httpClient = httpClient;
    }


    /**
     * 获取教职工信息
     *
     * @param page 页码
     * @return 结果
     */
    public JzgxxResult getJzgxx(Integer page) {
        String apiUrl = apiDomain + jzgUrl;
        return sendRequest("教职工", apiUrl, page, JzgxxResult.class, List.class, DataJzgxx.class);
    }

    /**
     * 获取组织信息
     *
     * @param page 页码
     * @return 结果
     */
    public ZzjgResult getZzjg(Integer page) {
        String apiUrl = apiDomain + zzjgUrl;
        return sendRequest("组织机构", apiUrl, page, ZzjgResult.class, List.class, DataZzjgxx.class);
    }

    /**
     * 获取本专科生
     *
     * @param page 页码
     * @return 结果
     */
    public BzksResult getBzks(Integer page) {
        String apiUrl = apiDomain + bzksUrl;
        return sendRequest("本专科生", apiUrl, page, BzksResult.class, List.class, DataBzks.class);
    }

    /**
     * 发送请求并处理响应
     *
     * @param resourceName 资源名称
     * @param apiUrl       API地址
     * @param page         页码
     * @param types        返回类型参数
     * @return 响应结果
     */
    private <T> T sendRequest(String resourceName, String apiUrl, Integer page, Type... types) {
        SignResult signResult = generateSign();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("$count", true);
        paramMap.put("$top", 1000);  //最大1000
        paramMap.put("$skip", (page - 1) * 1000);
        String jsonBody = JSON.toJSONString(paramMap);


        // 构建请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("appId", appId);
        headers.put("timestamp", signResult.getTimestamp());
        headers.put("sign", signResult.getSign());
        headers.put("Content-Type", "application/json");

        // 发送请求
        String resultStr = httpClient.post(apiUrl, jsonBody, headers, resourceName);

        // 验证响应
        if (!resultStr.contains("@odata.context")) {
            throw ExceptionCodeEnum.BAD_REQUEST.toServiceException("请求湖北开放大学获取" + resourceName + "接口失败！失败原因：" + resultStr);
        }

        // 解析响应
        return JSON.parseObject(resultStr, TypeUtils.buildType(types));
    }

    private SignResult generateSign() {
        // 获取当前时间戳
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 拼接待签名字符串
        String stringToSign = appId + secret + timestamp;
        // 计算MD5并转小写
        String sign = DigestUtils.md5Hex(stringToSign);
        // 将sign进行Base64编码
        String base64Sign = Base64.getEncoder().encodeToString(sign.getBytes());
        return new SignResult(timestamp, base64Sign);
    }
}

package com.lanshan.base.commonservice.schooldata.whut.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 本科生专业基本信息表(WhutUndergraduateMajorInfo)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class WhutUndergraduateMajorInfo extends Model<WhutUndergraduateMajorInfo> {
    /**
     * 自增主键ID
     */
    private Long id;
    /**
     * 专业编码
     */
    private String zybm;
    /**
     * 专业名称
     */
    private String zymc;
    /**
     * 专业全称
     */
    private String zyqc;
    /**
     * 专业英文名称
     */
    private String zyywmc;
    /**
     * 专业培养目标
     */
    private String zypymb;
    /**
     * 专业培养要求
     */
    private String zypyyq;
    /**
     * 专业类别
     */
    private String zylb;
    /**
     * 归属大类名称
     */
    private String gsdlmc;
    /**
     * 招生类别
     */
    private String zslb;
    /**
     * 院系编码
     */
    private String yxbm;
    /**
     * 校区编码
     */
    private String xqbm;
    /**
     * 学制
     */
    private String xz;
    /**
     * 培养层次
     */
    private String pycc;
    /**
     * 本专科专业编码
     */
    private String bzkzybm;
    /**
     * 学位
     */
    private String xw;
    /**
     * 是否辅修专业
     */
    private String sffxzy;
    /**
     * 启用标志码
     */
    private String qybz;
    /**
     * 时间戳
     */
    private Date tstamp;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}


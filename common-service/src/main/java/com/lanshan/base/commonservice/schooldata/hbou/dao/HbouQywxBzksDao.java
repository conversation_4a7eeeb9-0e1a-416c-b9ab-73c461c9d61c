package com.lanshan.base.commonservice.schooldata.hbou.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxBzks;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface HbouQywxBzksDao extends BaseMapper<HbouQywxBzks> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<HbouQywxBzks> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<HbouQywxBzks> entities);

    /**
     * 清空表数据
     */
    @Update("TRUNCATE TABLE school_data.hbou_qywx_bzks")
    void truncate();
}

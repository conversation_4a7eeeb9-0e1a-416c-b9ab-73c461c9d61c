package com.lanshan.base.commonservice.standarddata.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 *
 * <AUTHOR> yang.
 * @since 2025-06-04
 */

@Data
@TableName(value = "std_department", schema = "standard_data",autoResultMap = true)
public class StdDepartment implements Serializable {

	private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private String name;

    private Long parentid;

    @TableField("\"order\"")
    private Long order;

    private String departmentLeader;

    private String path;

    private String idPath;

}

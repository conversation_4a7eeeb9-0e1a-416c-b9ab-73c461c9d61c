package com.lanshan.base.commonservice.todo.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("待办删除流程节点入参")
public class TodoDelFlowNodeQo implements Serializable {
    private static final long serialVersionUID = 671041102319015843L;

    @ApiModelProperty("流水号")
    private String serialNo;

    @ApiModelProperty("节点id")
    private Long nodeId;

}


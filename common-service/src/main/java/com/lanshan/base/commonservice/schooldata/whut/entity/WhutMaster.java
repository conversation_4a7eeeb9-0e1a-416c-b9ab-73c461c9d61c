package com.lanshan.base.commonservice.schooldata.whut.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 学生基本信息表(WhutMaster)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class WhutMaster extends Model<WhutMaster> {
    /**
     * 自增主键ID
     */
    private Integer id;
    /**
     * 学号
     */
    private String xh;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 外文姓名
     */
    private String wwxm;
    /**
     * 姓名拼音
     */
    private String xmpy;
    /**
     * 曾用名
     */
    private String cym;
    /**
     * 性别码
     */
    private String xbm;
    /**
     * 出生日期
     */
    private Date csrq;
    /**
     * 出生地码
     */
    private String csdm;
    /**
     * 籍贯
     */
    private String jg;
    /**
     * 民族码
     */
    private String mzm;
    /**
     * 国籍/地区码
     */
    private String gjdqm;
    /**
     * 身份证件类型码
     */
    private String sfzjlxm;
    /**
     * 身份证件号
     */
    private String sfzjh;
    /**
     * 婚姻状况码
     */
    private String hyzkm;
    /**
     * 港澳台侨外码
     */
    private String gatqwm;
    /**
     * 政治面貌码
     */
    private String zzmmm;
    /**
     * 健康状况码
     */
    private String jkzkm;
    /**
     * 血型码
     */
    private String xxm;
    /**
     * 身份证件有效期
     */
    private String sfzjyxq;
    /**
     * 是否独生子女
     */
    private String sfdszn;
    /**
     * 户口性质
     */
    private String hkxzm;
    /**
     * 户口所在地
     */
    private String hkszd;
    /**
     * 学生类别码
     */
    private String xslbm;
    /**
     * 导师工号
     */
    private String dsgh;
    /**
     * 就读学位码
     */
    private String jdxwm;
    /**
     * 就读学历码
     */
    private String jdxlm;
    /**
     * 家庭住址
     */
    private String jtzz;
    /**
     * 邮编
     */
    private String yb;
    /**
     * 手机号码
     */
    private String sjhm;
    /**
     * 考生号
     */
    private String ksh;
    /**
     * 报名号
     */
    private String bmh;
    /**
     * 入学前工作单位
     */
    private String rxqgzdw;
    /**
     * 定向委培单位
     */
    private String dxwpdw;
    /**
     * 何时何地受过何种奖励或处分
     */
    private String hshdsghzjlhcf;
    /**
     * 录取类别码
     */
    private String lqlbm;
    /**
     * 培养状态码
     */
    private String pyztm;
    /**
     * 硕士是否答辩
     */
    private String sssfdb;
    /**
     * 录取年份
     */
    private String lqnf;
    /**
     * 是否外国人
     */
    private String sfwgr;
    /**
     * 学籍状态码
     */
    private String xjztm;
    /**
     * 是否在职
     */
    private String sfzz;
    /**
     * 全日制或非全日制
     */
    private String qrzhfqrz;
    /**
     * 记录创建时间戳
     */
    private Date tstamp;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}


package com.lanshan.base.commonservice.addressbook.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.addressbook.entity.CpUserPhone;
import com.lanshan.base.commonservice.addressbook.vo.UserIdentityInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户手机号绑定表(CpUserPhone)表数据库访问层
 *
 * <AUTHOR>
 */
public interface CpUserPhoneDao extends BaseMapper<CpUserPhone> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<CpUserPhone> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<CpUserPhone> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<CpUserPhone> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<CpUserPhone> entities);

    UserIdentityInfoVO getIdentityInfo(String currentUserId);
}


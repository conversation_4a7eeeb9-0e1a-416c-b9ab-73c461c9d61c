package com.lanshan.base.commonservice.schooldata.whut.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 本科生班级基本信息表(WhutUndergraduateClassInfo)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class WhutUndergraduateClassInfo extends Model<WhutUndergraduateClassInfo> {
    /**
     * 自增主键ID
     */
    private Long id;
    /**
     * 班级编码
     */
    private String bjbm;
    /**
     * 班级名称
     */
    private String bjmc;
    /**
     * 班级全称
     */
    private String bjqc;
    /**
     * 专业编码
     */
    private String zybm;
    /**
     * 所属年级
     */
    private String ssjn;
    /**
     * 学制
     */
    private String xz;
    /**
     * 建班年月
     */
    private String jbny;
    /**
     * 毕业年度
     */
    private String bynd;
    /**
     * 班长学号
     */
    private String bzxh;
    /**
     * 班级性质
     */
    private String bjxz;
    /**
     * 男生人数
     */
    private String nsrs;
    /**
     * 女生人数
     */
    private String nvsrs;
    /**
     * 时间戳
     */
    private Date tstam;
    /**
     * 群聊ID
     */
    private String chatid;
    /**
     * 建群状态（0:待建群 1:已建群）
     */
    private Integer status;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}


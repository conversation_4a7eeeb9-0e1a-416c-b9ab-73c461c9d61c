package com.lanshan.base.commonservice.group.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.group.entity.GroupChat;
import com.lanshan.base.commonservice.group.qo.*;
import com.lanshan.base.commonservice.group.vo.ChatInfo;
import com.lanshan.base.commonservice.group.vo.ExistGroupChatVo;
import com.lanshan.base.commonservice.group.vo.GroupChatVO;
import me.chanjar.weixin.common.error.WxErrorException;

/**
 * 群聊信息表(GroupChat)表服务接口
 *
 * <AUTHOR>
 */
public interface GroupChatService extends IService<GroupChat> {

    /**
     * 分页查询群聊数据
     * @param qo 群聊分页入参
     * @return 返回群聊列表
     */
    IPage<GroupChatVO> pageGroupChat(GroupChatSearchQo qo);

    /**
     * 查询群聊是否存在
     * @param qo 部门-标签-用户列表
     * @return 已存在群聊列表
     */
    ExistGroupChatVo listExistGroupChat(ListExistGroupChatQo qo);

    /**
     * 创建群聊
     * @param vo 群聊信息
     * @throws WxErrorException 企微微信API异常
     */
    String createGroup(ChatInfo vo) throws WxErrorException;

    /**
     * 通过主键查询单条数据
     * @param qo 群聊ID
     * @return 返回群聊详情
     */
    GroupChatVO getGroupChatById(GetGroupChatQo qo);

    /**
     * 添加或移除群成员
     * @param vo 添加或移除成员入参
     */
    void addOrRemoveMember(AddOrRemoveMemberQo vo);

    /**
     * 发送群消息
     * @param vo 发送消息入参
     */
    void sendGroupChatMessage(SendGroupChatMessageQo vo);

    /**
     * 修改群聊
     * @param vo 需修改的群聊信息
     * @throws WxErrorException 企微微信API异常
     */
    void updateGroup(UpdateGroupChatQo vo) throws WxErrorException;
}


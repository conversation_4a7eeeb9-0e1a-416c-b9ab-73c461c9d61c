package com.lanshan.base.commonservice.group.dao;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import com.lanshan.base.commonservice.group.entity.GroupChat;

/**
 * 群聊信息表(GroupChat)表数据库访问层
 *
 * <AUTHOR>
 */
public interface GroupChatDao extends BaseMapper<GroupChat> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<GroupChat> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<GroupChat> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<GroupChat> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<GroupChat> entities);

    /**
     * 查询指定应用名称
     *
     * @param agentIdSet 应用id集合
     * @return
     */
    List<Map<Integer, String>> selectWbAppName(Set<Integer> agentIdSet);
    long insertByMe(@Param("entity") GroupChat chat);
}


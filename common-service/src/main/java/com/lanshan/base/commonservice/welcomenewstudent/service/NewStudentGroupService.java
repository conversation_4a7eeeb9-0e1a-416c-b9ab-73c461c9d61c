package com.lanshan.base.commonservice.welcomenewstudent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.group.qo.SendGroupChatMessageQo;
import com.lanshan.base.commonservice.group.qo.UpdateGroupChatQo;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import com.lanshan.base.commonservice.welcomenewstudent.qo.NewStudentGroupQO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentGroupVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 新生群表(NewStudentGroup)表服务接口
 *
 * <AUTHOR>
 */
public interface NewStudentGroupService extends IService<NewStudentGroup> {

    /**
     * 分页查询所有数据
     *
     * @param qo 查询参数
     * @return
     */
    IPage<NewStudentGroupVO> pageByParam(NewStudentGroupQO qo);

    /**
     * 导入群信息
     *
     * @param file 文件
     */
    Boolean importGroup(MultipartFile file);

    /**
     * 导出未建的群信息
     *
     * @param response 响应
     */
    void exportGroup(HttpServletResponse response);

    /**
     * 导出群信息
     *
     * @param response 响应
     */
    void exportError(HttpServletResponse response);

    /**
     * 修改群主
     *
     * @return
     */
    Boolean changeGroupOwner(UpdateGroupChatQo vo);

    /**
     * 发送群消息
     *
     * @return
     */
    Boolean sendGroupMsg(SendGroupChatMessageQo qo);
}


package com.lanshan.base.commonservice.addressbook.service.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.dto.common.TagIdDTO;
import com.lanshan.base.api.dto.user.TagAddOrRemoveUsersDto;
import com.lanshan.base.api.dto.user.TagDto;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.feign.user.TagFeign;
import com.lanshan.base.api.qo.user.TagSaveQo;
import com.lanshan.base.api.qo.user.TagUsersQo;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.dao.CpUserImportTagLogDao;
import com.lanshan.base.commonservice.addressbook.dto.CpUserTagImportExcelDto;
import com.lanshan.base.commonservice.addressbook.entity.CpUserImportTagLog;
import com.lanshan.base.commonservice.addressbook.entity.CpUserImportTagLogDetail;
import com.lanshan.base.commonservice.addressbook.qo.CpUserImportTagLogQO;
import com.lanshan.base.commonservice.addressbook.service.CpUserImportTagLogDetailService;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.vo.CpUserImportResutlVO;
import com.lanshan.base.commonservice.addressbook.vo.CpUserImportTagLogVO;
import com.lanshan.base.commonservice.addressbook.service.CpUserImportTagLogService;
import com.lanshan.base.commonservice.addressbook.service.UserTagRelationService;
import com.lanshan.base.commonservice.addressbook.entity.CpUserTagRelation;
import com.lanshan.base.commonservice.config.properties.AgentProperties;
import jodd.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 企业微信用户导入标签日志服务实现
 */
@Slf4j
@Service("cpUserImportTagLogService")
public class CpUserImportTagLogServiceImpl extends ServiceImpl<CpUserImportTagLogDao, CpUserImportTagLog> implements CpUserImportTagLogService {

    @Resource
    private UserService userService;

    @Resource
    private CpUserImportTagLogDetailService cpUserImportTagLogDetailService;

    @Resource
    private TagFeign tagFeign;
    
    @Resource
    private AgentProperties agentProperties;
    
    @Resource
    private UserTagRelationService userTagRelationService;

    @Override
    public IPage<CpUserImportTagLogVO> page(IPage<CpUserImportTagLog> page, CpUserImportTagLogQO qo) {
        LambdaQueryWrapper<CpUserImportTagLog> queryWrapper = Wrappers.lambdaQuery(CpUserImportTagLog.class);

        // 设置查询条件
        if (qo.getCode() != null) {
            queryWrapper.like(CpUserImportTagLog::getCode, qo.getCode());
        }
        if (qo.getOperator() != null) {
            queryWrapper.like(CpUserImportTagLog::getOperator, qo.getOperator());
        }
        if (qo.getHasError() != null) {
            queryWrapper.eq(CpUserImportTagLog::getHasError, qo.getHasError());
        }

        // 按ID倒序排序
        queryWrapper.orderByDesc(CpUserImportTagLog::getId);

        // 执行查询
        IPage<CpUserImportTagLog> result = this.page(page, queryWrapper);

        // 转换为VO
        return result.convert(this::convertToVO);
    }

    @Override
    public boolean add(CpUserImportTagLogQO qo) {
        CpUserImportTagLog entity = new CpUserImportTagLog();
        BeanUtils.copyProperties(qo, entity);
        return this.save(entity);
    }

    @Override
    public boolean update(CpUserImportTagLogQO qo) {
        if (qo.getId() == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("ID不能为空");
        }

        CpUserImportTagLog entity = this.getById(qo.getId());
        if (entity == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("记录不存在");
        }

        BeanUtils.copyProperties(qo, entity);
        return this.updateById(entity);
    }

    @Override
    public CpUserImportTagLogVO getDetail(Long id) {
        CpUserImportTagLog entity = this.getById(id);
        if (entity == null) {
            return null;
        }
        return convertToVO(entity);
    }


    @Override
    public CpUserImportResutlVO importTag(MultipartFile file, boolean createNewTag) {
        if (file.getSize() > 100 * 1024 * 1024) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导入文件不能大于100M");
        }
        // 立即读取文件内容到内存
        byte[] fileBytes;
        try {
            fileBytes = file.getBytes();
        } catch (IOException e) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("文件读取失败");
        }
        final String opUserName = SecurityContextHolder.getUserName();

        String code = generateCode();
        try (InputStream is = new ByteArrayInputStream(fileBytes)) {
            // 使用EasyExcel读取数据，表头在第二行
            List<CpUserTagImportExcelDto> dataList = EasyExcel.read(is)
                    .head(CpUserTagImportExcelDto.class)
                    .sheet()
                    .headRowNumber(1)  // 指定表头在第2行
                    .doReadSync();


            // 搜集所有标签
            List<String> allTags = dataList.stream()
                    .map(CpUserTagImportExcelDto::getTags)
                    .filter(Objects::nonNull)
                    .flatMap(tags -> Arrays.stream(tags.split("&")))
                    .distinct()
                    .collect(Collectors.toList());

            //标签名称，长度限制为32个字以内（汉字或英文字母）
            if (allTags.stream().anyMatch(tag -> tag.length() >= 32)) {
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("标签长度不能超过32");
            }

            // 查询企微所有标签
            Result<List<TagDto>> listResult = tagFeign.tagList(agentProperties.getCorpId(), null);
            List<TagDto> tagList = listResult.getResult();
            // 判断allTags 不在tagList中的集合
            final List<TagDto> finalTagList = tagList;
            List<String> notInTagList = allTags.stream()
                    .filter(tag -> finalTagList.stream().noneMatch(tagDto -> tagDto.getTagname().equals(tag)))
                    .collect(Collectors.toList());
            //有TAG需要新增，则返回需要新增的集合
            if (!notInTagList.isEmpty()) {
                if (!createNewTag) {
                    CpUserImportResutlVO cpUserImportResutlVO = new CpUserImportResutlVO();
                    cpUserImportResutlVO.setNeedCreateTagNameList(notInTagList);
                    return cpUserImportResutlVO;
                }
                //新增标签
                StringBuilder failTagStr = new StringBuilder();
                for (String tagName : notInTagList) {
                    try {
                        TagSaveQo tagSaveQo = new TagSaveQo();
                        tagSaveQo.setTagname(tagName);
                        Result<TagIdDTO> tagIdDTOResult = tagFeign.tagCreate(agentProperties.getCorpId(), null, tagSaveQo);
                        if (tagIdDTOResult.hasError()) {
                            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("标签【" + tagName + "】创建失败");
                        }
                    } catch (Exception e) {
                        // 将添加失败的标签名称合并字符串
                        failTagStr.append(tagName).append("&");
                    }
                }
                if (failTagStr.length() > 0) {
                    //插入失败记录
                    createImportLog(code, opUserName, true, "标签添加失败：" + failTagStr.substring(0, failTagStr.length() - 1));
                    throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("标签添加失败：" + failTagStr.substring(0, failTagStr.length() - 1));
                } else {
                    // 重新从企微查询所有标签
                    listResult = tagFeign.tagList(agentProperties.getCorpId(), null);
                    tagList = listResult.getResult();
                }
            }

            // 创建成功的导入日志
            createImportLog(code, opUserName, false, "");
            CpUserImportTagLog currentLog = this.getOne(new LambdaQueryWrapper<CpUserImportTagLog>().eq(CpUserImportTagLog::getCode, code));
            if (currentLog == null) {
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("导入文件失败");
            }

            return handelDetail(currentLog, dataList, tagList);

        } catch (IOException e) {
            log.error("导入数据异常", e);
            //插入失败记录
            createImportLog(code, opUserName, true, e.getMessage());
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException(e.getMessage());
        }
    }

    /**
     * 创建导入日志记录
     */
    private void createImportLog(String code, String operator, boolean hasError, String errorMessage) {
        CpUserImportTagLog cpUserImportTagLog = new CpUserImportTagLog();
        cpUserImportTagLog.setCode(code);
        cpUserImportTagLog.setOperator(operator);
        cpUserImportTagLog.setOperateTime(new DateTime());
        cpUserImportTagLog.setHasError(hasError);
        cpUserImportTagLog.setErrorMessage(errorMessage);
        this.save(cpUserImportTagLog);
    }

    private CpUserImportResutlVO handelDetail(CpUserImportTagLog currentLog, List<CpUserTagImportExcelDto> dataList, List<TagDto> tagList) {
        int successCounter = 0;
        int failCounter = 0;
        // 提取所有用户ID用于查询现有数据
        List<String> userIds = dataList.stream()
                .map(CpUserTagImportExcelDto::getUserId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 查询现有用户详细信息
        List<CpUser> userInfoList = userService.listByIds(userIds);
        Map<String, CpUser> userInfoMap = userInfoList.stream()
                .collect(Collectors.toMap(CpUser::getUserid, cpUser -> cpUser));

        List<CpUserImportTagLogDetail> detailList = new ArrayList<>();
        // 处理每条数据
        for (CpUserTagImportExcelDto data : dataList) {
            CpUserImportTagLogDetail detail = new CpUserImportTagLogDetail();
            detail.setUserId(data.getUserId());
            detail.setName(data.getUserName());
            detail.setTags(data.getTags());
            detail.setSuccessTags("");
            detail.setFailTags("");
            detail.setLogId(currentLog.getId());
            detail.setHasError(false);
            try {
                // 校验必填字段
                if (StringUtil.isBlank(data.getUserId())) {
                    throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("学工号不能为空");
                }
                CpUser userInfo1 = userInfoMap.get(data.getUserId());
                if (userInfo1 == null) {
                    throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("学工号不存在");
                }
                if (!userInfo1.getName().equals(data.getUserName())) {
                    throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("学工号和姓名不匹配");
                }
                String tagStr = data.getTags();
                if (StringUtil.isBlank(tagStr)) {
                    throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("标签不能为空");
                }
                List<String> tmpTagList = Arrays.asList(tagStr.split("&"));
                if (tmpTagList.size() > 10) {
                    throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("标签不能超过10个");
                }
                successCounter++;
            } catch (Exception e) {
                detail.setHasError(true);
                detail.setErrorMessage(e.getMessage());
                failCounter++;
            }
            detailList.add(detail);
        }
        
        // 统一保存详情到数据库
        cpUserImportTagLogDetailService.saveBatch(detailList);
                       
        //提取出成功记录的 所有标签集合
        List<String> tmpTagList = detailList.stream()
                .filter(detail -> !detail.getHasError())
                .map(CpUserImportTagLogDetail::getTags)
                .flatMap(tagStr -> Arrays.stream(tagStr.split("&")))
                .distinct() // 去重
                .collect(Collectors.toList());
        
        for (String tagStr : tmpTagList) {
            Optional<TagDto> first = tagList.stream().filter(tagInfo -> tagInfo.getTagname().equals(tagStr))
                    .findFirst();
            if (first.isEmpty()) {
                //企微没有此标签 一般不会出现此情况，因为上面已经新增了
                continue;
            }
            TagDto tagDto = first.get();
            //从成功记录中查询包含此标签的所有用户
            List<String> tagUserIds = detailList.stream()
                    .filter(detail -> !detail.getHasError())
                    .filter(detail -> detail.getTags().contains(tagStr))
                    .map(CpUserImportTagLogDetail::getUserId)
                    .collect(Collectors.toList());
            //批量将含有此标签的用户在企微打上标签
            Result<TagAddOrRemoveUsersDto> tagAddOrRemoveUsersDtoResult = tagFeign.addTagUsers(agentProperties.getCorpId(), null,
                    new TagUsersQo(tagDto.getTagid(), tagUserIds, null)
            );
            
            // 使用Map优化查找性能
            updateTagResults(detailList, tagUserIds, tagStr, !tagAddOrRemoveUsersDtoResult.hasError());
        }

        // 批量更新详情记录到数据库
        cpUserImportTagLogDetailService.updateBatchById(detailList);
        
        // 批量新增用户标签关联数据
        batchInsertUserTagRelations(detailList, tagList);

        CpUserImportResutlVO cpUserImportResutlVO = new CpUserImportResutlVO();
        cpUserImportResutlVO.setSuccessCount(successCounter);
        cpUserImportResutlVO.setFailCount(failCounter);
        return cpUserImportResutlVO;
    }
    
    /**
     * 优化标签结果更新逻辑
     */
    private void updateTagResults(List<CpUserImportTagLogDetail> detailList, List<String> tagUserIds, String tagStr, boolean isSuccess) {
        // 构建userId到detail的映射，提高查找效率
        Map<String, CpUserImportTagLogDetail> userDetailMap = detailList.stream()
                .filter(detail -> tagUserIds.contains(detail.getUserId()) && detail.getTags().contains(tagStr))
                .collect(Collectors.toMap(CpUserImportTagLogDetail::getUserId, detail -> detail, (existing, replacement) -> existing));
        
        for (String userId : tagUserIds) {
            CpUserImportTagLogDetail detail = userDetailMap.get(userId);
            if (detail != null) {
                if (isSuccess) {
                    // 添加成功标签
                    String currentSuccessTags = detail.getSuccessTags();
                    if (StringUtil.isBlank(currentSuccessTags)) {
                        detail.setSuccessTags(tagStr);
                    } else {
                        detail.setSuccessTags(currentSuccessTags + "&" + tagStr);
                    }
                } else {
                    // 添加失败标签
                    String currentFailTags = detail.getFailTags();
                    if (StringUtil.isBlank(currentFailTags)) {
                        detail.setFailTags(tagStr);
                    } else {
                        detail.setFailTags(currentFailTags + "&" + tagStr);
                    }
                }
            }
        }
    }

    /**
     * 批量新增用户标签关联数据
     */
    private void batchInsertUserTagRelations(List<CpUserImportTagLogDetail> detailList, List<TagDto> tagList) {
        // 构建标签名称到ID的映射
        Map<String, Long> tagNameToIdMap = tagList.stream()
                .collect(Collectors.toMap(TagDto::getTagname, TagDto::getTagid));
        
        // 收集所有需要新增的用户标签关联
        List<CpUserTagRelation> newRelations = new ArrayList<>();
        
        // 提取成功记录中的用户标签关系
        for (CpUserImportTagLogDetail detail : detailList) {
            if (!detail.getHasError() && StringUtil.isNotBlank(detail.getSuccessTags())) {
                String userId = detail.getUserId();
                String[] successTags = detail.getSuccessTags().split("&");
                
                for (String tagName : successTags) {
                    Long tagId = tagNameToIdMap.get(tagName);
                    if (tagId != null) {
                        newRelations.add(CpUserTagRelation.builder()
                                .userid(userId)
                                .tagid(tagId)
                                .build());
                    }
                }
            }
        }
        
        if (!newRelations.isEmpty()) {
            // 方案2：先查询所有相关用户的现有标签，然后在内存中过滤（更高效）
            List<String> userIds = newRelations.stream()
                    .map(CpUserTagRelation::getUserid)
                    .distinct()
                    .collect(Collectors.toList());
            
            // 只查询涉及用户的所有现有标签关联
            List<CpUserTagRelation> existingRelations = userTagRelationService.list(
                    Wrappers.<CpUserTagRelation>lambdaQuery()
                            .in(CpUserTagRelation::getUserid, userIds)
            );
            
            // 构建现有关联的唯一标识集合
            Set<String> existingKeys = existingRelations.stream()
                    .map(relation -> relation.getUserid() + "_" + relation.getTagid())
                    .collect(Collectors.toSet());
            
            // 过滤掉已存在的关联关系
            List<CpUserTagRelation> toInsert = newRelations.stream()
                    .filter(relation -> !existingKeys.contains(relation.getUserid() + "_" + relation.getTagid()))
                    .collect(Collectors.toList());
            
            if (!toInsert.isEmpty()) {
                userTagRelationService.saveBatch(toInsert);
                log.info("批量新增用户标签关联{}条", toInsert.size());
            } else {
                log.info("所有用户标签关联已存在，无需新增");
            }
        }
    }

    /**
     * 生成唯一标识
     *
     * @return
     */
    private String generateCode() {
        //规则 dbq_年月日时分秒+4位随机数
        StringBuffer sb = new StringBuffer();
        sb.append("dbq_");
        sb.append(DateTime.now().toString("yyyyMMddHHmmss"));
        sb.append(String.format("%04d", new Random().nextInt(10000)));
        return sb.toString();
    }

    /**
     * 将实体转换为VO
     *
     * @param entity 实体
     * @return VO
     */
    private CpUserImportTagLogVO convertToVO(CpUserImportTagLog entity) {
        CpUserImportTagLogVO vo = new CpUserImportTagLogVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}
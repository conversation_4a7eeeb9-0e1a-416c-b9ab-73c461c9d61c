package com.lanshan.base.commonservice.group.vo;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 群聊信息表(GroupChat)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "群聊信息表VO")
@Data
@ToString
public class GroupChatVO implements Serializable {

    @ApiModelProperty(value = "自增ID")
    private Long id;

    @ApiModelProperty(value = "群聊ID（企业微信的群聊唯一标识）")
    private String chatid;

    @ApiModelProperty(value = "群名称")
    private String name;

    @ApiModelProperty(value = "群主工号")
    private String owner;

    @ApiModelProperty(value = "群主名称")
    private String ownerName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建人名称")
    private String creatorName;

    @ApiModelProperty(value = "修改人")
    private String updater;

    @ApiModelProperty(value = "修改人名称")
    private String updaterName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "修改时间")
    private Date updateDate;

    @ApiModelProperty(value = "群聊类型")
    private Integer type;

    @ApiModelProperty(value = "群聊状态(0:未激活 1:已激活)")
    private Integer status;

    @ApiModelProperty(value = "群聊成员md5加密字符（用来对比所有成员是否已存在另一个群聊中）")
    private String memberIdEncrypt;

    @ApiModelProperty(value = "建群消息")
    private String message;

    @ApiModelProperty(value = "选择群主类型（0：指定群主，1：群内任一成员）")
    private Integer ownerType;

    @ApiModelProperty(value = "成员人数")
    private Integer memberSize;

    @ApiModelProperty(value = "群聊的成员详情")
    private IPage<GroupChatMemberInfoVO> groupChatMemberInfo;

    @ApiModelProperty(value = "已加入群聊成员 userid 列表")
    private List<String> joinUseridList;
}


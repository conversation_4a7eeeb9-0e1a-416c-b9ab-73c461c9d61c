package com.lanshan.base.commonservice.config.rabbitmq;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @program: wx-demo
 * @Description:
 * @createTime: 2025-05-14 09:22
 */
@Component
@ConfigurationProperties(prefix = "message")
public class MessageConfig {

        private String queuename;
        private String queueexchange;

    public String getQueuename() {
        return queuename;
    }

    public void setQueuename(String queuename) {
        this.queuename = queuename;
    }

    public String getQueueexchange() {
        return queueexchange;
    }

    public void setQueueexchange(String queueexchange) {
        this.queueexchange = queueexchange;
    }
}

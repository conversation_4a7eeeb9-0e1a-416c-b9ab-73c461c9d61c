package com.lanshan.base.commonservice.message.controller;

import com.alibaba.fastjson2.JSONObject;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.starter.rabbitmq.service.EnhancedMqMessageSender;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 消息中间件消息发送控制器
 * 支持多种类型的消息发送
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/message/mq")
@Api(tags = "消息中间件消息发送控制器", hidden = true)
public class MqMsgController {

    @Resource
    private EnhancedMqMessageSender enhancedMqMessageSender;

    @PostMapping("/send")
    @ApiOperation("发送消息")
    public Result<Boolean> sendMsg(String routeKey, @RequestBody String msg) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg", msg);
        jsonObject.put("routeKey", routeKey);

        // 使用默认的direct交换机发送消息
        enhancedMqMessageSender.sendMessage("", routeKey, jsonObject);
        return Result.build(true);
    }

    @PostMapping("/send/direct")
    @ApiOperation("发送消息到Direct交换机")
    public Result<Boolean> sendDirectMsg(String exchange, String routingKey, @RequestBody String msg) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg", msg);
        jsonObject.put("exchange", exchange);
        jsonObject.put("routingKey", routingKey);

        enhancedMqMessageSender.sendToDirectExchange(exchange, routingKey, jsonObject);
        return Result.build(true);
    }

    @PostMapping("/send/topic")
    @ApiOperation("发送消息到Topic交换机")
    public Result<Boolean> sendTopicMsg(String exchange, String routingKey, @RequestBody String msg) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg", msg);
        jsonObject.put("exchange", exchange);
        jsonObject.put("routingKey", routingKey);

        enhancedMqMessageSender.sendToTopicExchange(exchange, routingKey, jsonObject);
        return Result.build(true);
    }

    @PostMapping("/send/priority")
    @ApiOperation("发送优先级消息")
    public Result<Boolean> sendPriorityMsg(String exchange, String routingKey, int priority, @RequestBody String msg) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg", msg);
        jsonObject.put("priority", priority);

        enhancedMqMessageSender.sendMessageWithPriority(exchange, routingKey, jsonObject, priority);
        return Result.build(true);
    }

    @PostMapping("/send/delayed")
    @ApiOperation("发送延时消息")
    public Result<Boolean> sendDelayedMsg(String exchange, String routingKey, long delay, @RequestBody String msg) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("msg", msg);
        jsonObject.put("delay", delay);

        enhancedMqMessageSender.sendDelayedMessage(exchange, routingKey, jsonObject, delay, TimeUnit.SECONDS);
        return Result.build(true);
    }
}

package com.lanshan.base.commonservice.welcomenewstudent.excel.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.group.constant.GroupConstant;
import com.lanshan.base.commonservice.group.entity.GroupChat;
import com.lanshan.base.commonservice.group.entity.GroupChatScope;
import com.lanshan.base.commonservice.group.service.GroupChatService;
import com.lanshan.base.commonservice.group.vo.ChatInfo;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentGroupExportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentGroupImportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentGroupService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 新生群导入监听
 */
@Slf4j
public class NewStuGroupImportListener implements ReadListener<NewStudentGroupImportDTO> {

    private final String agentId;

    private final RedisService redisService;

    private final GroupChatService groupChatService;

    private final NewStudentGroupService newStudentGroupService;

    private static final int BATCH_COUNT = 1000;

    private final LongAdder successAdder = new LongAdder();

    private final List<NewStudentGroup> cachedDataList = new ArrayList<>(BATCH_COUNT * 2);

    private final List<NewStudentGroupExportDTO> errorImportDataList = new ArrayList<>();

    private final Set<String> allGroupCode = new HashSet<>();

    public NewStuGroupImportListener(String agentId, GroupChatService groupChatService, RedisService redisService,
                                     NewStudentGroupService newStudentGroupService) {
        this.agentId = agentId;
        this.groupChatService = groupChatService;
        this.redisService = redisService;
        this.newStudentGroupService = newStudentGroupService;
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        ReadListener.super.invokeHead(headMap, context);
        // 删除redis中保存的错误信息
        redisService.deleteObject(CommonServiceRedisKeys.NEW_STUDENT_GROUP_IMPORT_ERROR_LIST);
    }

    @Override
    public void invoke(NewStudentGroupImportDTO data, AnalysisContext context) {
        if (!checkParam(data)) {
            return;
        }
        initAllGroupCode();
        if (!allGroupCode.contains(data.getGroupCode())) {
            return;
        }
        try {
            List<String> joinInUser = new ArrayList<>(8);
            joinInUser.add(data.getOwnerUserid());
            joinInUser.add(data.getCustomOneUserid());
            joinInUser.add(data.getCustomTwoUserid());
            NewStudentGroup newStudentGroup = new NewStudentGroup();
            BeanUtil.copyProperties(data, newStudentGroup);
            newStudentGroup.setJoinInUser(joinInUser);
            ChatInfo chatInfo = buildGroupCreateEntity(data);
            String chatId = groupChatService.createGroup(chatInfo);
            newStudentGroup.setChatId(chatId);
            cachedDataList.add(newStudentGroup);
        } catch (Exception e) {
            log.error("创建群聊失败,失败原因:", e);
            NewStudentGroupExportDTO errorData = new NewStudentGroupExportDTO();
            BeanUtil.copyProperties(data, errorData);
            errorData.setErrorMsg(e.getMessage());
            errorImportDataList.add(errorData);
            return;
        }
        if (cachedDataList.size() >= BATCH_COUNT) {
            doSave();
            cachedDataList.clear();
        }
    }

    @NotNull
    private ChatInfo buildGroupCreateEntity(NewStudentGroupImportDTO data) {
        //构建群聊参数
        ChatInfo chatInfo = new ChatInfo();
        //构建群聊信息
        GroupChat groupChat = new GroupChat();
        groupChat.setOwner(data.getOwnerUserid());
        groupChat.setOwnerName(data.getOwnerName());
        groupChat.setName(data.getGroupName());
        groupChat.setRemark(data.getRemark());
        groupChat.setType(GroupConstant.GROUP_CHAT_NEW_STU);
        groupChat.setMessage("创建新生群：" + data.getGroupName());
        groupChat.setOwnerType(GroupConstant.GROUP_OWNER_TYPE_DESIGNATION);
        //构建群成员数据
        List<GroupChatScope> userList = new ArrayList<>();
        GroupChatScope customOne = new GroupChatScope();
        customOne.setDataId(data.getCustomOneUserid());
        customOne.setDataName(data.getCustomOneName());
        customOne.setType(GroupConstant.SCOPE_USER);
        userList.add(customOne);
        GroupChatScope customTwo = new GroupChatScope();
        customTwo.setDataId(data.getCustomTwoUserid());
        customTwo.setDataName(data.getCustomTwoName());
        customTwo.setType(GroupConstant.SCOPE_USER);
        userList.add(customTwo);
        chatInfo.setChat(groupChat);
        chatInfo.setUserList(userList);
        chatInfo.setTagList(Collections.emptyList());
        chatInfo.setDepartmentList(Collections.emptyList());
        //设置建群使用的应用 ID
        chatInfo.setAgentId(agentId);
        return chatInfo;
    }

    private boolean checkParam(NewStudentGroupImportDTO data) {
        String errorMsg = "";
        if (StrUtil.isBlank(data.getGroupName())) {
            errorMsg = errorMsg + "群名称不能为空；";
        }
        if (StrUtil.isBlank(data.getOwnerName())) {
            errorMsg = errorMsg + "群主姓名不能为空；";
        }
        if (StrUtil.isBlank(data.getOwnerUserid())) {
            errorMsg = errorMsg + "群主学工号不能为空；";
        }
        if (StrUtil.isBlank(data.getYear())) {
            errorMsg = errorMsg + "入学年份不能为空；";
        }
        if (StrUtil.isBlank(data.getGroupCode())) {
            errorMsg = errorMsg + "群聊编码不能为空；";
        }
        if (StrUtil.isBlank(data.getCustomOneUserid())) {
            errorMsg = errorMsg + "成员1学工号不能为空；";
        }
        if (StrUtil.isBlank(data.getCustomTwoUserid())) {
            errorMsg = errorMsg + "成员2学工号不能为空；";
        }
        if (StrUtil.isBlank(errorMsg)) {
            return true;
        }
        NewStudentGroupExportDTO errorData = new NewStudentGroupExportDTO();
        BeanUtil.copyProperties(data, errorData);
        errorData.setErrorMsg(errorMsg);
        errorImportDataList.add(errorData);
        return false;
    }

    private void initAllGroupCode() {
        synchronized (allGroupCode) {
            if (allGroupCode.isEmpty()) {
                allGroupCode.addAll(newStudentGroupService.list(Wrappers.<NewStudentGroup>lambdaQuery()
                                .select(NewStudentGroup::getGroupCode))
                        .stream().map(NewStudentGroup::getGroupCode)
                        .collect(Collectors.toSet()));
            }
        }
    }

    private void doSave() {
        for (NewStudentGroup newStudentGroup : cachedDataList) {
            newStudentGroupService.update(newStudentGroup, Wrappers.<NewStudentGroup>lambdaQuery().eq(NewStudentGroup::getGroupCode, newStudentGroup.getGroupCode()));
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("所有数据解析完成！,成功条数：{},失败条数：{}", successAdder.longValue(), errorImportDataList.size());
        //缓存错误数据
        redisService.deleteObject(CommonServiceRedisKeys.NEW_STUDENT_GROUP_IMPORT_ERROR_LIST);
        if (CollUtil.isNotEmpty(errorImportDataList)) {
            redisService.setCacheList(CommonServiceRedisKeys.NEW_STUDENT_GROUP_IMPORT_ERROR_LIST, errorImportDataList);
        }
        //如果不为空执行保存
        if (!cachedDataList.isEmpty()) {
            doSave();
        }
    }
}

package com.lanshan.base.commonservice.welcomenewstudent.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 新生信息表(NewStudentData)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "新生信息表VO")
@Data
@ToString
public class NewStudentDataVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "学号")
    private String userid;

    @ApiModelProperty(value = "手机号")
    private String phoneNo;

    @ApiModelProperty(value = "入学年份 例如 2025")
    private String year;

    @ApiModelProperty(value = "证件类型")
    private String identityType;

    @ApiModelProperty(value = "院系代码")
    private String collegeCode;

    @ApiModelProperty(value = "院系名称")
    private String collegeName;

    @ApiModelProperty(value = "专业代码")
    private String majorCode;

    @ApiModelProperty(value = "专业名称")
    private String majorName;

    @ApiModelProperty(value = "班级代码")
    private String classCode;

    @ApiModelProperty(value = "班级名称")
    private String className;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "生源地")
    private String sourceOfOrigin;

    @ApiModelProperty(value = "是否认证")
    private Boolean authStatus;

    @ApiModelProperty(value = "是否开通")
    private Boolean openStatus;

    @ApiModelProperty(value = "是否签到")
    private Boolean checkInStatus;

    @ApiModelProperty(value = "是否开始发送消息状态")
    private Boolean startSendStatus;

    @ApiModelProperty(value = "是否结束发送消息状态")
    private Boolean endSendStatus;

    @ApiModelProperty(value = "是否加入群")
    private Boolean joinInGroupStatus;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "创建人姓名")
    private String creatorName;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "更新人")
    private String updater;

    @ApiModelProperty(value = "更新人姓名")
    private String updaterName;

    @ApiModelProperty(value = "更新时间")
    private Date updateDate;
}


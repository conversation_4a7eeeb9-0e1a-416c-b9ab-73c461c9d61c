package com.lanshan.base.commonservice.standardapp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "RefundSettingVO", description = "退费设置视图对象")
public class RefundSettingVO implements Serializable {
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "转换日期")
    private Date transferDate;

    @ApiModelProperty(value = "退费日期")
    private Date refundDate;

    @ApiModelProperty(value = "捐赠说明")
    private String donateRemark;
}
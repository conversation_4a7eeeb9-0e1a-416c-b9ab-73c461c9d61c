package com.lanshan.base.commonservice.addressbook.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.addressbook.entity.UserInfoBind;
import com.lanshan.base.commonservice.addressbook.vo.UserIdentityInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户认证表(UserInfoBind)表数据库访问层
 *
 * <AUTHOR>
 */
public interface UserInfoBindDao extends BaseMapper<UserInfoBind> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<UserInfoBind> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<UserInfoBind> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<UserInfoBind> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<UserInfoBind> entities);

    /**
     * 根据openId查询用户身份信息
     *
     * @param openId 微信openId
     * @return 用户身份信息
     */
    List<UserIdentityInfoVO> listIdentitiesByOpenId(String openId);

    /**
     * 获取新生用户身份信息
     *
     * @param currentUserId 当前用户Id
     * @return
     */
    UserIdentityInfoVO getNewStuIdentityInfo(String currentUserId);
}


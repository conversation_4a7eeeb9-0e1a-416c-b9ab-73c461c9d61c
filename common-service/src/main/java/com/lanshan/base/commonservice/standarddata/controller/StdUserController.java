package com.lanshan.base.commonservice.standarddata.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.api.page.PageResult;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.standarddata.dto.StdUserPageDTO;
import com.lanshan.base.commonservice.standarddata.po.StdUser;
import com.lanshan.base.commonservice.standarddata.service.StdDepartmentService;
import com.lanshan.base.commonservice.standarddata.service.StdUserService;
import com.lanshan.base.commonservice.standarddata.vo.StdDepartmentTreeVO;
import com.lanshan.base.commonservice.standarddata.vo.StdUserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 通讯录中间库
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/standarddata/stdUser")
public class StdUserController {

    @Resource
    private StdUserService stdUserService;
    @Resource
    private StdDepartmentService stdDepartmentService;

    /**
     * 部门树形结构
     * <AUTHOR> yang.
     * @since 2025/6/4 10:43
     */
    @PostMapping(value = "/getDeptTree" ,produces = "application/json;charset=UTF-8")
    public Result<List<StdDepartmentTreeVO>> getDeptTree(){
        return Result.build(stdDepartmentService.getDeptTree());
    }

    /**
     * 用户分页列表
     * <AUTHOR> yang.
     * @since 2025/6/4 10:43
     */
    @PostMapping(value = "/getUserPageList" ,produces = "application/json;charset=UTF-8")
    public Result<Page<StdUserVO>> getUserPageList(@RequestBody StdUserPageDTO dto){
        Page<StdUserVO> pg = stdUserService.getUserPageList(dto);
        return Result.build(pg);
    }

}

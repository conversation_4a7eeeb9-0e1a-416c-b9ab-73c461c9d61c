package com.lanshan.base.commonservice.welcomenewstudent.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 新生信息表(NewStudentData)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class NewStudentData extends Model<NewStudentData> {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 身份证号
     */
    private String idCardNum;
    /**
     * 姓名
     */
    private String name;
    /**
     * 学号
     */
    private String userid;
    /**
     * 手机号
     */
    private String phoneNo;
    /**
     * 入学年份 例如 2025
     */
    private String year;
    /**
     * 证件类型
     */
    private String identityType;
    /**
     * 院系代码
     */
    private String collegeCode;
    /**
     * 院系名称
     */
    private String collegeName;
    /**
     * 专业代码
     */
    private String majorCode;
    /**
     * 专业名称
     */
    private String majorName;
    /**
     * 班级代码
     */
    private String classCode;
    /**
     * 班级名称
     */
    private String className;
    /**
     * 性别
     */
    private String gender;
    /**
     * 生源地
     */
    private String sourceOfOrigin;
    /**
     * 是否认证
     */
    private Boolean authStatus;
    /**
     * 是否开通
     */
    private Boolean openStatus;
    /**
     * 是否签到
     */
    private Boolean checkInStatus;
    /**
     * 是否开始发送消息状态
     */
    private Boolean startSendStatus;
    /**
     * 是否结束发送消息状态
     */
    private Boolean endSendStatus;
    /**
     * 是否加入群组状态
     */
    private Boolean joinInGroupStatus;
    /**
     * 删除标识
     */
    private Boolean deleteFlag;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 创建人姓名
     */
    @TableField(fill = FieldFill.INSERT)
    private String creatorName;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updater;
    /**
     * 更新人姓名
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updaterName;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateDate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}


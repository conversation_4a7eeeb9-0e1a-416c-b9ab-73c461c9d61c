package com.lanshan.base.commonservice.visitor.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.dto.app.AppDTO;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.enums.MsgChannelEnum;
import com.lanshan.base.api.enums.MsgTypeEnum;
import com.lanshan.base.api.enums.YnEnum;
import com.lanshan.base.api.qo.PageQo;
import com.lanshan.base.api.utils.file.ExcelUtils;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.config.manager.AsyncManager;
import com.lanshan.base.commonservice.config.manager.factory.AsyncFactory;
import com.lanshan.base.commonservice.message.enums.MsgPublishTypeEnum;
import com.lanshan.base.commonservice.message.enums.MsgSourceEnum;
import com.lanshan.base.commonservice.message.qo.MsgSaveQo;
import com.lanshan.base.commonservice.message.service.MsgDefEsService;
import com.lanshan.base.commonservice.system.service.ISysConfigService;
import com.lanshan.base.commonservice.visitor.convert.VisitorEntourageConvert;
import com.lanshan.base.commonservice.visitor.convert.VisitorInfoConvert;
import com.lanshan.base.commonservice.visitor.convert.VisitorRecordConvert;
import com.lanshan.base.commonservice.visitor.entity.VisitorEntourage;
import com.lanshan.base.commonservice.visitor.entity.VisitorInfo;
import com.lanshan.base.commonservice.visitor.entity.VisitorOperateLog;
import com.lanshan.base.commonservice.visitor.entity.VisitorRecord;
import com.lanshan.base.commonservice.visitor.enums.*;
import com.lanshan.base.commonservice.visitor.excel.VisitorRecordExportDto;
import com.lanshan.base.commonservice.visitor.mapper.VisitorRecordMapper;
import com.lanshan.base.commonservice.visitor.qo.*;
import com.lanshan.base.commonservice.visitor.service.VisitorEntourageService;
import com.lanshan.base.commonservice.visitor.service.VisitorInfoService;
import com.lanshan.base.commonservice.visitor.service.VisitorRecordService;
import com.lanshan.base.commonservice.visitor.vo.AppointQRcodeVO;
import com.lanshan.base.commonservice.visitor.vo.VisitorRecordVo;
import com.lanshan.base.commonservice.workbench.service.AppManageService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 访客记录表(VisitorRecord)服务实现类
 */
@Slf4j
@Service
public class VisitorRecordServiceImpl extends ServiceImpl<VisitorRecordMapper, VisitorRecord> implements VisitorRecordService {

    @Resource
    private VisitorEntourageService visitorEntourageService;

    @Resource
    private UserService userService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private VisitorInfoService visitorInfoService;

    @Resource
    private MsgDefEsService msgDefEsService;

    @Resource
    private AppManageService appManageService;

    @Resource
    private WxMaService wxMaService;

    @Resource(name = "commonTaskExecutor")
    private ThreadPoolTaskExecutor commonExecutor;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveAppointment(VisitorAppointmentSaveQo qo) {
        VisitorRecordSaveQo recordSaveQo = VisitorRecordConvert.INSTANCE.toQo(qo);

        //类型为预约
        recordSaveQo.setType(VisitorRecordTypeEnum.APPOINTMENT.getCode());

        //审核人为被访问人
        recordSaveQo.setAuditorUserid(qo.getIntervieweeUserid());
        recordSaveQo.setAuditorName(qo.getIntervieweeName());

        //查询访客信息是否存在，如果不存在则新增
        processVisitorInfo(qo);

        //新增访客记录
        saveRecord(recordSaveQo);

        //向审核人发送预约审批通知
        commonExecutor.execute(() -> sendApprovalNotice(recordSaveQo));
    }

    /**
     * 处理访客信息
     * 查询访客信息是否存在，如果不存在则新增
     */
    private void processVisitorInfo(VisitorAppointmentSaveQo qo) {
        //从当前线程获取手机号
        String mobile = SecurityContextHolder.getUserId();

        VisitorInfo info = visitorInfoService.getById(mobile);
        if (info == null) {
            VisitorInfo entity = VisitorInfoConvert.INSTANCE.toEntity(qo);
            //从当前线程获取OPEN_ID
            entity.setOpenId(SecurityContextHolder.getUserName());
            visitorInfoService.save(entity);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveInvitation(VisitorInvitationSaveQo qo) {
        VisitorRecordSaveQo recordSaveQo = VisitorRecordConvert.INSTANCE.toQo(qo);

        //类型为邀约
        recordSaveQo.setType(VisitorRecordTypeEnum.INVITATION.getCode());

        //被访问人、审核人为当前用户
        String userId = SecurityContextHolder.getUserId();
        //查询用户
        CpUser user = userService.getById(userId);
        recordSaveQo.setIntervieweeUserid(userId);
        recordSaveQo.setIntervieweeName(user.getName());
        recordSaveQo.setIntervieweeMobile(user.getMobile());
        recordSaveQo.setAuditorUserid(userId);
        recordSaveQo.setAuditorName(user.getName());

        //状态为已通过
        recordSaveQo.setStatus(VisitorRecordStatusEnum.APPROVED.getCode());

        //新增访客记录
        saveRecord(recordSaveQo);
    }

    @Override
    public IPage<VisitorRecordVo> pageAppointment(PageQo qo) {
        VisitorRecordPageQo pageQo = BeanUtil.copyProperties(qo, VisitorRecordPageQo.class);
        log.info("请求参数：{},UserId:{}", JSON.toJSONString(pageQo), SecurityContextHolder.getUserId());
        //类型为预约
        pageQo.setType(VisitorRecordTypeEnum.APPOINTMENT.getCode());
        //从当前线程获取手机号
        pageQo.setMobile(SecurityContextHolder.getUserId());

        //分页查询访客记录
        return pageRecordVo(pageQo);
    }

    @Override
    public IPage<VisitorRecordVo> pageInvitation(PageQo qo) {
        VisitorRecordPageQo pageQo = BeanUtil.copyProperties(qo, VisitorRecordPageQo.class);

        //类型为邀约
        pageQo.setType(VisitorRecordTypeEnum.INVITATION.getCode());
        //从当前线程获取手机号
        pageQo.setMobile(SecurityContextHolder.getUserId());

        //分页查询访客记录
        return pageRecordVo(pageQo);
    }

    @Override
    public IPage<VisitorRecordVo> pageAudit(VisitorRecordAuditPageQo qo) {
        VisitorRecordPageQo pageQo = VisitorRecordConvert.INSTANCE.toQo(qo);

        //类型为预约
        pageQo.setType(VisitorRecordTypeEnum.APPOINTMENT.getCode());

        //审核人为当前用户
        pageQo.setAuditorUserid(SecurityContextHolder.getUserId());

        //分页查询访客记录
        return pageRecordVo(pageQo);
    }

    @Override
    public IPage<VisitorRecordVo> pageMyInvitation(VisitorRecordAuditPageQo qo) {
        VisitorRecordPageQo pageQo = VisitorRecordConvert.INSTANCE.toQo(qo);

        //类型为邀约
        pageQo.setType(VisitorRecordTypeEnum.INVITATION.getCode());

        //审核人为当前用户
        pageQo.setAuditorUserid(SecurityContextHolder.getUserId());

        //分页查询访客记录
        return pageRecordVo(pageQo);
    }

    @Override
    public IPage<VisitorRecordVo> pageRecord(VisitorRecordPageQo qo) {
        //分页查询访客记录
        return pageRecordVo(qo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelAppointment(Long recordId) {
        //查询记录
        VisitorRecord visitorRecord = getById(recordId);
        if (visitorRecord == null) {
            throw ExceptionCodeEnum.VISITOR_RECORD_NOT_EXIST.toServiceException();
        }

        //只能取消待审核的预约
        if (VisitorRecordStatusEnum.PENDING.getCode() != visitorRecord.getStatus()) {
            throw ExceptionCodeEnum.VISITOR_RECORD_ONLY_CANCEL_PENDING.toServiceException();
        }

        //取消预约
        updateRecordStatus(recordId, VisitorRecordStatusEnum.CANCELLED.getCode());

        //取消预约的用户没有用户id，只有名称
        VisitorOperateLog log = VisitorOperateLog.builder()
                .recordId(recordId)
                .type(VisitorOperateTypeEnum.CANCEL_APPOINTMENT.getCode())
                .name(visitorRecord.getName()).build();
        //新增操作记录
        AsyncManager.me().execute(AsyncFactory.addVisitorOperateLog(log));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void audit(VisitorRecordProcessQo qo) {
        //查询记录
        VisitorRecord visitorRecord = getById(qo.getId());
        if (visitorRecord == null) {
            throw ExceptionCodeEnum.VISITOR_RECORD_NOT_EXIST.toServiceException();
        }

        if (VisitorRecordStatusEnum.APPROVED.getCode() == qo.getStatus()) {
            //只能审核通过待审核、已拒绝的预约
            if (VisitorRecordStatusEnum.PENDING.getCode() != visitorRecord.getStatus()
                    && VisitorRecordStatusEnum.REJECTED.getCode() != visitorRecord.getStatus()) {
                throw ExceptionCodeEnum.VISITOR_RECORD_ONLY_APPROVE_PENDING_REJECTED.toServiceException();
            }

        } else {
            //只能审核拒绝待审核、已通过的预约
            if (VisitorRecordStatusEnum.PENDING.getCode() != visitorRecord.getStatus()
                    && VisitorRecordStatusEnum.APPROVED.getCode() != visitorRecord.getStatus()) {
                throw ExceptionCodeEnum.VISITOR_RECORD_ONLY_REJECT_APPROVED.toServiceException();
            }
        }

        //审核记录
        updateRecordStatus(qo.getId(), qo.getStatus());

        //操作类型
        int operateType = qo.getStatus() ==
                VisitorRecordStatusEnum.APPROVED.getCode() ? VisitorOperateTypeEnum.APPROVE.getCode() : VisitorOperateTypeEnum.REJECT.getCode();
        VisitorOperateLog log = VisitorOperateLog.builder()
                .recordId(qo.getId())
                .type(operateType)
                .userid(SecurityContextHolder.getUserId())
                .name(visitorRecord.getAuditorName()).build();
        //新增操作记录
        AsyncManager.me().execute(AsyncFactory.addVisitorOperateLog(log));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelInvitation(Long id) {
        //查询记录
        VisitorRecord visitorRecord = getById(id);
        if (visitorRecord == null) {
            throw ExceptionCodeEnum.VISITOR_RECORD_NOT_EXIST.toServiceException();
        }

        if (VisitorRecordStatusEnum.APPROVED.getCode() != visitorRecord.getStatus()) {
            throw ExceptionCodeEnum.VISITOR_RECORD_ONLY_CANCEL_APPROVED.toServiceException();
        }

        //取消邀约
        updateRecordStatus(id, VisitorRecordStatusEnum.CANCELLED.getCode());

        VisitorOperateLog log = VisitorOperateLog.builder()
                .recordId(id)
                .type(VisitorOperateTypeEnum.CANCEL_INVITATION.getCode())
                .userid(SecurityContextHolder.getUserId())
                .name(visitorRecord.getAuditorName()).build();
        //新增操作记录
        AsyncManager.me().execute(AsyncFactory.addVisitorOperateLog(log));
    }

    @Override
    public void exportRecord(VisitorRecordPageQo pageQo, HttpServletResponse response) throws IOException {
        //不分页查询
        pageQo.setPage(1L);
        pageQo.setSize(Long.MAX_VALUE);
        //查询访客记录
        IPage<VisitorRecordVo> page = pageRecordVo(pageQo);
        List<VisitorRecordVo> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return;
        }

        List<VisitorRecordExportDto> exportDtoList = VisitorRecordConvert.INSTANCE.toDto(records);
        //导出Excel
        ExcelUtils.exportExcel(response, exportDtoList, "访客记录");
    }

    @Override
    public AppointQRcodeVO getAppointmentQRcodeInfo() {

        // 审核端当前使用教师的学工号
        String userId = SecurityContextHolder.getUserId();

        // 当前用户的基本信息
        CpUser user = userService.getById(userId);

        return AppointQRcodeVO.builder()
                .miniAppUrl(sysConfigService.selectConfigByKey("visitor.miniapp.access.url"))
                .userPhoneNumber(user.getMobile())
                .userName(user.getName())
                .expireDate(DateUtil.offsetDay(new Date(), 3))
                .build();
    }

    /**
     * 更新访客记录状态
     *
     * @param id     访客记录id
     * @param status 状态
     */
    private void updateRecordStatus(Long id, Integer status) {
        LambdaUpdateWrapper<VisitorRecord> updateWrapper = Wrappers.lambdaUpdate(VisitorRecord.class);
        updateWrapper.set(VisitorRecord::getStatus, status)
                .eq(VisitorRecord::getId, id);
        super.update(updateWrapper);

        commonExecutor.execute(() -> {
            try {
                //发送访客预约审核进度通知
                sendVisitorRecordProcessNotice(id);
            } catch (RuntimeException e) {
                log.error("发送访客预约/邀约审核状态通知失败，原因：{}", e.getMessage());
            }
        });
    }

    /**
     * 发送访客预约/邀约审核进度通知
     *
     * @param id 访客记录id
     */
    private void sendVisitorRecordProcessNotice(Long id) {
        VisitorRecord visitorRecord = getById(id);
        String dateFormat = "yyyy年MM月dd日 HH:mm";
        //预约发送审批通过和拒绝的通知，通过小程序和短信发送
        if (visitorRecord.getType() == 1) {

            // 只处理审批通过和拒绝
            if (visitorRecord.getStatus() != 1 && visitorRecord.getStatus() != 2) {
                return;
            }
            // 获取访客信息
            VisitorInfo visitorInfo = visitorInfoService.getOne(new LambdaQueryWrapper<VisitorInfo>().eq(VisitorInfo::getMobile, visitorRecord.getMobile()));

            //通过访客预约小程序发送订阅消息
            wxMaService.switchover(sysConfigService.selectConfigByKey("visitor.miniprogram.appid"));
            WxMaSubscribeMessage message = WxMaSubscribeMessage.builder()
                    .templateId(sysConfigService.selectConfigByKey("visitor.approve.process.templateId"))
                    .toUser(visitorInfo.getOpenId())
                    .data(List.of(
                            new WxMaSubscribeMessage.MsgData("thing1", visitorRecord.getName()),
                            new WxMaSubscribeMessage.MsgData("thing2", visitorRecord.getIntervieweeName()),
                            new WxMaSubscribeMessage.MsgData("time3", DateUtil.format(visitorRecord.getStartTime(), DatePattern.NORM_DATETIME_FORMAT)),
                            new WxMaSubscribeMessage.MsgData("thing4", visitorRecord.getStatus() == 1 ? "已通过" : "已驳回")))
                    .miniprogramState(sysConfigService.selectConfigByKey("visitor.miniprogram.state"))
                    .lang("zh_CN")
                    .build();
            try {
                wxMaService.getMsgService().sendSubscribeMsg(message);
            } catch (WxErrorException e) {
                log.error(e.getMessage(), e);
            } finally {
                //清理ThreadLocal
                WxMaConfigHolder.remove();
            }
            // 通过短信发送通知
            SmsBlend smsBlend = SmsFactory.getSmsBlend();
            String content = String.format("亲爱的%s，您于%s访问%s的预约%s",
                    visitorRecord.getName(),
                    DateUtil.format(visitorRecord.getStartTime(), dateFormat),
                    visitorRecord.getIntervieweeName(),
                    visitorRecord.getStatus() == 1 ? "已通过" : "已驳回");
            smsBlend.sendMessage(visitorRecord.getMobile(), content);

        } else {
            //邀约发送审批成功和取消的通知， 只通过短信发送即可
            String contentFormat;
            if (visitorRecord.getStatus() == 1) {
                contentFormat = "尊敬的%s，您收到一条预约，您可于%s-%s入校";
            } else if (visitorRecord.getStatus() == 3) {
                contentFormat = "尊敬的%s，您的邀约已取消：%s-%s入校";
            } else {
                return;
            }
            SmsBlend smsBlend = SmsFactory.getSmsBlend();
            String content = String.format(contentFormat,
                    visitorRecord.getName(),
                    DateUtil.format(visitorRecord.getStartTime(), dateFormat),
                    DateUtil.format(visitorRecord.getEndTime(), dateFormat));
            smsBlend.sendMessage(visitorRecord.getMobile(), content);
        }
    }

    @Override
    public IPage<VisitorRecordVo> pageVisitMe(VisitorRecordAuditPageQo qo) {
        VisitorRecordPageQo pageQo = VisitorRecordConvert.INSTANCE.toQo(qo);

        //被访问人为当前用户
        pageQo.setIntervieweeUserid(SecurityContextHolder.getUserId());

        //分页查询访客记录
        return pageRecordVo(pageQo);
    }

    /**
     * 分页查询访客记录
     *
     * @param pageQo 分页查询入参
     * @return 分页查询结果
     */
    private IPage<VisitorRecordVo> pageRecordVo(VisitorRecordPageQo pageQo) {
        IPage<VisitorRecord> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        LambdaQueryWrapper<VisitorRecord> queryWrapper = Wrappers.lambdaQuery(VisitorRecord.class);

        //填充查询条件
        fillQueryCondition(pageQo, queryWrapper);

        //分页查询
        IPage<VisitorRecord> result = super.page(page, queryWrapper);
        if (CollUtil.isEmpty(result.getRecords())) {
            return new Page<>(pageQo.getPage(), pageQo.getSize());
        }

        //转换vo
        IPage<VisitorRecordVo> pageVo = result.convert(VisitorRecordConvert.INSTANCE::toVo);

        //填充字段信息
        fillField(pageVo.getRecords());

        return pageVo;
    }

    /**
     * 填充查询条件
     */
    private void fillQueryCondition(VisitorRecordPageQo pageQo, LambdaQueryWrapper<VisitorRecord> queryWrapper) {
        //访客手机号码
        if (StringUtils.isNotBlank(pageQo.getMobile())) {
            queryWrapper.like(VisitorRecord::getMobile, pageQo.getMobile());
        }
        //访客名称
        if (StringUtils.isNotBlank(pageQo.getName())) {
            queryWrapper.like(VisitorRecord::getName, pageQo.getName());
        }
        //被访问人姓名
        if (StringUtils.isNotBlank(pageQo.getIntervieweeName())) {
            queryWrapper.like(VisitorRecord::getIntervieweeName, pageQo.getIntervieweeName());
        }
        //被访问人手机号
        if (StringUtils.isNotBlank(pageQo.getIntervieweeMobile())) {
            queryWrapper.like(VisitorRecord::getIntervieweeMobile, pageQo.getIntervieweeMobile());
        }
        //类型
        if (pageQo.getType() != null) {
            queryWrapper.eq(VisitorRecord::getType, pageQo.getType());
        }
        //状态
        if (pageQo.getStatus() != null) {
            //待审核 状态为待审核且结束时间大于当前时间
            if (VisitorRecordStatusEnum.PENDING.getCode() == pageQo.getStatus()) {
                queryWrapper.eq(VisitorRecord::getStatus, VisitorRecordStatusEnum.PENDING.getCode())
                        .ge(VisitorRecord::getEndTime, DateUtil.date());
            } else if (VisitorRecordStatusEnum.INVALID.getCode() == pageQo.getStatus()) {
                //已失效 待审核且结束时间小于当前时间
                queryWrapper.eq(VisitorRecord::getStatus, VisitorRecordStatusEnum.PENDING.getCode())
                        .lt(VisitorRecord::getEndTime, DateUtil.date());
            } else {
                queryWrapper.eq(VisitorRecord::getStatus, pageQo.getStatus());
            }
        }
        //拜访单位id
        if (pageQo.getDeptId() != null) {
            queryWrapper.eq(VisitorRecord::getDeptId, pageQo.getDeptId());
        }
        //来访时间
        if (pageQo.getStartTimeBegin() != null && pageQo.getStartTimeEnd() != null) {
            queryWrapper.ge(VisitorRecord::getStartTime, pageQo.getStartTimeBegin());
            queryWrapper.lt(VisitorRecord::getStartTime, pageQo.getStartTimeEnd());
        }
        //离开时间
        if (pageQo.getEndTimeBegin() != null && pageQo.getEndTimeEnd() != null) {
            queryWrapper.ge(VisitorRecord::getEndTime, pageQo.getEndTimeBegin());
            queryWrapper.lt(VisitorRecord::getEndTime, pageQo.getEndTimeEnd());
        }
        //随行人员名称
        if (StringUtils.isNotBlank(pageQo.getEntourageName())) {
            queryWrapper.like(VisitorRecord::getEntourageName, pageQo.getEntourageName());
        }
        //被访人学工号
        if (StringUtils.isNotBlank(pageQo.getIntervieweeUserid())) {
            queryWrapper.eq(VisitorRecord::getIntervieweeUserid, pageQo.getIntervieweeUserid());
        }
        //审核人学工号
        if (StringUtils.isNotBlank(pageQo.getAuditorUserid())) {
            queryWrapper.eq(VisitorRecord::getAuditorUserid, pageQo.getAuditorUserid());
        }
        //是否审核
        if (pageQo.getIsAudit() != null) {
            //已审核 状态为待审核且结束时间小于当前时间 或 状态不为待审核
            if (pageQo.getIsAudit() == YnEnum.YES.getValue()) {
                queryWrapper.and(item ->
                        item.and(item1 -> item1.eq(VisitorRecord::getStatus, VisitorRecordStatusEnum.PENDING.getCode())
                                        .lt(VisitorRecord::getEndTime, DateUtil.date()))
                                .or(item2 -> item2.ne(VisitorRecord::getStatus, VisitorRecordStatusEnum.PENDING.getCode())));
            } else {
                //待审核 状态为待审核且结束时间大于等于当前时间
                queryWrapper.eq(VisitorRecord::getStatus, VisitorRecordStatusEnum.PENDING.getCode())
                        .ge(VisitorRecord::getEndTime, DateUtil.date());
            }
        }
        //访客/随行人名称
        if (StringUtils.isNotBlank(pageQo.getVisitorOrEntourageName())) {
            queryWrapper.and(item -> item.like(VisitorRecord::getName, pageQo.getVisitorOrEntourageName())
                    .or().like(VisitorRecord::getEntourageName, pageQo.getVisitorOrEntourageName()));
        }

        //按照来访时间倒序排序
        queryWrapper.orderByDesc(VisitorRecord::getStartTime);
    }

    /**
     * 填充字段信息
     *
     * @param recordVos 访客记录列表
     */
    private void fillField(List<VisitorRecordVo> recordVos) {
        Date now = DateUtil.date();
        for (VisitorRecordVo vo : recordVos) {
            //设置证件类型
            vo.setCertificateTypeDesc(EnumUtil.getFieldBy(VisitorCertificateTypeEnum::getMsg, VisitorCertificateTypeEnum::getCode, vo.getCertificateType()));
            //设置进校方式
            vo.setEnterTypeDesc(EnumUtil.getFieldBy(VisitorEnterTypeEnum::getMsg, VisitorEnterTypeEnum::getCode, vo.getEnterType()));
            //设置类型
            vo.setTypeDesc(EnumUtil.getFieldBy(VisitorRecordTypeEnum::getMsg, VisitorRecordTypeEnum::getCode, vo.getType()));
            //设置状态 待审核且结束时间小于当前时间 则设置状态为已失效
            if (VisitorRecordStatusEnum.PENDING.getCode() == vo.getStatus() && vo.getEndTime().before(now)) {
                vo.setStatus(VisitorRecordStatusEnum.INVALID.getCode());
                vo.setStatusDesc(VisitorRecordStatusEnum.INVALID.getMsg());
            } else {
                vo.setStatusDesc(EnumUtil.getFieldBy(VisitorRecordStatusEnum::getMsg, VisitorRecordStatusEnum::getCode, vo.getStatus()));
            }
        }
    }

    /**
     * 新增访客记录
     *
     * @param recordSaveQo 访客记录入参
     */
    private void saveRecord(VisitorRecordSaveQo recordSaveQo) {
        VisitorRecord visitorRecord = VisitorRecordConvert.INSTANCE.toEntity(recordSaveQo);
        List<VisitorEntourageQo> entourageQoList = recordSaveQo.getEntourageList();
        //设置随行人员数量、名称
        if (CollUtil.isNotEmpty(entourageQoList)) {
            visitorRecord.setEntourageCount(entourageQoList.size());
            visitorRecord.setEntourageName(entourageQoList.stream().map(VisitorEntourageQo::getName).collect(Collectors.joining(",")));
        }

        //新增访客记录
        super.save(visitorRecord);

        //新增随行人员
        if (CollUtil.isNotEmpty(entourageQoList)) {
            List<VisitorEntourage> entourageList = VisitorEntourageConvert.INSTANCE.toEntity(entourageQoList);
            //设置访客记录id
            entourageList.forEach(item -> item.setRecordId(visitorRecord.getId()));
            visitorEntourageService.saveBatch(entourageList);
        }

        //新增保存访客记录操作日志
        saveRecordOperateLog(visitorRecord);
    }

    /**
     * 新增访客记录操作日志
     *
     * @param visitorRecord 访客记录
     */
    private void saveRecordOperateLog(VisitorRecord visitorRecord) {
        //操作类型
        int operateType;
        //操作人
        String operateUserId;
        String operateName;
        if (VisitorRecordTypeEnum.APPOINTMENT.getCode() == visitorRecord.getType()) {
            operateType = VisitorOperateTypeEnum.SUBMIT_APPOINTMENT.getCode();
            //预约类型不存在userid
            operateUserId = null;
            operateName = visitorRecord.getName();
        } else {
            operateType = VisitorOperateTypeEnum.SUBMIT_INVITATION.getCode();
            //邀约类型的操作人为审核人
            operateUserId = SecurityContextHolder.getUserId();
            operateName = visitorRecord.getAuditorName();
        }

        VisitorOperateLog log = VisitorOperateLog.builder()
                .recordId(visitorRecord.getId())
                .type(operateType)
                .userid(operateUserId)
                .name(operateName).build();
        //新增操作记录
        AsyncManager.me().execute(AsyncFactory.addVisitorOperateLog(log));
    }

    /**
     * 发送审核通知
     *
     * @param visitRecord 访客记录
     */
    private void sendApprovalNotice(VisitorRecordSaveQo visitRecord) {

        Long visitorApproveAppId = Long.valueOf(sysConfigService.selectConfigByKey("visitor.approve.agentid"));
        MsgSaveQo msgSaveQo = new MsgSaveQo();
        msgSaveQo.setType(MsgTypeEnum.TEXT.value());
        msgSaveQo.setTitle("您有一条新的访客预约待审核");
        msgSaveQo.setDescription(String.format("尊敬的%s，%s提交了访客预约申请需要您的审批，请打开【企业微信-工作台-访客审核】进行审批", visitRecord.getAuditorName(), visitRecord.getName()));
        msgSaveQo.setAppId(visitorApproveAppId);
        AppDTO appInfo = appManageService.getAppInfo(visitorApproveAppId);
        msgSaveQo.setLinkUrl(appInfo.getHomeUrl());
        msgSaveQo.setAppName(appInfo.getAppName());
        msgSaveQo.setPublishType(MsgPublishTypeEnum.NOW_SEND.getCode());
        msgSaveQo.setPublisher(visitRecord.getName());
        msgSaveQo.setUserList(List.of(visitRecord.getAuditorUserid()));
        msgSaveQo.setPublishChannel(List.of(MsgChannelEnum.WEIXIN_CORP_CHANNEL.name(), MsgChannelEnum.SMS_CHANNEL.name()));
        msgSaveQo.setIsMustRead(YnEnum.YES.getValue());
        msgSaveQo.setSource(MsgSourceEnum.APP.getCode());
        msgDefEsService.saveMsg(msgSaveQo);
    }
}

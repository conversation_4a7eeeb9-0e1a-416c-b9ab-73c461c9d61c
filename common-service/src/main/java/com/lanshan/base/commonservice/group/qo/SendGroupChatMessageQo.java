package com.lanshan.base.commonservice.group.qo;

import lombok.Data;
import lombok.ToString;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.File;
import java.util.List;

/**
 * Description: new java files header
 * 发送群聊消息
 * <AUTHOR>
 * @version 1.0
 * date 2023-12-07 10:55
 */
@Data
@ToString
public class SendGroupChatMessageQo {

    /**
     * agentId应用ID
     */
    private String agentId;

    /**
     * 需要发送消息的群聊id列表
     */
    @NotEmpty(message = "群聊id不能为空")
    private List<String> chatIds;

    /**
     * 内容类型（1:文本 2:图片 3:文件）
     */
    @NotNull(message = "内容类型不能为空")
    private Integer type;

    /**
     * 文本内容
     */
    private String content;

    /**
     * 文件
     */
    private MultipartFile file;
}

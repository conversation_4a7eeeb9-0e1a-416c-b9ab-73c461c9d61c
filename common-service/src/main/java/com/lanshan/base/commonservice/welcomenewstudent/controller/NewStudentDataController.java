package com.lanshan.base.commonservice.welcomenewstudent.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.welcomenewstudent.converter.NewStudentDataConverter;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentDataVO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentStatVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;

/**
 * 新生信息表(NewStudentData)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("newStudentData")
@Api(tags = "新生信息表(NewStudentData)控制层", hidden = true)
public class NewStudentDataController {
    /**
     * 服务对象
     */
    @Resource
    private NewStudentDataService newStudentDataService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<NewStudentDataVO>> selectAll(Page<NewStudentDataVO> page, NewStudentDataVO vo) {
        QueryWrapper<NewStudentData> queryWrapper = new QueryWrapper<>(NewStudentDataConverter.INSTANCE.toEntity(vo));
        IPage<NewStudentData> pageData = this.newStudentDataService.page(page.convert(NewStudentDataConverter.INSTANCE::toEntity), queryWrapper);
        return Result.build(pageData.convert(NewStudentDataConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/selectOne/{id}")
    public Result<NewStudentDataVO> selectOne(@PathVariable Serializable id) {
        return Result.build(NewStudentDataConverter.INSTANCE.toVO(this.newStudentDataService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("/insert")
    public Result<Boolean> insert(@RequestBody NewStudentDataVO vo) {
        return Result.build(this.newStudentDataService.save(NewStudentDataConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody NewStudentDataVO vo) {
        return Result.build(this.newStudentDataService.updateById(NewStudentDataConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.newStudentDataService.removeByIds(idList));
    }

    @ApiOperation("导入新生信息")
    @PostMapping("/importData")
    public Result<Boolean> importData(MultipartFile file) {
        return Result.build(newStudentDataService.importData(file));
    }

    @ApiOperation("导出新生数据失败数据")
    @GetMapping("getImportNewStudentErrorData")
    public void getImportNewStudentErrorData(HttpServletResponse response) {
        newStudentDataService.exportErrorData(response);
    }

    @ApiOperation("获取新生统计信息")
    @GetMapping("/getNewStudentStat")
    public Result<List<NewStudentStatVO>> getNewStudentStat(String year) {
        return Result.build(newStudentDataService.getNewStudentStat(year));
    }

}


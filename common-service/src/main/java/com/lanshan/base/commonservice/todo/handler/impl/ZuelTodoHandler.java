package com.lanshan.base.commonservice.todo.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.JAXBUtil;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.base.api.enums.CompleteTypeEnum;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.enums.YnEnum;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.schooldata.zuel.constant.ZuelOpenApiConstant;
import com.lanshan.base.commonservice.schooldata.zuel.constant.ZuelRedisKeys;
import com.lanshan.base.commonservice.schooldata.zuel.entity.ZuelOATodoInfo;
import com.lanshan.base.commonservice.schooldata.zuel.properties.ZuelOaProperties;
import com.lanshan.base.commonservice.todo.dto.TodoUserDto;
import com.lanshan.base.commonservice.todo.entity.TodoDef;
import com.lanshan.base.commonservice.todo.entity.TodoDetail;
import com.lanshan.base.commonservice.todo.entity.TodoFlowInfo;
import com.lanshan.base.commonservice.todo.entity.TodoUserRelation;
import com.lanshan.base.commonservice.todo.enums.TodoCompleteModeEnum;
import com.lanshan.base.commonservice.todo.enums.TodoCreatorTypeEnum;
import com.lanshan.base.commonservice.todo.handler.TodoHandler;
import com.lanshan.base.commonservice.todo.mapper.TodoDefMapper;
import com.lanshan.base.commonservice.todo.service.TodoDefService;
import com.lanshan.base.commonservice.todo.service.TodoDetailService;
import com.lanshan.base.commonservice.todo.service.TodoFlowInfoService;
import com.lanshan.base.commonservice.todo.service.TodoUserRelationService;
import com.lanshan.base.starter.db.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 中南财大待办处理器
 */
@Slf4j
@Component
public class ZuelTodoHandler extends AbstractTodoHandler implements TodoHandler {

    @Resource
    private ZuelOaProperties zuelOaProperties;

    @Resource
    private RedisService redisService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TodoDefMapper todoDefMapper;

    @Resource
    private TodoFlowInfoService todoFlowInfoService;

    @Resource
    private UserService userService;

    @Resource
    private TodoDefService todoDefService;

    @Resource
    private TodoDetailService todoDetailService;

    @Resource
    private TodoUserRelationService todoUserRelationService;

    @Override
    public void syncTodoInfo() {

        //判断是否开启同步
        Boolean oaTodoSyncEnabled = zuelOaProperties.getOaTodoSyncEnabled();
        if (Boolean.FALSE.equals(oaTodoSyncEnabled)) {
            return;
        }

        String userId = SecurityContextHolder.getUserId();

        //判断距离上次同步是否超过同步时间间隔（分钟），超过则进行同步
        Long lastTime = redisService.getCacheObject(ZuelRedisKeys.ZUEL_TODO_OA_SYNC_LAST_TIME + ":" + userId);
        if (lastTime != null && (System.currentTimeMillis() - lastTime) / 1000 / 60 < zuelOaProperties.getOaTodoSyncInterval()) {
            return;
        }
        ZuelTodoHandler proxy = (ZuelTodoHandler) AopContext.currentProxy();
        //获取分布式锁
        RLock lock = redissonClient.getLock(ZuelRedisKeys.ZUEL_TODO_OA_SYNC_LOCK + ":" + userId);
        try {
            boolean lockResult = lock.tryLock(10, TimeUnit.MILLISECONDS);
            if (!lockResult) {
                log.error("获取待办OA同步锁失败");
                return;
            }

            // 查询OA待办列表 - 外部系统调用应在事务外
            List<ZuelOATodoInfo.ZuelOAItem> list = queryOATodo(userId);

            // 查询用户待办序列号列表
            List<TodoUserDto> todoUserDtoList = todoDefMapper.listUserTodoSerialNo(userId);

            // 业务逻辑处理 - 准备数据
            List<TodoFlowInfo> todoFlowInfoList = new ArrayList<>();
            List<TodoDef> todoDefList = new ArrayList<>();
            List<TodoDetail> todoDetailList = new ArrayList<>();
            List<TodoUserRelation> todoUserRelationList = new ArrayList<>();
            List<TodoUserDto> completeList = new ArrayList<>();

            if (CollUtil.isNotEmpty(todoUserDtoList)) {
                // 处理待办数据
                TodoUserDto last = todoUserDtoList.get(0);
                List<ZuelOATodoInfo.ZuelOAItem> newList = list.stream().filter(item ->
                        DateUtil.parse(item.getPubDate(), DatePattern.NORM_DATETIME_PATTERN).after(last.getCreateDate())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(newList)) {
                    processTodo(newList, todoFlowInfoList, todoDefList, todoDetailList, userId, todoUserRelationList);
                }

                // 不存在的转为已完成
                completeList = todoUserDtoList.stream().filter(item -> list.stream().noneMatch(newItem -> newItem.getId().equals(item.getSerialNo()))).collect(Collectors.toList());
            } else {
                // 无存量数据，则全部新增
                processTodo(list, todoFlowInfoList, todoDefList, todoDetailList, userId, todoUserRelationList);
            }

            // 事务操作 - 保存数据
            proxy.executeTodoDataSaveTransaction(todoFlowInfoList, todoDefList, todoDetailList, todoUserRelationList, completeList);

            // 更新同步时间
            redisService.setCacheObject(ZuelRedisKeys.ZUEL_TODO_OA_SYNC_LAST_TIME + ":" + userId, System.currentTimeMillis());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("同步待办信息被中断", e);
        } catch (Exception e) {
            log.error("【待办OA同步信息】同步失败：{}", e.getMessage(), e);
        } finally {
            // 解锁前检查当前线程是否持有该锁
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeTodoDataSaveTransaction(
            List<TodoFlowInfo> todoFlowInfoList,
            List<TodoDef> todoDefList,
            List<TodoDetail> todoDetailList,
            List<TodoUserRelation> todoUserRelationList,
            List<TodoUserDto> completeList) {

        // 完成待办
        if (CollUtil.isNotEmpty(completeList)) {
            completeTodo(completeList);
        }

        // 新增待办数据
        saveTodo(todoFlowInfoList, todoDefList, todoDetailList, todoUserRelationList);
    }


    /**
     * 完成待办
     */
    private void completeTodo(List<TodoUserDto> completeList) {
        //序列号列表
        List<String> serialNoList = completeList.stream().map(TodoUserDto::getSerialNo).collect(Collectors.toList());
        //查询待办流程
        LambdaQueryWrapper<TodoFlowInfo> todoFlowInfoQw = Wrappers.lambdaQuery(TodoFlowInfo.class);
        todoFlowInfoQw.in(TodoFlowInfo::getSerialNo, serialNoList);
        List<TodoFlowInfo> todoFlowInfoCompleteList = todoFlowInfoService.list(todoFlowInfoQw);

        //获取id列表
        List<Long> idList = todoFlowInfoCompleteList.stream().map(TodoFlowInfo::getId).collect(Collectors.toList());

        //更新待办用户关联（OA待办的相关表的id都一致)
        LambdaUpdateWrapper<TodoUserRelation> todoUserRelationUw = Wrappers.lambdaUpdate(TodoUserRelation.class);
        todoUserRelationUw.set(TodoUserRelation::getStatus, CompleteTypeEnum.COMPLETE.getCode());
        todoUserRelationUw.set(TodoUserRelation::getCompleteTime, new Date());
        todoUserRelationUw.eq(TodoUserRelation::getStatus, CompleteTypeEnum.INCOMPLETE.getCode());
        todoUserRelationUw.in(TodoUserRelation::getId, idList);
        todoUserRelationService.update(todoUserRelationUw);
    }

    /**
     * 新增待办
     */
    private void saveTodo(List<TodoFlowInfo> todoFlowInfoList, List<TodoDef> todoDefList, List<TodoDetail> todoDetailList, List<TodoUserRelation> todoUserRelationList) {
        //新增待办
        if (CollUtil.isNotEmpty(todoFlowInfoList)) {
            todoFlowInfoService.saveBatch(todoFlowInfoList);
        }
        if (CollUtil.isNotEmpty(todoDefList)) {
            todoDefService.saveBatch(todoDefList);
        }
        if (CollUtil.isNotEmpty(todoDetailList)) {
            todoDetailService.saveBatch(todoDetailList);
        }
        if (CollUtil.isNotEmpty(todoUserRelationList)) {
            todoUserRelationService.saveBatch(todoUserRelationList);
        }
    }

    /**
     * 处理待办
     */
    private void processTodo(List<ZuelOATodoInfo.ZuelOAItem> newList, List<TodoFlowInfo> todoFlowInfoList, List<TodoDef> todoDefList, List<TodoDetail> todoDetailList, String userId, List<TodoUserRelation> todoUserRelationList) {
        for (ZuelOATodoInfo.ZuelOAItem oaItem : newList) {
            //发布时间
            Date pubDate = DateUtil.parse(oaItem.getPubDate(), DatePattern.NORM_DATETIME_PATTERN);
            //OA系统应用ID
            Long oaAppId = zuelOaProperties.getAppId();
            //OA系统应用名称
            String oaAppName = zuelOaProperties.getAppName();
            //生成id
            long id = IdGenerator.generateId();
            //结束时间默认设为2100-01-01
            Date endDate = DateUtil.parse("2100-01-01");

            //创建待办流程
            TodoFlowInfo todoFlowInfo = new TodoFlowInfo();
            todoFlowInfo.setId(id);
            todoFlowInfo.setAppId(oaAppId);
            todoFlowInfo.setAppName(oaAppName);
            todoFlowInfo.setSerialNo(oaItem.getId());
            todoFlowInfo.setName(oaItem.getTitle());
            todoFlowInfo.setDescription(oaItem.getDescription());
            todoFlowInfo.setSubmitterId(String.valueOf(oaAppId));
            todoFlowInfo.setSubmitterName(oaItem.getAuthor());

            //设置待办类型 取出标题【】里面的内容作为类型
            if (StringUtils.isNotBlank(oaItem.getTitle())) {
                String patternString = "【(.*?)】";
                Pattern pattern = Pattern.compile(patternString);
                Matcher matcher = pattern.matcher(oaItem.getTitle());
                // 找到第一个匹配就停止
                if (matcher.find()) {
                    todoFlowInfo.setType(matcher.group(1));
                }
            }
            todoFlowInfoList.add(todoFlowInfo);

            //创建待办定义
            TodoDef todoDef = new TodoDef();
            todoDef.setId(id);
            todoDef.setName(oaItem.getTitle());
            todoDef.setDescription(oaItem.getDescription());
            todoDef.setCompleteMode(Collections.singletonList(TodoCompleteModeEnum.AUTO.getCode()));
            todoDef.setStartTime(pubDate);
            todoDef.setEndTime(endDate);
            todoDef.setLinkUrl(oaItem.getMlink());
            todoDef.setLinkUrlPc(oaItem.getLink());
            //设为OA系统应用
            todoDef.setCreator(String.valueOf(oaAppId));
            todoDef.setCreatorName(oaAppName);
            todoDef.setCreatorType(TodoCreatorTypeEnum.APP.getCode());
            todoDef.setCreateDate(pubDate);
            todoDef.setUserCount(1);
            todoDef.setIsFlow(YnEnum.YES.getValue());
            todoDef.setFlowId(id);
            todoDef.setNodeId(1L);
            todoDefList.add(todoDef);

            //创建待办明细
            TodoDetail todoDetail = new TodoDetail();
            todoDetail.setId(id);
            todoDetail.setTodoDefId(id);
            todoDetail.setStartTime(pubDate);
            todoDetail.setEndTime(endDate);
            todoDetailList.add(todoDetail);

            //创建待办用户关联
            TodoUserRelation todoUserRelation = new TodoUserRelation();
            todoUserRelation.setId(id);
            todoUserRelation.setTodoDefId(id);
            todoUserRelation.setTodoDetailId(id);
            todoUserRelation.setUserid(userId);
            //查询名称
            CpUser user = userService.getById(userId);
            todoUserRelation.setName(Optional.ofNullable(user).map(CpUser::getName).orElse(null));
            todoUserRelationList.add(todoUserRelation);
        }
    }

    /**
     * 查询OA待办列表
     */
    private List<ZuelOATodoInfo.ZuelOAItem>  queryOATodo(String userId) {
        List<ZuelOATodoInfo.ZuelOAItem> list = new ArrayList<>();

        int totalPage;
        int page = 1;
        //获取OA待办列表
        do {
            String url = zuelOaProperties.getOaTodoUrl();
            int pageSize = zuelOaProperties.getOaTodoPageSize();
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put(ZuelOpenApiConstant.OA_TODO_ID, 1); //1为待办文件列表
            queryMap.put(ZuelOpenApiConstant.OA_TODO_UID, userId);
            queryMap.put(ZuelOpenApiConstant.OA_TODO_CI, page);
            queryMap.put(ZuelOpenApiConstant.OA_TODO_PS, pageSize);
            ZuelOATodoInfo todoInfo;
            try {
                String xml = HttpUtil.get(url, queryMap);
                //去除空格
                xml = CharSequenceUtil.trim(xml);
                todoInfo = JAXBUtil.xmlToBean(xml, ZuelOATodoInfo.class);
            } catch (Exception e) {
                //查询失败
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("【待办OA同步信息】用户【" + userId + "】获取第 " + page + " 页数据失败：" + e.getMessage()).toServiceException();
            }
            List<ZuelOATodoInfo.ZuelOAItem> item = todoInfo.getChannel().getItem();
            if (CollUtil.isEmpty(item)) {
                log.info("【待办OA同步信息】用户【{}】获取数据为空", userId);
                break;
            }

            list.addAll(item);

            log.info("【待办OA同步信息】用户【{}】获取第{}页数据成功，当前总共获取{}条数据", userId, page, list.size());

            page++;

            //获取总条数
            int totalCount = todoInfo.getChannel().getPagecount();
            //获取总页数
            totalPage = totalCount / pageSize;
            if (totalCount % pageSize != 0) {
                totalPage++;
            }
        } while (page <= totalPage);

        return list;
    }
}

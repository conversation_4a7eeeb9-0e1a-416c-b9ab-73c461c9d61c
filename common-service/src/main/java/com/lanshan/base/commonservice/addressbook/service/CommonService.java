package com.lanshan.base.commonservice.addressbook.service;

import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.qo.user.*;
import com.lanshan.base.commonservice.addressbook.entity.CpUserOperateBindMobile;

import java.util.List;

/**
 * 通讯录通用服务
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
public interface CommonService {

    /**
     * 通讯录操作表定时任务
     */
    void addressbookOperate();

    /**
     * 企微备份
     */
    void cpBak();

    /**
     * 清空企微备份
     */
    void cpBakRemove();

    /**
     * 企微恢复
     */
    void cpRecover();

    /**
     * 应用新组织架构 将新组织架构移动到根目录下
     */
    void applyNewDept();

    /**
     * 通讯录同步定时任务
     */
    void addressbookSync();

    /**
     * 保存用户
     *
     * @param qo
     */
    void saveUser(UserSaveSimpleQo qo);

    /**
     * 批量保存用户
     *
     * @param qoList
     */
    void batchSaveUser(List<UserSaveSimpleQo> qoList);

    /**
     * 保存部门
     *
     * @param qo
     */
    void saveDepartment(DepartmentSaveQo qo) throws ServiceException;

    /**
     * 批量保存部门
     *
     * @param qoList
     */
    void batchSaveDepartment(List<DepartmentSaveQo> qoList);

    /**
     * 保存标签
     *
     * @param qo
     */
    void saveTag(TagSaveQo qo);

    /**
     * 批量保存标签
     *
     * @param qoList
     */
    void batchSaveTag(List<TagSaveQo> qoList);

    /**
     * 批量新增用户部门关系
     *
     * @param list 用户部门关系
     */
    void batchSaveUserDeptRelation(List<UserDeptRelationQo> list);

    /**
     * 批量新增用户标签关系
     *
     * @param list 用户标签关系
     */
    void batchSaveUserTagRelation(List<UserTagRelationQo> list);

    /**
     * 批量新增部门标签关系
     *
     * @param list 部门标签关系
     */
    void batchSaveDeptTagRelation(List<DeptTagRelationQo> list);

    /**
     * 用户敏感信息同步定时任务
     */
    void sensitiveInfoSync();

    /**
     * 用户id绑定手机号
     *
     * @param userid 用户id
     * @param mobile 手机号
     */
    void useridBindMobile(String userid, String mobile);

    /**
     * 解绑用户
     *
     * @param userid 用户id
     */
    void unbindUser(String userid);

    /**
     * 用户是否可以绑定
     *
     * @param userid 用户id
     */
    CpUserOperateBindMobile userCanBind(String userid);

    /**
     * 用户绑定手机号-测试企微
     *
     * @param userid
     * @param mobile
     */
    void useridBindMobileTestCp(String userid, String mobile);

    /**
     * 新增通讯录信息 包含用户、部门、标签
     *
     * @param qo
     */
    void addAddressBookInfo(AddAddressBookQo qo);

    /**
     * 外部人员加入
     *
     * @param qo
     */
    void externalJoin(ExternalJoinQo qo);

    /**
     * 删除指定缓存
     * @param key 缓存key
     */
    void delCache(String key);

    /**
     * 自动删除标签
     */
    void autoDelTag();

    /**
     * 批量部门下的用户,如果为空则删除二次验证部门下的用户
     */
    void batchDelDeptUser(String deptId);
}

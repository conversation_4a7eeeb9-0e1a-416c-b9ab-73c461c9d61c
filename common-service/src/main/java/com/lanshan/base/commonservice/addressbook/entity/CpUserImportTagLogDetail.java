package com.lanshan.base.commonservice.addressbook.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString
@EqualsAndHashCode(callSuper = true)
public class CpUserImportTagLogDetail extends Model<CpUserImportTagLogDetail> {

    private Long id;

    /**
     * 所属日志
     */
    private Long logId;

    /**
     * 学号
     */
    private String userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 标签 &分隔
     */
    private String tags;
    /**
     * 成功标签 &分隔
     */
    private String successTags;
    /**
     * 失败标签 &分隔
     */
    private String failTags;

    /**
     * 是否有错误
     */
    private Boolean hasError;

    /**
     * 错误信息
     */
    private String errorMessage;
}

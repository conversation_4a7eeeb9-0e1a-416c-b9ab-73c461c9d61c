package com.lanshan.base.commonservice.group.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;

/**
 * Description: new java files header
 * 修改群聊信息
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/13 17:20
 */
@ApiModel(value = "修改群聊信息QO")
@Data
@ToString
public class UpdateGroupChatQo {

    /**
     * 群聊id
     */
    @NotEmpty(message = "群聊id不能为空")
    @ApiModelProperty(value = "群聊id不能为空")
    private String chatId;

    /**
     * agentId应用ID
     */
    @ApiModelProperty(value = "agentId不能为空")
    private String agentId;

    /**
     * 群聊名称
     */
    @ApiModelProperty(value = "群聊名称")
    private String name;

    /**
     * 群主ID
     */
    @ApiModelProperty(value = "群主ID")
    private String owner;

    /**
     * 群主名称
     */
    @ApiModelProperty(value = "群主名称")
    private String ownerName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}

package com.lanshan.base.commonservice.welcomenewstudent.util;

import com.lanshan.base.commonservice.system.service.ISysConfigService;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import me.chanjar.weixin.cp.api.WxCpService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class WxCpServiceUtil {

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private WxCpServiceFactory wxcpsServiceFactory;

    public WxCpService getWelcomeWxCpService() {
        String agentId = sysConfigService.selectConfigByKey("welcome.new.stu.agentId");
        String corpId = sysConfigService.selectConfigByKey("corpId");
        return wxcpsServiceFactory.get(corpId, agentId);
    }

    public WxCpService getDefaultWxCpService() {
        return wxcpsServiceFactory.getDefaultService();
    }

    public WxCpService getWxCpService(String corpId, String agentId) {
        return wxcpsServiceFactory.get(corpId, agentId);
    }
}

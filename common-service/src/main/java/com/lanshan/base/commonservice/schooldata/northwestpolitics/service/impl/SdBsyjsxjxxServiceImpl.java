package com.lanshan.base.commonservice.schooldata.northwestpolitics.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dto.DoctorStatusInfoDTO;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.po.SdBsyjsxjxx;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.dao.SdBsyjsxjxxMapper;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.service.SdBsyjsxjxxService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.util.NorthwestPoliticsUtil;
import com.lanshan.base.commonservice.schooldata.northwestpolitics.util.SM4Util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 博士研究生学籍信息表 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-26
 */

@Slf4j
@Service
public class SdBsyjsxjxxServiceImpl extends ServiceImpl<SdBsyjsxjxxMapper, SdBsyjsxjxx> implements SdBsyjsxjxxService {

    @Resource
    private SdBsyjsxjxxMapper sdBsyjsxjxxMapper;

    @Override
    public void syncBsyjsxjxx() {
        List<DoctorStatusInfoDTO> records = NorthwestPoliticsUtil.getDoctorStatusInfo(null);
        if (CollUtil.isEmpty(records)){
            log.info("博士研究生学籍信息为空，同步终止");
            return;
        }
        sdBsyjsxjxxMapper.truncate();
        List<SdBsyjsxjxx> recordList = BeanUtil.copyToList(records, SdBsyjsxjxx.class);
        SdBsyjsxjxxServiceImpl proxy = (SdBsyjsxjxxServiceImpl)AopContext.currentProxy();
        proxy.insertBatch(recordList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertBatch(List<SdBsyjsxjxx> records){
        List<List<SdBsyjsxjxx>> batchList = CollUtil.split(records, 2000);
        for (List<SdBsyjsxjxx> batch : batchList) {
            sdBsyjsxjxxMapper.insertBatch(batch);
        }
    }

}

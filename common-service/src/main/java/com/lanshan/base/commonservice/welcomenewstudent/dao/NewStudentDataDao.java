package com.lanshan.base.commonservice.welcomenewstudent.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentStatVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 新生信息表(NewStudentData)表数据库访问层
 *
 * <AUTHOR>
 */
public interface NewStudentDataDao extends BaseMapper<NewStudentData> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<NewStudentData> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<NewStudentData> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<NewStudentData> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<NewStudentData> entities);

    /**
     * 获取新生统计信息
     *
     * @return 新生统计信息
     */
    List<NewStudentStatVO> getNewStudentStat(String year);
}


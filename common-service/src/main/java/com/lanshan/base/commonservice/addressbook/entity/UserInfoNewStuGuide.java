package com.lanshan.base.commonservice.addressbook.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 新生指南表(UserInfoNewStuGuide)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class UserInfoNewStuGuide extends Model<UserInfoNewStuGuide> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
    /**
     * 状态 true 启用，false 禁用
     */
    private Boolean status;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建人名称
     */
    private String creatorName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}


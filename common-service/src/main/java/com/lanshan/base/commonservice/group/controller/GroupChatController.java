package com.lanshan.base.commonservice.group.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.annotation.RequiresPermissions;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.group.qo.*;
import com.lanshan.base.commonservice.group.service.GroupChatService;
import com.lanshan.base.commonservice.group.vo.ChatInfo;
import com.lanshan.base.commonservice.group.vo.ExistGroupChatVo;
import com.lanshan.base.commonservice.group.vo.GroupChatVO;
import com.lanshan.base.commonservice.log.OperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 群聊信息表(GroupChat)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RefreshScope
@RequestMapping("groupChat")
@Api(tags = "群聊信息表(GroupChat)控制层", hidden = true)
public class GroupChatController {
    /**
     * 服务对象
     */
    @Resource
    private GroupChatService groupChatService;

    @Value("${common.agent.config.common-agent-id}")
    private String agentId;

    /**
     * 分页查询所有数据
     *
     * @param vo 查询实体
     * @return 所有数据
     */
    @RequiresPermissions("group:chat:list")
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<GroupChatVO>> selectAll(GroupChatSearchQo vo) {
        IPage<GroupChatVO> pageData = this.groupChatService.pageGroupChat(vo);
        return Result.build(pageData);
    }

    /**
     * 通过成员userIds查询群聊
     *
     * @param qo 部门-标签-用户列表
     * @return 返回已存在的群聊列表
     */
    @RequiresPermissions("group:chat:list")
    @ApiOperation("通过成员userIds查询群聊")
    @PostMapping("/listExistGroupChat")
    public Result<ExistGroupChatVo> selectOne(@RequestBody ListExistGroupChatQo qo) {
        return Result.build(this.groupChatService.listExistGroupChat(qo));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param qo 主键
     * @return 单条数据
     */
    @RequiresPermissions("group:chat:getGroupChatById")
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/getGroupChatById")
    public Result<GroupChatVO> getGroupChatById(GetGroupChatQo qo) {
        return Result.build(this.groupChatService.getGroupChatById(qo));
    }


    /**
     * 创建群聊
     *
     * @param vo 实体对象
     * @return 创建群聊
     */
    @RequiresPermissions("group:chat:createGroup")
    @ApiOperation("创建群聊")
    @PostMapping("/createGroup")
    @OperateLog("创建群聊")
    public Result<String> createGroup(@RequestBody @Validated ChatInfo vo) throws WxErrorException {
        if (StringUtils.isEmpty(vo.getAgentId())) {
            vo.setAgentId(agentId);
        }
        return Result.build(this.groupChatService.createGroup(vo));
    }

    /**
     * 添加或移除成员
     *
     * @param vo 实体对象
     * @return 无返回值
     */
    @RequiresPermissions("group:chat:addOrRemoveMember")
    @PostMapping("/addOrRemoveMember")
    @ApiOperation("添加或移除成员")
    @OperateLog("添加或移除成员")
    public Result<Void> addOrRemoveMember(@RequestBody @Validated AddOrRemoveMemberQo vo) {
        if (StringUtils.isEmpty(vo.getAgentId())) {
            vo.setAgentId(agentId);
        }
        this.groupChatService.addOrRemoveMember(vo);
        return Result.build();
    }

    /**
     * 发送群消息
     *
     * @param vo 消息入参
     * @return 无返回值
     */
    @RequiresPermissions("group:chat:sendGroupChatMessage")
    @PostMapping("/sendGroupChatMessage")
    @ApiOperation("发送群消息")
    @OperateLog("发送群消息")
    public Result<Void> sendGroupChatMessage(SendGroupChatMessageQo vo) {
        if (StringUtils.isEmpty(vo.getAgentId())) {
            vo.setAgentId(agentId);
        }
        this.groupChatService.sendGroupChatMessage(vo);
        return Result.build();
    }

    /**
     * 修改群聊
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @RequiresPermissions("group:chat:edit")
    @PostMapping("/updateGroup")
    @ApiOperation("修改群聊")
    @OperateLog("修改群聊")
    public Result<Void> updateGroup(@RequestBody UpdateGroupChatQo vo) throws WxErrorException {
        if (StringUtils.isEmpty(vo.getAgentId())) {
            vo.setAgentId(agentId);
        }
        this.groupChatService.updateGroup(vo);
        return Result.build();
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @RequiresPermissions("group:chat:remove")
    @PostMapping("/delete")
    @ApiOperation("删除数据")
    @OperateLog("删除数据")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.groupChatService.removeByIds(idList));
    }
}


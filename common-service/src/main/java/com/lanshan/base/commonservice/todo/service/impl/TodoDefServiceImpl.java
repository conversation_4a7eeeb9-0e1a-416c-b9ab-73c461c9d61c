package com.lanshan.base.commonservice.todo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.*;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.lanshan.base.api.constant.SecurityConstant;
import com.lanshan.base.api.dto.app.AppDTO;
import com.lanshan.base.api.dto.message.TextMsgBody;
import com.lanshan.base.api.dto.message.TextcardMsgBody;
import com.lanshan.base.api.enums.*;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.feign.schedule.CalendarFeign;
import com.lanshan.base.api.feign.schedule.ScheduleFeign;
import com.lanshan.base.api.qo.schedule.CalendarSaveQo;
import com.lanshan.base.api.qo.schedule.ScheduleSaveQo;
import com.lanshan.base.api.qo.schedule.ScheduleUpdateQo;
import com.lanshan.base.api.qo.user.UserInfoPageQo;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.api.vo.user.UserInfoVo;
import com.lanshan.base.commonservice.access.dto.ApiAccessInfoDto;
import com.lanshan.base.commonservice.access.entity.AcApp;
import com.lanshan.base.commonservice.access.service.AcAppService;
import com.lanshan.base.commonservice.access.vo.AcAppVO;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.service.UserDepartmentRelationService;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.addressbook.service.UserTagRelationService;
import com.lanshan.base.commonservice.config.properties.AgentProperties;
import com.lanshan.base.commonservice.config.properties.TodoProperties;
import com.lanshan.base.commonservice.constant.AddressbookConstant;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.message.handler.ChannelHandlerFactory;
import com.lanshan.base.commonservice.system.service.ISysConfigService;
import com.lanshan.base.commonservice.task.TodoWorkbenchMetrics;
import com.lanshan.base.commonservice.todo.converter.TodoBeanConverter;
import com.lanshan.base.commonservice.todo.dto.*;
import com.lanshan.base.commonservice.todo.entity.*;
import com.lanshan.base.commonservice.todo.enums.*;
import com.lanshan.base.commonservice.todo.handler.TodoHandler;
import com.lanshan.base.commonservice.todo.handler.TodoHandlerFactory;
import com.lanshan.base.commonservice.todo.mapper.*;
import com.lanshan.base.commonservice.todo.qo.*;
import com.lanshan.base.commonservice.todo.service.*;
import com.lanshan.base.commonservice.todo.vo.*;
import com.lanshan.base.commonservice.workbench.dto.AppShowStyleDTO;
import com.lanshan.base.commonservice.workbench.entity.WbApp;
import com.lanshan.base.commonservice.workbench.entity.WbAppWxset;
import com.lanshan.base.commonservice.workbench.enums.AppShowStyleEnum;
import com.lanshan.base.commonservice.workbench.service.AppManageService;
import com.lanshan.base.commonservice.workbench.service.WbAppService;
import com.lanshan.base.commonservice.workbench.service.WbAppWxsetService;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpAgent;
import me.chanjar.weixin.cp.bean.WxCpAgentWorkBench;
import me.chanjar.weixin.cp.bean.workbench.WorkBenchKeyData;
import me.chanjar.weixin.cp.bean.workbench.WorkBenchList;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 待办定义表(TodoDef)服务实现类
 */
@Slf4j
@Service
@RefreshScope
public class TodoDefServiceImpl extends ServiceImpl<TodoDefMapper, TodoDef> implements TodoDefService {

    @Resource
    private TodoDefMapper todoDefMapper;

    @Resource
    private TodoUserRelationService todoUserRelationService;

    @Resource
    private TodoUserRelationMapper todoUserRelationMapper;

    @Resource
    private TodoWhitelistUserMapper todoWhitelistUserMapper;

    @Resource
    private UserService userService;

    @Resource
    private TodoDetailService todoDetailService;

    @Resource
    private CalendarFeign calendarFeign;

    @Resource
    private ScheduleFeign scheduleFeign;

    @Resource
    private AgentProperties agentProperties;

    @Resource
    private AppManageService appManageService;

    @Value("${todo-calendar-color}")
    private String todoCalendarColor;

    @Resource(name = "commonTaskExecutor")
    private ThreadPoolTaskExecutor commonExecutor;

    @Resource
    private TodoFlowInfoService todoFlowInfoService;

    @Resource
    private TodoFlowCopyUserService todoFlowCopyUserService;

    @Resource
    private AcAppService acAppService;

    @Resource
    private TodoFlowCopyUserMapper todoFlowCopyUserMapper;

    @Resource
    private ChannelHandlerFactory channelHandlerFactory;

    @Resource
    private WxCpServiceFactory wxCpServiceFactory;

    @Resource
    private TodoUserAppStatMapper todoUserAppStatMapper;

    @Resource
    private TodoWhitelistConfigMapper todoWhitelistConfigMapper;

    @Resource
    private WbAppService wbAppService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private TodoHandlerFactory todoHandlerFactory;

    @Resource
    private TodoProperties todoProperties;

    @Resource
    private ToDoStatService toDoStatService;

    @Lazy
    @Resource
    private TodoDefServiceImpl todoDefService;

    @Resource
    private TodoNodeOperateLogService todoNodeOperateLogService;

    @Resource
    private UserDepartmentRelationService userDepartmentRelationService;

    @Resource
    private UserTagRelationService userTagRelationService;

    @Resource
    private WbAppWxsetService wbAppWxsetService;

    @Resource
    private RedissonClient redissonClient;



    @Resource
    private TodoWorkbenchMetrics todoWorkbenchMetrics;

    @Value("${todo.todo-notice-flag:false}")
    private boolean todoNoticeFlag;

    private static final String TODAY_TODO = "今日待办";
    private static final String TODAY_COMPLETED_TODO = "今日已完成";
    private static final String TODAY_COMPLETE_RATE = "今日完成率";
    private static final String CURRENT_YEAR_COMPLETED = "今年已完成";
    private static final String TOTAL_COMPLETED = "所有已完成";
    private static final String LIMIT_ONE = "limit 1";
    private static final String WHITE_LIST_SIGN = "whiteList";

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public Long saveTodo(TodoSaveQo qo) {
        //新增待办定义
        TodoDef todoDef = addTodoDef(qo);

        //新增待办明细
        List<TodoDetail> todoDetailList = addTodoDetail(todoDef);

        //新增参与人
        addTodoUser(qo.getUseridList(), todoDetailList);

        //如果有提醒，则添加企微日历、日程
        if (YnEnum.YES.getValue() == qo.getIsRemind()) {
            // 使用线程池异步处理提醒
            commonExecutor.execute(() -> {
                try {
                    addRemind(qo.getUseridList(), todoDef);
                } catch (Exception e) {
                    log.error("添加提醒失败：{}", e.getMessage(), e);
                }
            });
        }

        return todoDef.getId();
    }

    /**
     * 新增待办定义
     *
     * @param qo 待办新增入参
     * @return 待办定义
     */
    private TodoDef addTodoDef(TodoSaveQo qo) {
        //参数校验
        commonParamCheck(qo, true);

        TodoDef todoDef = BeanUtil.copyProperties(qo, TodoDef.class);
        //设置参与人数
        todoDef.setUserCount(qo.getUseridList().size());
        //新增待办定义
        todoDefService.save(todoDef);
        return todoDef;
    }

    /**
     * 参数校验
     *
     * @param qo    待办入参
     * @param isAdd 是否新增
     */
    private void commonParamCheck(TodoBaseQo qo, boolean isAdd) {
        //校验名称
        if (isAdd) {
            //新增 名称不能为空且长度不能大于500
            if (StringUtils.isBlank(qo.getName()) || qo.getName().length() > 500) {
                throw ExceptionCodeEnum.TODO_NAME_ADD_INVALID.toServiceException();
            }
        } else {
            //修改 名称长度不能大于50
            if (StringUtils.isNotBlank(qo.getName()) && qo.getName().length() > 50) {
                throw ExceptionCodeEnum.TODO_NAME_UPDATE_INVALID.toServiceException();
            }
        }

        //描述长度不能大于500
        if (StringUtils.isNotBlank(qo.getDescription()) && qo.getDescription().length() > 500) {
            throw ExceptionCodeEnum.TODO_DESCRIPTION_INVALID.toServiceException();
        }

        //校验开始时间是否小于结束时间
        if (qo.getStartTime().compareTo(qo.getEndTime()) > 0) {
            throw ExceptionCodeEnum.TODO_START_AFTER_END.toServiceException();
        }

        //参与人不能为空
        if (CollUtil.isEmpty(qo.getUseridList())) {
            throw ExceptionCodeEnum.TODO_USER_LIST_IS_NULL.toServiceException();
        }

        //重复校验
        if (YnEnum.YES.getValue() == qo.getIsRepeat()) {
            repeatCheck(qo);
        }

        //如果为提醒
        if (YnEnum.YES.getValue() == qo.getIsRemind() && (CollUtil.isEmpty(qo.getRemindTimeDiffs()))) {
            throw ExceptionCodeEnum.TODO_REMIND_TIME_DIFFS_IS_NULL.toServiceException();
        }
    }

    /**
     * 新增重复校验
     *
     * @param qo 待办新增入参
     */
    private void repeatCheck(TodoBaseQo qo) {
        if (qo.getRepeatType() == null) {
            throw ExceptionCodeEnum.TODO_REPEAT_TYPE_IS_NULL.toServiceException();
        } else {
            //判断每周周几重复是否为空
            if (TodoRepeatTypeEnum.WEEK.getCode() == qo.getRepeatType() && CollUtil.isEmpty(qo.getRepeatDayOfWeek())) {
                throw ExceptionCodeEnum.TODO_REPEAT_DAY_OF_WEEK.toServiceException();
                //判断每月哪几天重复是否为空
            } else if (TodoRepeatTypeEnum.MONTH.getCode() == qo.getRepeatType() && (CollUtil.isEmpty(qo.getRepeatDayOfMonth()))) {
                throw ExceptionCodeEnum.TODO_REPEAT_DAY_OF_MONTH.toServiceException();
            }
        }
        //重复结束时间不能为空
        if (qo.getRepeatUntil() == null) {
            throw ExceptionCodeEnum.TODO_REPEAT_UTIL_IS_NULL.toServiceException();
        }
        //判断结束时间是否小于重复结束时间
        if ((qo.getEndTime().compareTo(qo.getRepeatUntil()) > 0)) {
            throw ExceptionCodeEnum.TODO_END_AFTER_REPEAT_UTIL.toServiceException();
        }
        //重复间隔不能为空
        if (qo.getRepeatInterval() == null) {
            throw ExceptionCodeEnum.TODO_REPEAT_INTERVAL_IS_NULL.toServiceException();
        }
    }

    /**
     * 新增提醒
     *
     * @param useridList 用户id列表
     * @param todoDef    待办定义
     */
    private void addRemind(List<String> useridList, TodoDef todoDef) {
        //如果新增类型是应用则查询应用id对应的企微应用，如果不存在则默认使用代办中心应用
        String agentId = agentProperties.getTodoAgentId();
        //设置企微应用为代办中心
        todoDef.setAgentId(agentId);
        if (TodoCreatorTypeEnum.APP.getCode() == todoDef.getCreatorType()) {
            //查询应用
            AppDTO appInfo = appManageService.getAppInfo(Long.valueOf(todoDef.getCreator()));
            Integer agentIdOfCreator = appInfo.getAgentId();
            //如果存在则使用该企微应用
            if (agentIdOfCreator != null) {
                agentId = String.valueOf(agentIdOfCreator);

                //设置待办定义创建人是企微应用
                todoDef.setAgentId(agentId);
            }
        }

        //新建日历
        CalendarSaveQo calendarSaveQo = CalendarSaveQo.builder()
                .summary(todoDef.getName())
                .description(todoDef.getDescription())
                .color(todoCalendarColor)
                .build();

        Result<String> calendarResult = calendarFeign.add(agentProperties.getCorpId(), agentId, calendarSaveQo);
        if (calendarResult.hasError()) {
            throw new ServiceException(String.format("新增企微日历失败：%s", calendarResult.getMsg()), calendarResult.getCode());
        }

        //新建日程
        //生成日程保存qo
        ScheduleSaveQo scheduleSaveQo = genScheduleSaveQo(calendarResult.getData(), todoDef, useridList);
        Result<String> scheduleResult = scheduleFeign.add(agentProperties.getCorpId(), agentId, scheduleSaveQo);
        if (scheduleResult.hasError()) {
            throw new ServiceException(String.format("新增企微日程失败：%s", scheduleResult.getMsg()), scheduleResult.getCode());
        }

        //更新待办定义的日历id
        todoDef.setCalId(calendarResult.getResult());
        todoDefService.updateById(todoDef);

        //更新待办明细的日程id
        LambdaUpdateWrapper<TodoDetail> detailWrapper = Wrappers.lambdaUpdate(TodoDetail.class);
        detailWrapper.set(TodoDetail::getScheduleId, scheduleResult.getResult())
                .eq(TodoDetail::getTodoDefId, todoDef.getId());
        todoDetailService.update(detailWrapper);
    }

    /**
     * 生成日程保存qo
     *
     * @param calId      日历id
     * @param todoDef    待办定义
     * @param useridList 用户id列表
     */
    private ScheduleSaveQo genScheduleSaveQo(String calId, TodoDef todoDef, List<String> useridList) {
        //日程保存qo
        ScheduleSaveQo scheduleSaveQo = ScheduleSaveQo.builder()
                .calId(calId)
                .summary(todoDef.getName())
                .description(todoDef.getDescription())
                .startTime(todoDef.getStartTime().getTime() / 1000)
                .endTime(todoDef.getEndTime().getTime() / 1000)
                .build();
        //参与人
        List<ScheduleSaveQo.Attendee> attendeeList = useridList.stream().map(item -> ScheduleSaveQo.Attendee.builder().userid(item).build()).collect(Collectors.toList());
        scheduleSaveQo.setAttendees(attendeeList);
        //提醒信息
        ScheduleSaveQo.Reminders reminders = ScheduleSaveQo.Reminders.builder()
                .isRemind(todoDef.getIsRemind())
                .remindTimeDiffs(todoDef.getRemindTimeDiffs())
                .build();
        //重复信息
        if (YnEnum.YES.getValue() == todoDef.getIsRepeat()) {
            reminders.setIsRepeat(YnEnum.YES.getValue());
            reminders.setIsRemind(YnEnum.YES.getValue());
            reminders.setIsCustomRepeat(YnEnum.YES.getValue());
            reminders.setRepeatType(todoDef.getRepeatType());
            reminders.setRepeatUntil(todoDef.getRepeatUntil().getTime() / 1000);
            reminders.setRepeatInterval(todoDef.getRepeatInterval());
            reminders.setRepeatDayOfWeek(todoDef.getRepeatDayOfWeek());
            reminders.setRepeatDayOfMonth(todoDef.getRepeatDayOfMonth());
        }
        scheduleSaveQo.setReminders(reminders);
        return scheduleSaveQo;
    }

    /**
     * 新增参与人
     *
     * @param useridList     用户列表
     * @param todoDetailList 待办明细列表
     */
    private void addTodoUser(List<String> useridList, List<TodoDetail> todoDetailList) {
        List<CpUser> cpUserList = userService.listByIds(useridList);
        //转为map
        Map<String, String> userMap = CollUtil.toMap(cpUserList, MapUtil.newHashMap(), CpUser::getUserid, CpUser::getName);

        //遍历构建参与人列表
        List<TodoUserRelation> userRelationList = new ArrayList<>();
        for (TodoDetail todoDetail : todoDetailList) {
            userRelationList.addAll(useridList.stream().map(item ->
                    TodoUserRelation.builder().todoDefId(todoDetail.getTodoDefId()).todoDetailId(todoDetail.getId()).userid(item).name(userMap.get(item)).build()).collect(Collectors.toList()));
        }

        //新增参与人
        todoUserRelationService.saveBatch(userRelationList);
    }

    /**
     * 新增待办明细
     *
     * @param todoDef 待办定义
     */
    private List<TodoDetail> addTodoDetail(TodoDef todoDef) {
        //待办明细
        List<TodoDetail> todoDetailList = new ArrayList<>();

        //开始时间、结束时间
        Date startTime = todoDef.getStartTime();
        Date endTime = todoDef.getEndTime();
        //开始时间与结束时间的差值
        long betweenSec = DateUtil.between(startTime, endTime, DateUnit.SECOND);

        //是否重复
        if (YnEnum.YES.getValue() == todoDef.getIsRepeat()) {
            TodoRepeatTypeEnum repeatTypeEnum = EnumUtil.getBy(TodoRepeatTypeEnum.class, item -> item.getCode() == todoDef.getRepeatType());
            //待办类型不存在
            if (repeatTypeEnum == null) {
                throw ExceptionCodeEnum.TODO_REPEAT_TYPE_NOT_EXIST.toServiceException();
            }

            //重复结束时间
            Date repeatUntil = todoDef.getRepeatUntil();
            if (repeatUntil == null) {
                throw ExceptionCodeEnum.TODO_REPEAT_UNTIL_IS_NULL.toServiceException();
            }

            //重复间隔 例如：repeatInterval指定为3，repeatType指定为每周重复，那么每3周重复一次；如果为空，默认为1；
            Integer repeatInterval = todoDef.getRepeatInterval();
            if (repeatInterval == null) {
                repeatInterval = 1;
            } else if (repeatInterval < 1) {
                throw ExceptionCodeEnum.TODO_REPEAT_INTERVAL_MUST_GE_1.toServiceException();
            }

            switch (repeatTypeEnum) {
                case DAY:
                    //处理日明细
                    processDayDetail(todoDef, startTime, (int) betweenSec, repeatUntil, todoDetailList, repeatInterval);
                    break;
                case WEEK:
                    //处理周明细
                    processWeekDetail(todoDef, startTime, (int) betweenSec, repeatUntil, todoDetailList, repeatInterval);
                    break;
                case MONTH:
                    //处理月明细
                    processMonthDetail(todoDef, startTime, (int) betweenSec, repeatUntil, todoDetailList, repeatInterval);
                    break;
            }

            //待办明细为空说明传入的时间参数有误
            if (CollUtil.isEmpty(todoDetailList)) {
                throw ExceptionCodeEnum.TODO_REPEAT_TIME_PARAM_ERROR.toServiceException();
            }

            //新增待办明细
            todoDetailService.saveBatch(todoDetailList);
            return todoDetailList;

        } else {
            //添加单个待办明细
            return addSingleTodo(todoDef);
        }
    }

    /**
     * 添加单个待办明细
     *
     * @param todoDef 待办定义
     */
    private List<TodoDetail> addSingleTodo(TodoDef todoDef) {
        List<TodoDetail> todoDetailList = new ArrayList<>();
        //添加待办明细
        todoDetailList.add(TodoDetail.builder().todoDefId(todoDef.getId()).startTime(todoDef.getStartTime()).endTime(todoDef.getEndTime()).build());
        todoDetailService.saveBatch(todoDetailList);
        return todoDetailList;
    }

    /**
     * 处理月明细
     *
     * @param todoDef        待办定义
     * @param startTime      开始时间
     * @param betweenSec     结束时间到开始时间的间隔秒
     * @param repeatUntil    重复结束时间
     * @param todoDetailList 待办明细列表
     * @param repeatInterval 间隔
     */
    private void processMonthDetail(TodoDef todoDef, Date startTime, int betweenSec, Date repeatUntil, List<TodoDetail> todoDetailList, int repeatInterval) {
        //每月几号重复
        List<Integer> repeatDayOfMonth = todoDef.getRepeatDayOfMonth();
        //如果未指定每月几号重复，则用开始时间来计算
        if (CollUtil.isEmpty(repeatDayOfMonth)) {
            //明细的开始时间、结束时间
            Date startTimeTemp = startTime;
            Date endTimeTemp = DateUtil.offsetSecond(startTimeTemp, betweenSec);

            //每次增加间隔天，直到结束时间超过重复结束时间
            while (DateUtil.compare(endTimeTemp, repeatUntil) <= 0) {
                TodoDetail detail = TodoDetail.builder().todoDefId(todoDef.getId()).startTime(startTimeTemp).endTime(endTimeTemp).build();
                todoDetailList.add(detail);

                //增加间隔周
                startTimeTemp = DateUtil.offsetMonth(startTimeTemp, repeatInterval);
                endTimeTemp = DateUtil.offsetSecond(startTimeTemp, betweenSec);
            }
        } else {
            //如果指定每月几号重复，则按指定日期计算
            //起始时间移动到对应月的1号
            Date first = DateUtil.offsetDay(startTime, -(DateUtil.dayOfMonth(startTime) - 1));

            //每次增加间隔月，直到结束时间的月超过重复结束时间的月
            while (DateUtil.compare(DateUtil.beginOfMonth(first), DateUtil.beginOfMonth(repeatUntil)) <= 0) {
                //循环指定对应月的几号
                for (Integer i : repeatDayOfMonth) {
                    //用当月1号来进行日期偏移，偏移量需减1
                    DateTime startTimeTemp = DateUtil.offsetDay(first, i - 1);
                    DateTime endTimeTemp = DateUtil.offsetSecond(startTimeTemp, betweenSec);
                    //需满足开始时间大于等于待办定义的开始时间，结束时间小于等于重复结束时间
                    if (DateUtil.compare(startTimeTemp, startTime) >= 0 && DateUtil.compare(endTimeTemp, repeatUntil) <= 0) {
                        TodoDetail detail = TodoDetail.builder().todoDefId(todoDef.getId()).startTime(startTimeTemp).endTime(endTimeTemp).build();
                        todoDetailList.add(detail);
                    }
                }
                //增加间隔月
                first = DateUtil.offsetMonth(first, repeatInterval);
            }
        }
    }


    /**
     * 处理周明细
     *
     * @param todoDef        待办定义
     * @param startTime      开始时间
     * @param betweenSec     结束时间到开始时间的间隔秒
     * @param repeatUntil    重复结束时间
     * @param todoDetailList 待办明细列表
     * @param repeatInterval 间隔
     */
    private void processWeekDetail(TodoDef todoDef, Date startTime, int betweenSec, Date repeatUntil, List<TodoDetail> todoDetailList, int repeatInterval) {
        //每周几重复
        List<Integer> repeatDayOfWeek = todoDef.getRepeatDayOfWeek();
        //如果未指定每周几重复，则用开始时间来计算
        if (CollUtil.isEmpty(repeatDayOfWeek)) {
            //明细的开始时间、结束时间
            Date startTimeTemp = startTime;
            Date endTimeTemp = DateUtil.offsetSecond(startTimeTemp, betweenSec);

            //每次增加间隔天，直到结束时间超过重复结束时间
            while (DateUtil.compare(endTimeTemp, repeatUntil) <= 0) {
                TodoDetail detail = TodoDetail.builder().todoDefId(todoDef.getId()).startTime(startTimeTemp).endTime(endTimeTemp).build();
                todoDetailList.add(detail);

                //增加间隔周
                startTimeTemp = DateUtil.offsetWeek(startTimeTemp, repeatInterval);
                endTimeTemp = DateUtil.offsetSecond(startTimeTemp, betweenSec);
            }

        } else {
            //如果指定每周几重复，则按指定日期计算
            processWeekDetailSpecified(todoDef, startTime, betweenSec, repeatUntil, todoDetailList, repeatInterval, repeatDayOfWeek);
        }
    }

    /**
     * 处理周明细-指定周几重复
     *
     * @param todoDef        待办定义
     * @param startTime      开始时间
     * @param betweenSec     结束时间到开始时间的间隔秒
     * @param repeatUntil    重复结束时间
     * @param todoDetailList 待办明细列表
     * @param repeatInterval 间隔
     */
    private void processWeekDetailSpecified(TodoDef todoDef, Date startTime, int betweenSec, Date repeatUntil, List<TodoDetail> todoDetailList, int repeatInterval, List<Integer> repeatDayOfWeek) {
        //获取开始时间对应周几，DateUtil.dayOfWeek：1表示周日，2表示周一；这里将周日设为7，其余减1
        int dayOfWeek = DateUtil.dayOfWeek(startTime) == 7 ? 1 : DateUtil.dayOfWeek(startTime) - 1;
        //开始时间移动到对应的周一
        Date monday = DateUtil.offsetDay(startTime, -(dayOfWeek - 1));

        //每次增加间隔周，直到结束时间的周超过重复结束时间的周
        while (DateUtil.compare(DateUtil.beginOfWeek(monday), DateUtil.beginOfWeek(repeatUntil)) <= 0) {
            //循环指定对应周的周几
            for (Integer i : repeatDayOfWeek) {
                //用当周周一来进行日期偏移，偏移量需减1
                DateTime startTimeTemp = DateUtil.offsetDay(monday, i - 1);
                DateTime endTimeTemp = DateUtil.offsetSecond(startTimeTemp, betweenSec);
                //需满足开始时间大于等于待办定义的开始时间，结束时间小于等于重复结束时间
                if (DateUtil.compare(startTimeTemp, startTime) >= 0 && DateUtil.compare(endTimeTemp, repeatUntil) <= 0) {
                    TodoDetail detail = TodoDetail.builder().todoDefId(todoDef.getId()).startTime(startTimeTemp).endTime(endTimeTemp).build();
                    todoDetailList.add(detail);
                }
            }
            //增加间隔周
            monday = DateUtil.offsetWeek(monday, repeatInterval);
        }
    }

    /**
     * 处理日明细
     *
     * @param todoDef        待办定义
     * @param startTime      开始时间
     * @param betweenSec     结束时间到开始时间的间隔秒
     * @param repeatUntil    重复结束时间
     * @param todoDetailList 待办明细列表
     * @param repeatInterval 间隔
     */
    private void processDayDetail(TodoDef todoDef, Date startTime, int betweenSec, Date repeatUntil, List<TodoDetail> todoDetailList, int repeatInterval) {
        //明细的开始时间、结束时间
        Date startTimeTemp = startTime;
        Date endTimeTemp = DateUtil.offsetSecond(startTimeTemp, betweenSec);

        //每次增加间隔天，直到结束时间超过重复结束时间
        while (DateUtil.compare(endTimeTemp, repeatUntil) <= 0) {
            TodoDetail detail = TodoDetail.builder().todoDefId(todoDef.getId()).startTime(startTimeTemp).endTime(endTimeTemp).build();
            todoDetailList.add(detail);

            //增加间隔天
            startTimeTemp = DateUtil.offsetDay(startTimeTemp, repeatInterval);
            endTimeTemp = DateUtil.offsetSecond(startTimeTemp, betweenSec);
        }
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public void updateTodo(TodoUpdateQo qo) {
        //查询旧待办定义
        TodoDef oldTodoDef = super.getById(qo.getId());

        //更新待办定义
        TodoDef todoDef = updateTodoDef(qo);

        //如果是只更新基础信息，更新完则直接返回
        if (YnEnum.YES.getValue() == qo.getUpdateBasic()) {
            return;
        }

        //更新待办明细及用户关联
        updateTodoDetailAndUserRelation(qo, oldTodoDef, todoDef);
    }

    /**
     * 更新待办定义
     *
     * @param qo 待办更新入参
     * @return 待办定义
     */
    private TodoDef updateTodoDef(TodoUpdateQo qo) {

        //待办定义
        TodoDef todoDef = BeanUtil.copyProperties(qo, TodoDef.class);

        //非基础更新下
        if (YnEnum.NO.getValue() == qo.getUpdateBasic()) {
            //参数校验
            commonParamCheck(qo, false);
            //设置参与人数
            todoDef.setUserCount(qo.getUseridList().size());
        }

        //更新待办定义
        todoDefService.updateById(todoDef);
        return todoDef;
    }

    /**
     * 更新待办明细
     * 如果有待办已经完成则不能进行更新，
     * 但满足以下条件还能更新（拆分更新）：
     * 1.新旧待办类型都为重复
     * 2.入参操作开始时间（该时间必须对应上某条明细）后的待办没有完成
     * 3.入参操作模式（1：指定当前待办修改 2：未来所有待办修改）不能为空
     *
     * @param qo         待办更新入参
     * @param oldTodoDef 旧待办定义
     * @param todoDef    新待办定义
     */
    private void updateTodoDetailAndUserRelation(TodoUpdateQo qo, TodoDef oldTodoDef, TodoDef todoDef) {

        //获取企微应用id
        String agentId = oldTodoDef.getAgentId();

        //新旧都为重复、入参操作模式不为空，才会涉及到待办明细切割
        if (YnEnum.YES.getValue() == oldTodoDef.getIsRepeat() && YnEnum.YES.getValue() == todoDef.getIsRepeat() && qo.getOpMode() != null) {
            //待办部分更新
            todoPartUpdate(qo, oldTodoDef, todoDef, agentId);

        } else {
            //待办全部更新
            todoAllUpdate(qo, oldTodoDef, todoDef, agentId);
        }
    }

    /**
     * 待办部分更新
     *
     * @param qo         待办更新入参
     * @param oldTodoDef 旧待办定义
     * @param todoDef    新待办定义
     * @param agentId    企微应用id
     */
    private void todoAllUpdate(TodoUpdateQo qo, TodoDef oldTodoDef, TodoDef todoDef, String agentId) {
        //查询待办是否有已完成
        LambdaQueryWrapper<TodoUserRelation> relationWrapper = Wrappers.lambdaQuery(TodoUserRelation.class);
        relationWrapper.eq(TodoUserRelation::getTodoDefId, qo.getId())
                .eq(TodoUserRelation::getStatus, CompleteTypeEnum.COMPLETE.getCode());
        boolean exists = todoUserRelationService.exists(relationWrapper);
        if (exists) {
            throw ExceptionCodeEnum.TODO_ALREADY_COMPLETE.toServiceException();
        }

        //清除旧的待办明细
        LambdaQueryWrapper<TodoDetail> detailWrapper = Wrappers.lambdaQuery(TodoDetail.class);
        detailWrapper.eq(TodoDetail::getTodoDefId, qo.getId());
        todoDetailService.remove(detailWrapper);

        //新增待办明细
        List<TodoDetail> todoDetailList = addTodoDetail(todoDef);

        //删除待办用户关联
        LambdaUpdateWrapper<TodoUserRelation> userRelationWrapper = Wrappers.lambdaUpdate(TodoUserRelation.class);
        detailWrapper.eq(TodoDetail::getTodoDefId, qo.getId());
        todoUserRelationService.remove(userRelationWrapper);

        //新增待办用户关联
        addTodoUser(qo.getUseridList(), todoDetailList);

        //如果旧待办存在提醒则删除旧日历
        if (StringUtils.isBlank(oldTodoDef.getCalId())) {
            Result<Object> result = calendarFeign.del(agentProperties.getCorpId(), agentId, oldTodoDef.getCalId());
            if (result.hasError()) {
                throw new ServiceException(String.format("更新待办-删除企微日历失败：%s", result.getMsg()), result.getCode());
            }
        }

        //如果有提醒，则添加企微日历、日程
        if (YnEnum.YES.getValue() == qo.getIsRemind()) {
            //新增提醒
            addRemind(qo.getUseridList(), todoDef);
        }
    }

    /**
     * 待办部分更新
     *
     * @param qo         待办更新入参
     * @param oldTodoDef 旧待办定义
     * @param todoDef    新待办定义
     * @param agentId    企微应用id
     */
    private void todoPartUpdate(TodoUpdateQo qo, TodoDef oldTodoDef, TodoDef todoDef, String agentId) {

        //操作开始时间校验
        opStartTimeCheck(qo);

        //处理旧的待办明细
        processOldDetail(qo);

        List<TodoDetail> todoDetailList;
        if (TodoOpModeEnum.CURRENT.getCode() == qo.getOpMode()) {
            //指定当前待办修改
            todoDetailList = addSingleTodo(todoDef);
        } else {
            //未来所有待办修改
            todoDetailList = addTodoDetail(todoDef);
        }

        //新增待办用户关联
        addTodoUser(qo.getUseridList(), todoDetailList);

        if (YnEnum.NO.getValue() == oldTodoDef.getIsRemind()) {
            //如果旧的没有提醒、新的有提醒，则新增
            if (YnEnum.YES.getValue() == todoDef.getIsRemind()) {
                //新增提醒
                addRemind(qo.getUseridList(), todoDef);
            }

        } else {
            //如果旧的有提醒、新的有提醒，则更新日程
            if (YnEnum.YES.getValue() == todoDef.getIsRemind()) {
                //日程保存qo
                ScheduleSaveQo scheduleSaveQo = genScheduleSaveQo(oldTodoDef.getCalId(), todoDef, qo.getUseridList());
                scheduleSaveQo.setScheduleId(qo.getScheduleId());
                //日程扩展qo
                ScheduleUpdateQo.ScheduleExtQo scheduleExtQo = ScheduleUpdateQo.ScheduleExtQo.builder()
                        .opStartTime(qo.getOpStartTime().getTime() / 1000)
                        .opMode(qo.getOpMode()).build();
                //日程更新qo
                ScheduleUpdateQo scheduleUpdateQo = ScheduleUpdateQo.builder().qo(scheduleSaveQo).extQo(scheduleExtQo).build();
                //日程更新
                Result<String> result = scheduleFeign.update(agentProperties.getCorpId(), agentId, scheduleUpdateQo);
                if (result.hasError()) {
                    throw new ServiceException(String.format("更新待办-更新企微日程失败：%s", result.getMsg()), result.getCode());
                }
                //更新待办明细的日程id
                LambdaUpdateWrapper<TodoDetail> detailWrapper = Wrappers.lambdaUpdate(TodoDetail.class);
                detailWrapper.set(TodoDetail::getScheduleId, result.getResult())
                        .in(TodoDetail::getId, todoDetailList.stream().map(TodoDetail::getId).collect(Collectors.toList()));
                todoDetailService.update(detailWrapper);

            } else {
                //如果旧的有提醒、新的没有提醒，则删除
                Result<Object> result = calendarFeign.del(agentProperties.getCorpId(), agentId, oldTodoDef.getCalId());
                if (result.hasError()) {
                    throw new ServiceException(String.format("更新待办-删除企微日历失败：%s", result.getMsg()), result.getCode());
                }
            }
        }
    }

    /**
     * 处理旧的待办明细
     *
     * @param qo 待办更新入参
     */
    private void processOldDetail(TodoUpdateQo qo) {
        //查询待办明细id wrapper
        LambdaQueryWrapper<TodoDetail> detailWrapper = Wrappers.lambdaQuery(TodoDetail.class);
        detailWrapper.select(TodoDetail::getId);
        detailWrapper.eq(TodoDetail::getTodoDefId, qo.getId());
        //判断操作模式
        if (TodoOpModeEnum.CURRENT.getCode() == qo.getOpMode()) {
            //指定当前待办修改 删除操作时间对应的待办明细
            detailWrapper.eq(TodoDetail::getStartTime, qo.getOpStartTime());

        } else if (TodoOpModeEnum.FUTURE.getCode() == qo.getOpMode()) {
            //未来所有待办修改 删除操作时间及之后的待办明细
            detailWrapper.ge(TodoDetail::getStartTime, qo.getOpStartTime());

        } else {
            throw ExceptionCodeEnum.TODO_OP_MODE_NOT_EXIST.toServiceException();
        }
        //查询待办明细id
        List<Long> detailIds = todoDetailService.listObjs(detailWrapper);

        //删除待办明细
        todoDetailService.removeBatchByIds(detailIds);

        //删除待办明细对应的用户关联
        LambdaQueryWrapper<TodoUserRelation> userRelationWrapper = Wrappers.lambdaQuery(TodoUserRelation.class);
        userRelationWrapper.in(TodoUserRelation::getTodoDetailId, detailIds);
        todoUserRelationService.remove(userRelationWrapper);
    }

    /**
     * 操作开始时间校验
     *
     * @param qo 待办更新入参
     */
    private void opStartTimeCheck(TodoUpdateQo qo) {
        //开始时间不得早于操作开始时间
        if (qo.getStartTime().before(qo.getOpStartTime())) {
            throw ExceptionCodeEnum.TODO_START_TIME_BEFORE_OP_START_TIME.toServiceException();
        }

        //查询入参操作开始时间后的待办是否完成
        ExistCompleteAfterStartTimeQo existCompleteAfterStartTimeQo = ExistCompleteAfterStartTimeQo.builder().todoDefId(qo.getId()).startTime(qo.getOpStartTime()).build();
        boolean todoComplete = todoUserRelationService.existCompleteAfterStartTime(existCompleteAfterStartTimeQo);
        if (todoComplete) {
            throw ExceptionCodeEnum.TODO_ALREADY_COMPLETE.toServiceException();
        }

        //查询开始时间是否对应某条重复待办明细的开始时间
        LambdaQueryWrapper<TodoDetail> opStartTimeDetailWrapper = Wrappers.lambdaQuery(TodoDetail.class);
        opStartTimeDetailWrapper.eq(TodoDetail::getTodoDefId, qo.getId());
        opStartTimeDetailWrapper.eq(TodoDetail::getStartTime, qo.getOpStartTime());
        TodoDetail todoDetail = todoDetailService.getOne(opStartTimeDetailWrapper);
        if (todoDetail == null) {
            throw ExceptionCodeEnum.TODO_OP_START_TIME_NOT_MATCH.toServiceException();
        }
        qo.setScheduleId(todoDetail.getScheduleId());
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public void removeTodo(Long id) {
        //查询待办定义
        TodoDef todoDef = super.getById(id);
        if (todoDef == null) {
            throw ExceptionCodeEnum.TODO_NOT_EXIST.toServiceException();
        }

        Integer isRemind = todoDef.getIsRemind();
        //如果待办需要提醒，则需要删除企微日历（同时也会删除对应的日程）
        if (YnEnum.YES.getValue() == isRemind) {
            //判断待办创建人是否为企微应用，如果不是则使用代办中心应用
            String agentId = todoDef.getAgentId();

            Result<Object> result = calendarFeign.del(agentProperties.getCorpId(), agentId, todoDef.getCalId());
            if (result.hasError()) {
                throw new ServiceException(String.format("删除企微日历失败：%s", result.getMsg()), result.getCode());
            }
        }

        //删除待办定义
        todoDefService.removeById(id);

        //删除待办明细
        LambdaQueryWrapper<TodoDetail> detailWrapper = Wrappers.lambdaQuery(TodoDetail.class);
        detailWrapper.eq(TodoDetail::getTodoDefId, id);
        todoDetailService.remove(detailWrapper);

        //删除待办用户关联
        LambdaUpdateWrapper<TodoUserRelation> userRelationWrapper = Wrappers.lambdaUpdate(TodoUserRelation.class);
        userRelationWrapper.eq(TodoUserRelation::getTodoDefId, id);
        List<TodoUserRelation> relationList = todoUserRelationService.list(userRelationWrapper);
        todoUserRelationService.remove(userRelationWrapper);

        Set<String> userIdSet = Optional.ofNullable(relationList).orElseGet(ArrayList::new).stream().map(TodoUserRelation::getUserid).collect(Collectors.toSet());
        cacheChangeUserIds(userIdSet);
    }

    @Override
    public void batchCompleteTodo(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }

        // 获取当前时间
        Date now = DateUtil.date();

        // 分批处理，每批最多100条，进一步减小批处理大小以降低单个事务的负担
        int batchSize = 100;
        List<List<Long>> batchIdLists = ListUtil.split(idList, batchSize);

        // 收集所有待办定义ID和用户ID，用于后续异步处理
        Set<Long> allDefIdSet = new HashSet<>();
        Set<String> allUserIdSet = new HashSet<>();

        // 使用全局锁防止大规模并发批量完成导致的死锁
        String globalLockKey = "TODO_BATCH_COMPLETE_GLOBAL_LOCK";
        RLock globalLock = redissonClient.getLock(globalLockKey);
        boolean globalLocked = false;

        try {
            // 尝试获取全局锁，最多等待500毫秒，锁自动释放时间30秒
            globalLocked = globalLock.tryLock(500, 30000, TimeUnit.MILLISECONDS);
            if (!globalLocked) {
                log.warn("获取批量完成待办全局锁失败，待办数量: {}", idList.size());
                throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
            }

            // 对每批数据单独处理，减小锁粒度
            int batchCount = 0;
            for (List<Long> batchIds : batchIdLists) {
                batchCount++;
                log.info("开始处理第{}/{}批待办完成，批次大小: {}", batchCount, batchIdLists.size(), batchIds.size());
                TodoDefServiceImpl currentProxy = (TodoDefServiceImpl) AopContext.currentProxy();
                // 处理单批待办完成
                currentProxy.processBatchTodos(batchIds, now, allDefIdSet, allUserIdSet);

                // 批次间增加小延迟，减少数据库压力
                if (batchCount < batchIdLists.size()) {
                    ThreadUtil.sleep(50, TimeUnit.MILLISECONDS);
                }
            }

            log.info("所有批次处理完成，共{}\u6279，总记录数: {}", batchIdLists.size(), idList.size());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("批量完成待办全局锁被中断，待办数量: {}", idList.size(), e);
            throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
        } catch (Exception e) {
            if (!(e instanceof ServiceException)) {
                log.error("批量完成待办失败，待办数量: {}", idList.size(), e);
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("批量完成待办失败");
            }
            throw e;
        } finally {
            // 确保锁被正确释放
            if (globalLocked && globalLock.isHeldByCurrentThread()) {
                globalLock.unlock();
            }
        }

        // 异步处理日历删除，将非关键路径操作从事务中剥离
        if (!allDefIdSet.isEmpty()) {
            asyncDeleteCalendars(new ArrayList<>(allDefIdSet));
        }

        // 异步缓存变更的用户ID
        if (CollUtil.isNotEmpty(allUserIdSet)) {
            commonExecutor.execute(() -> cacheChangeUserIds(allUserIdSet));
        }
    }

    /**
     * 处理单批待办完成
     * 每批数据单独加锁，减小锁粒度
     *
     * @param batchIds     批次ID列表
     * @param now          当前时间
     * @param allDefIdSet  所有待办定义ID集合（输出参数）
     * @param allUserIdSet 所有用户ID集合（输出参数）
     */
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 10)
    public void processBatchTodos(List<Long> batchIds, Date now, Set<Long> allDefIdSet, Set<String> allUserIdSet) {
        if (CollUtil.isEmpty(batchIds)) {
            return;
        }

        // 先查询待办关系信息，避免在锁内查询
        List<TodoUserRelation> relations = todoUserRelationService.list(
                Wrappers.lambdaQuery(TodoUserRelation.class)
                        .select(TodoUserRelation::getId, TodoUserRelation::getTodoDefId, TodoUserRelation::getUserid, TodoUserRelation::getStatus)
                        .in(TodoUserRelation::getId, batchIds));

        if (CollUtil.isEmpty(relations)) {
            log.warn("未找到待完成的待办关系记录，批次大小: {}", batchIds.size());
            return;
        }
        // 过滤出未完成的记录，避免重复更新
        List<TodoUserRelation> incompleteRelations = relations.stream()
                .filter(relation -> relation.getStatus() != CompleteTypeEnum.COMPLETE.getCode())
                .collect(Collectors.toList());

        if (incompleteRelations.isEmpty()) {
            log.info("批次中所有待办已经完成，无需更新，批次大小: {}", batchIds.size());

            // 收集待办定义ID和用户ID
            for (TodoUserRelation relation : relations) {
                allDefIdSet.add(relation.getTodoDefId());
                allUserIdSet.add(relation.getUserid());
            }
            return;
        }

        // 只更新未完成的记录ID
        batchIds = incompleteRelations.stream().map(TodoUserRelation::getId).collect(Collectors.toList());

        // 使用更细粒度的锁，只锁定当前批次
        String lockKey = "TODO_BATCH_COMPLETE_LOCK:" + batchIds.hashCode();
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;
        try {
            // 尝试获取锁，最多等待500毫秒，锁自动释放时间5秒（减小锁持有时间）
            locked = lock.tryLock(500, 5000, TimeUnit.MILLISECONDS);
            if (!locked) {
                log.warn("获取批量完成待办锁失败，待办数量: {}", batchIds.size());
                throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
            }

            // 完成待办
            todoUserRelationService.update(
                    Wrappers.<TodoUserRelation>lambdaUpdate()
                            .set(TodoUserRelation::getStatus, CompleteTypeEnum.COMPLETE.getCode())
                            .set(TodoUserRelation::getCompleteTime, now)
                            .in(TodoUserRelation::getId, batchIds)
            );



            // 收集待办定义ID和用户ID
            for (TodoUserRelation relation : relations) {
                allDefIdSet.add(relation.getTodoDefId());
                allUserIdSet.add(relation.getUserid());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取批量完成待办锁被中断，批次大小: {}", batchIds.size(), e);
            throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
        } catch (Exception e) {
            if (!(e instanceof ServiceException)) {
                log.error("批量完成待办失败，批次大小: {}", batchIds.size(), e);
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("批量完成待办失败");
            }
            throw e;
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 异步删除日历
     * 将非关键路径操作完全异步化，从事务中剥离
     *
     * @param defIdList 待办定义ID列表
     */
    private void asyncDeleteCalendars(List<Long> defIdList) {
        if (CollUtil.isEmpty(defIdList)) {
            return;
        }

        // 使用线程池异步执行，避免阻塞主流程
        commonExecutor.execute(() -> {
            try {
                // 查询其中已完成且有日历的代办定义
                List<TodoCompleteDefDto> defDtoList = todoDefMapper.listCompleteTodoDef(defIdList);
                if (CollUtil.isEmpty(defDtoList)) {
                    return;
                }

                // 拆分成更小的批次，每批最多10个，避免单个任务处理过多数据
                List<List<TodoCompleteDefDto>> split = ListUtil.split(defDtoList, 10);
                for (List<TodoCompleteDefDto> todoList : split) {
                    if (CollUtil.isEmpty(todoList)) {
                        continue;
                    }

                    // 删除日历
                    for (TodoCompleteDefDto defDto : todoList) {
                        try {
                            Result<Object> result = calendarFeign.del(agentProperties.getCorpId(), defDto.getAgentId(), defDto.getCalId());
                            if (result.hasError()) {
                                log.error("删除日历待办失败，待办id：{}，日历id：{}", defDto.getTodoDefId(), defDto.getCalId());
                            }
                        } catch (Exception e) {
                            log.error("删除日历待办异常，待办id：{}，日历id：{}", defDto.getTodoDefId(), defDto.getCalId(), e);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("异步删除日历失败，待办定义数量: {}", defIdList.size(), e);
            }
        });
    }

    @Override
    public IPage<TodoVo> pageTodo(TodoPageQo pageQo) {
        IPage<TodoVo> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        //获取当前用户
        String userId = SecurityContextHolder.getUserId();
        pageQo.setUserid(userId);

        List<TodoVo> todoList;

        //加入白名单校验逻辑
        if (StringUtils.isNotEmpty(pageQo.getAppId())) {
            boolean checkWhiteListResult = checkIsWhiteList(pageQo);
            if (checkWhiteListResult) {
//            page.setRecords(new ArrayList<TodoVo>());
//            page.setTotal(0);
                return page;
            }
        } else {
            if (StringUtils.isNotEmpty(pageQo.getWhiteListSign()) && WHITE_LIST_SIGN.equals(pageQo.getWhiteListSign())) {
//                存在白名单应用开启的状态，但是白名单一个用户都没有的情况吗?
                List<TodoWhitelistUser> whitelistUsers = todoWhitelistUserMapper.getWhiteUsers();
                if (CollectionUtil.isNotEmpty(whitelistUsers)) {
                    Map<Long, List> result = dealData(whitelistUsers);
                    List<String> blackAppListStr = getBlackList(result, pageQo.getUserid());
                    pageQo.setBlackListAppId(blackAppListStr);
                }

            }
        }
        //分页查询未完成待办
        if (CompleteTypeEnum.INCOMPLETE.getCode() == pageQo.getStatus()) {
            todoList = todoDefMapper.pageTodoIncomplete(page, pageQo);

        } else {
            //分页查询已完成待办
            todoList = todoDefMapper.pageTodoComplete(page, pageQo);
        }
        if (CollUtil.isEmpty(todoList)) {
            return page;
        }

        //填充字段信息 日程提醒
        fillField(todoList);

        page.setRecords(todoList);
        return page;
    }

    /**
     * 填充字段信息
     */
    private void fillField(List<TodoVo> todoList) {
        //获取应用id列表
        List<Long> appIdList = todoList.stream().filter(item -> TodoCreatorTypeEnum.APP.getCode() == item.getCreatorType()).map(item -> Long.parseLong(item.getCreator())).distinct().collect(Collectors.toList());
        Map<Long, WbApp> appMap = new HashMap<>();
        if (CollUtil.isNotEmpty(appIdList)) {
            //查询应用
            List<WbApp> wbApps = wbAppService.listByIds(appIdList);
            appMap = CollUtil.toMap(wbApps, null, WbApp::getId, item -> item);
        }

        for (TodoVo vo : todoList) {
            //设置日程提醒
            List<Integer> remindTimeDiffs = vo.getRemindTimeDiffs();
            if (CollUtil.isNotEmpty(remindTimeDiffs)) {
                List<String> timeDiffsDesc = remindTimeDiffs.stream().map(item ->
                        EnumUtil.getFieldBy(TodoRemindTimeDiffsEnum::getMsg, TodoRemindTimeDiffsEnum::getCode, item)).collect(Collectors.toList());
                vo.setRemindTimeDiffsDesc(timeDiffsDesc);
            }
            //设置重复信息
            if (YnEnum.YES.getValue() == vo.getIsRepeat()) {
                vo.setRepeatInfo(genRepeatInfo(vo.getRepeatType(), vo.getRepeatInterval(), vo.getRepeatUntil(), vo.getRepeatDayOfWeek(), vo.getRepeatDayOfMonth()));
            }
            //设置应用logo
            if (TodoCreatorTypeEnum.APP.getCode() == vo.getCreatorType()) {
                WbApp app = appMap.get(Long.parseLong(vo.getCreator()));
                if (app != null) {
                    vo.setAppLogoUrl(app.getLogoUrl());
                }
            }
        }
    }

    @Override
    public List<TodoVo> listOverdueTodo(TodoPageQo qo) {
        //获取当前用户
        String userId = SecurityContextHolder.getUserId();
        qo.setUserid(userId);

        //查询逾期待办
        List<TodoVo> voList = todoDefMapper.listOverdueTodo(qo);
        if (CollUtil.isEmpty(voList)) {
            return Collections.emptyList();
        }

        //填充字段信息 日程提醒
        fillField(voList);
        return voList;
    }

    @Override
    public IPage<TodoDefVo> pageTodoDef(TodoDefPageQo pageQo) {
        IPage<TodoDef> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        LambdaQueryWrapper<TodoDef> queryWrapper = Wrappers.lambdaQuery(TodoDef.class);

        //待办标题
        if (pageQo.getName() != null) {
            queryWrapper.like(TodoDef::getName, pageQo.getName());
        }
        //应用名称
        if (StringUtils.isNotEmpty(pageQo.getAppName())) {
            queryWrapper.like(TodoDef::getCreatorName, pageQo.getAppName());
            queryWrapper.eq(TodoDef::getCreatorType, TodoCreatorTypeEnum.APP.getCode());
        }
        //开始、结束时间
        if (pageQo.getStartTime() != null && pageQo.getEndTime() != null) {
            queryWrapper.ge(TodoDef::getStartTime, pageQo.getStartTime());
            queryWrapper.lt(TodoDef::getEndTime, DateUtil.offsetDay(pageQo.getEndTime(), 1));
        }
        //按照创建时间倒序排序
        queryWrapper.orderByDesc(TodoDef::getCreateDate);

        //分页查询
        IPage<TodoDef> result = super.page(page, queryWrapper);
        if (CollUtil.isEmpty(result.getRecords())) {
            return new Page<>(pageQo.getPage(), pageQo.getSize());
        }

        //转换vo
        IPage<TodoDefVo> pageVo = result.convert(item -> BeanUtil.copyProperties(item, TodoDefVo.class));
        //填充字段信息 是否重复、是否提醒、提醒、完成方式
        fillField(pageVo);
        return pageVo;
    }

    @Override
    public List<ExistTodoByMonthVo> existTodoByMonth(String yyyyMM, String whiteListSign) {
        //获取当前用户
        String userId = SecurityContextHolder.getUserId();
        //获取参数对应月份
        Date month = DateUtil.parse(yyyyMM, "yyyyMM");
        ExistTodoByMonthQo qo = ExistTodoByMonthQo.builder().userid(userId).month(month).build();
        if (StringUtils.isNotEmpty(whiteListSign) && WHITE_LIST_SIGN.equals(whiteListSign)) {
            List<TodoWhitelistUser> whitelistUsers = todoWhitelistUserMapper.getWhiteUsers();
            if (CollectionUtil.isNotEmpty(whitelistUsers)) {
                Map<Long, List> result = dealData(whitelistUsers);
                List<String> blackAppListStr = getBlackList(result, userId);
                qo.setBlackAppListStr(blackAppListStr);
            }

        }


        //根据月份查询有待办的日
        List<Integer> existDays = todoDefMapper.existTodoByMonth(qo);

        List<ExistTodoByMonthVo> voList = new ArrayList<>();
        //获取参数对应月份对应的日数
        int days = DateUtil.lengthOfMonth(DateUtil.month(month) + 1, DateUtil.isLeapYear(DateUtil.year(month)));
        //设置每一天是否有待办
        for (int i = 1; i <= days; i++) {
            ExistTodoByMonthVo vo = new ExistTodoByMonthVo();
            //设置日
            vo.setDay(i);
            //判断是否有待办
            if (existDays.contains(i)) {
                vo.setIsExist(YnEnum.YES.getValue());
            } else {
                vo.setIsExist(YnEnum.NO.getValue());
            }
            voList.add(vo);
        }

        return voList;
    }

    @Override
    public IPage<TodoUserVo> pageUser(TodoUserPageQo pageQo) {
        IPage<TodoUserVo> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        //分页查询参与人id列表
        List<String> useridList = todoUserRelationMapper.pageUser(page, pageQo);
        if (CollUtil.isEmpty(useridList)) {
            return page;
        }

        UserInfoPageQo qo = new UserInfoPageQo();
        //设置用户id列表
        qo.setUseridList(useridList);
        qo.setSize(Long.MAX_VALUE);

        //查询用户信息
        Page<UserInfoVo> voPage = userService.pageUserInfo(qo);
        List<UserInfoVo> records = voPage.getRecords();
        //转换vo
        List<TodoUserVo> voList = BeanUtil.copyToList(records, TodoUserVo.class);

        //设置vo记录
        page.setRecords(voList);
        return page;
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public void batchCancelCompleteTodo(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }

        // 使用分布式锁防止并发取消完成待办导致的死锁
        RLock lock = redissonClient.getLock("TODO_BATCH_CANCEL_COMPLETE_LOCK:" + idList.hashCode());
        boolean locked = false;
        try {
            // 尝试获取锁，最多等待500毫秒，锁自动释放时间10秒
            locked = lock.tryLock(500, 10000, TimeUnit.MILLISECONDS);
            if (!locked) {
                log.warn("获取批量取消完成待办锁失败，待办数量: {}", idList.size());
                throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
            }

            // 分批处理，每批最多500条
            int batchSize = 500;
            List<List<Long>> batchIdLists = ListUtil.split(idList, batchSize);

            // 收集所有用户ID
            Set<String> allUserIdSet = new HashSet<>();

            for (List<Long> batchIds : batchIdLists) {
                // 查询用户ID，用于后续缓存更新
                List<TodoUserRelation> relations = todoUserRelationService.list(
                        Wrappers.lambdaQuery(TodoUserRelation.class)
                                .select(TodoUserRelation::getUserid)
                                .in(TodoUserRelation::getId, batchIds));

                for (TodoUserRelation relation : relations) {
                    allUserIdSet.add(relation.getUserid());
                }

                // 取消完成待办
                LambdaUpdateWrapper<TodoUserRelation> updateWrapper = Wrappers.lambdaUpdate(TodoUserRelation.class);
                // 将完成时间置空
                updateWrapper.set(TodoUserRelation::getStatus, YnEnum.NO.getValue())
                        .set(TodoUserRelation::getCompleteTime, null)
                        .in(TodoUserRelation::getId, batchIds);
                todoUserRelationService.update(updateWrapper);
            }

            // 缓存变更的用户ID
            if (CollUtil.isNotEmpty(allUserIdSet)) {
                cacheChangeUserIds(allUserIdSet);
            }
        } catch (InterruptedException e) {
            log.error("获取批量取消完成待办锁被中断，待办数量: {}", idList.size(), e);
            Thread.currentThread().interrupt();
            throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
        } catch (Exception e) {
            if (!(e instanceof ServiceException)) {
                log.error("批量取消完成待办失败，待办数量: {}", idList.size(), e);
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("批量取消完成待办失败");
            }
            throw e;
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public void autoCompleteTodo(String startDateStr) {
        // 开始时间为空，则默认取当天开始时间
        Date startDate;
        if (StringUtils.isBlank(startDateStr)) {
            startDate = DateUtil.beginOfDay(new Date());
        } else {
            startDate = DateUtil.parse(startDateStr, DatePattern.NORM_DATE_PATTERN);
        }

        // 使用分布式锁防止并发自动完成待办导致的死锁
        RLock lock = redissonClient.getLock("TODO_AUTO_COMPLETE_LOCK:" + DateUtil.format(startDate, DatePattern.PURE_DATE_PATTERN));
        boolean locked = false;
        try {
            // 尝试获取锁，最多等待500毫秒，锁自动释放时间30秒
            locked = lock.tryLock(500, 30000, TimeUnit.MILLISECONDS);
            if (!locked) {
                log.warn("获取自动完成待办锁失败，日期: {}", DateUtil.format(startDate, DatePattern.NORM_DATE_PATTERN));
                return; // 如果获取锁失败，直接返回，不抛出异常，因为这是定时任务
            }

            // 获取自动完成的待办用户关联id，
            // 数据范围：1.结束时间在开始时间到当前时间内 2.完成模式包含到期自动完成 3.状态为未完成
            List<TodoAutoCompleteDto> dtoList = todoDefMapper.listAutoCompleteList(startDate);
            if (CollUtil.isNotEmpty(dtoList)) {
                log.info("找到{}\u6761需要自动完成的待办", dtoList.size());

                // 分批处理，每批最多500条
                int batchSize = 500;
                List<List<TodoAutoCompleteDto>> batchLists = ListUtil.split(dtoList, batchSize);

                // 收集所有用户ID
                Set<String> allUserIdSet = new HashSet<>();

                for (List<TodoAutoCompleteDto> batch : batchLists) {
                    // 完成时间取结束时间
                    List<TodoUserRelation> list = batch.stream().map(item -> {
                        return TodoUserRelation.builder()
                                .id(item.getId())
                                .status(CompleteTypeEnum.COMPLETE.getCode())
                                .completeTime(item.getEndTime())
                                .isAutoComplete(YnEnum.YES.getValue())
                                .build();
                    }).collect(Collectors.toList());

                    // 批量更新
                    todoUserRelationService.updateBatchById(list);
                }

                // 缓存变更的用户ID
                if (CollUtil.isNotEmpty(allUserIdSet)) {
                    cacheChangeUserIds(allUserIdSet);
                }
            } else {
                log.info("没有找到需要自动完成的待办，日期: {}", DateUtil.format(startDate, DatePattern.NORM_DATE_PATTERN));
            }
        } catch (InterruptedException e) {
            log.error("获取自动完成待办锁被中断，日期: {}", DateUtil.format(startDate, DatePattern.NORM_DATE_PATTERN), e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("自动完成待办失败，日期: {}", DateUtil.format(startDate, DatePattern.NORM_DATE_PATTERN), e);
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public void delCompleteCalendar(String dateStr) {
        Date date;
        // 如果为空，则默认取昨天
        if (StringUtils.isBlank(dateStr)) {
            date = DateUtil.yesterday();
        } else {
            date = DateUtil.parse(dateStr);
        }

        // 使用分布式锁防止并发删除日历导致的死锁
        RLock lock = redissonClient.getLock("TODO_DEL_CALENDAR_LOCK:" + DateUtil.format(date, DatePattern.PURE_DATE_PATTERN));
        boolean locked = false;
        try {
            // 尝试获取锁，最多等待500毫秒，锁自动释放时间30秒
            locked = lock.tryLock(500, 30000, TimeUnit.MILLISECONDS);
            if (!locked) {
                log.warn("获取删除完成待办日历锁失败，日期: {}", DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));
                return; // 如果获取锁失败，直接返回，不抛出异常，因为这是定时任务
            }

            // 获取要删除的日历待办
            List<TodoDef> list = todoDefMapper.listCompleteCalendar(date);
            if (CollUtil.isEmpty(list)) {
                log.info("没有找到需要删除的完成待办日历，日期: {}", DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));
                return;
            }

            log.info("找到{}\u6761需要删除的完成待办日历", list.size());

            // 拆分成5组进行并行处理
            List<List<TodoDef>> split = ListUtil.splitAvg(list, AddressbookConstant.PROCESS_THREAD_COUNT_5);

            // 创建一个计数器来跟踪完成的任务
            AtomicInteger completedTasks = new AtomicInteger(0);
            CountDownLatch latch = new CountDownLatch(split.size());

            // 异步删除日历
            for (List<TodoDef> todoList : split) {
                if (CollUtil.isEmpty(todoList)) {
                    latch.countDown();
                    continue;
                }

                commonExecutor.execute(() -> {
                    try {
                        for (TodoDef todoDef : todoList) {
                            try {
                                Result<Object> result = calendarFeign.del(agentProperties.getCorpId(), todoDef.getAgentId(), todoDef.getCalId());
                                if (result.hasError()) {
                                    log.error("删除日历待办失败，待办id：{}，日历id：{}", todoDef.getId(), todoDef.getCalId());
                                } else {
                                    completedTasks.incrementAndGet();
                                }
                            } catch (Exception e) {
                                log.error("删除日历待办异常，待办id：{}，日历id：{}", todoDef.getId(), todoDef.getCalId(), e);
                            }
                        }
                    } finally {
                        latch.countDown();
                    }
                });
            }

            // 等待所有任务完成，最多等待30秒
            try {
                boolean allDone = latch.await(30, TimeUnit.SECONDS);
                if (allDone) {
                    log.info("删除完成待办日历完成，成功删除{}/{}条记录", completedTasks.get(), list.size());
                } else {
                    log.warn("删除完成待办日历超时，已删除{}/{}条记录", completedTasks.get(), list.size());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("等待删除完成待办日历被中断", e);
            }
        } catch (InterruptedException e) {
            log.error("获取删除完成待办日历锁被中断，日期: {}", DateUtil.format(date, DatePattern.NORM_DATE_PATTERN), e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("删除完成待办日历失败，日期: {}", DateUtil.format(date, DatePattern.NORM_DATE_PATTERN), e);
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public IPage<TodoVo> pageMyTodo(TodoPageQo pageQo) {
        IPage<TodoVo> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        //获取当前用户
        String userId = SecurityContextHolder.getUserId();
        pageQo.setUserid(userId);


        TodoHandler todoHandler = todoHandlerFactory.getHandler(todoProperties.getHandler());
        //同步代办信息
        todoHandler.syncTodoInfo();

        List<TodoVo> voList;
        //加入白名单校验逻辑
        if (StringUtils.isNotEmpty(pageQo.getAppId())) {
            log.info("pageQo.appId {} ", pageQo.getAppId());
            boolean checkWhiteListResult = checkIsWhiteList(pageQo);
            log.info("checkWhiteListResult :{} ", checkWhiteListResult);
            if (checkWhiteListResult) {
//            page.setRecords(new ArrayList<TodoVo>());
//            page.setTotal(0);
                return page;
            }
        } else {
            if (StringUtils.isNotEmpty(pageQo.getWhiteListSign()) && WHITE_LIST_SIGN.equals(pageQo.getWhiteListSign())) {
//                存在白名单应用开启的状态，但是白名单一个用户都没有的情况吗?
                List<TodoWhitelistUser> whitelistUsers = todoWhitelistUserMapper.getWhiteUsers();
                if (CollectionUtil.isNotEmpty(whitelistUsers)) {
                    Map<Long, List> result = dealData(whitelistUsers);
                    List<String> blackAppListStr = getBlackList(result, pageQo.getUserid());
                    pageQo.setBlackListAppId(blackAppListStr);
                }

            }

        }
        //分页查询未完成待办
        if (CompleteTypeEnum.INCOMPLETE.getCode() == pageQo.getStatus()) {
            voList = todoDefMapper.pageTodoIncomplete(page, pageQo);

        } else {
            //分页查询已完成待办
            voList = todoDefMapper.pageTodoComplete(page, pageQo);
        }
        if (CollUtil.isEmpty(voList)) {
            return page;
        }

        //填充字段信息
        fillField(voList);

        page.setRecords(voList);
        return page;
    }

    private List<Long> getLongBlackList(Map<Long, List> map, String userId) {
        List<Long> blackListAppids = new ArrayList<>();
        Set<Long> keySet = map.keySet();
        for (Long key : keySet) {
            if (!map.get(key).contains(userId)) {
                blackListAppids.add(key);
            }
        }
        return blackListAppids;
    }

    private List<String> getBlackList(Map<Long, List> map, String userId) {
        List<String> blackListAppids = new ArrayList<>();
        Set<Long> keySet = map.keySet();
        for (Long key : keySet) {
            if (!map.get(key).contains(userId)) {
                blackListAppids.add(String.valueOf(key));
            }
        }
        return blackListAppids;
    }

    private Map dealData(List<TodoWhitelistUser> whitelistUsers) {
        Map resultMap = new HashMap<Long, List>();
        for (TodoWhitelistUser todoWhitelistUser : whitelistUsers) {
            List list;
            if (resultMap.get(todoWhitelistUser.getAppId()) == null) {
                list = new ArrayList();
            } else {
                list = (List) resultMap.get(todoWhitelistUser.getAppId());
            }
            list.add(todoWhitelistUser.getUserId());
            resultMap.put(todoWhitelistUser.getAppId(), list);
        }
        return resultMap;
    }

    private boolean checkIsWhiteList(TodoPageQo pageQo) {
        if (StringUtils.isEmpty(pageQo.getWhiteListSign()) || !WHITE_LIST_SIGN.equals(pageQo.getWhiteListSign())) {
            return false;
        }
        //获取白名单开关
        TodoWhitelistConfig todoWhitelistConfig = todoWhitelistConfigMapper.selectByAppId(pageQo.getAppId());
        if (todoWhitelistConfig == null || todoWhitelistConfig.getTestStatus() == 0) {
            return false;
        }
        //获取白名单用户，判断用户id是否在里面
        List<TodoWhitelistUser> todoWhitelistUsers = todoWhitelistUserMapper.getWhiteUsers();
        List<String> idList = todoWhitelistUsers.stream().map(TodoWhitelistUser::getUserId).collect(Collectors.toList());
        if (idList.contains(pageQo.getUserid())) {
            return false;
        }
        return true;
    }

    @Override
    public IPage<TodoFlowVo> pageCopyToMeTodo(TodoPageQo pageQo) {
        IPage<TodoFlowVo> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        //获取当前用户
        String userId = SecurityContextHolder.getUserId();
        pageQo.setUserid(userId);

        //加入白名单校验逻辑
        if (StringUtils.isNotEmpty(pageQo.getAppId())) {
            boolean checkWhiteListResult = checkIsWhiteList(pageQo);
            if (checkWhiteListResult) {
//            page.setRecords(new ArrayList<TodoVo>());
//            page.setTotal(0);
                return page;
            }
        } else {
            if (StringUtils.isNotEmpty(pageQo.getWhiteListSign()) && WHITE_LIST_SIGN.equals(pageQo.getWhiteListSign())) {
//                存在白名单应用开启的状态，但是白名单一个用户都没有的情况吗?
                List<TodoWhitelistUser> whitelistUsers = todoWhitelistUserMapper.getWhiteUsers();
                if (CollectionUtil.isNotEmpty(whitelistUsers)) {
                    Map<Long, List> result = dealData(whitelistUsers);
                    List<String> blackAppListStr = getBlackList(result, pageQo.getUserid());
                    pageQo.setBlackListAppId(blackAppListStr);
                }

            }

        }

        //查询抄送给我的待办
        List<TodoFlowVo> voList = todoDefMapper.pageCopyToMeTodo(page, pageQo);

        if (CollUtil.isEmpty(voList)) {
            return page;
        }

        //填充字段信息
        fillFlowField(voList);

        page.setRecords(voList);
        return page;
    }

    /**
     * 填充字段信息
     */
    private void fillFlowField(List<TodoFlowVo> voList) {
        //获取应用id列表
        List<Long> appIdList = voList.stream().map(item -> Long.parseLong(item.getCreator())).distinct().collect(Collectors.toList());
        Map<Long, WbApp> appMap = new HashMap<>();
        if (CollUtil.isNotEmpty(appIdList)) {
            //查询应用
            List<WbApp> wbApps = wbAppService.listByIds(appIdList);
            appMap = CollUtil.toMap(wbApps, null, WbApp::getId, item -> item);
        }

        for (TodoFlowVo vo : voList) {
            //设置应用logo
            WbApp app = appMap.get(Long.parseLong(vo.getCreator()));
            if (app != null) {
                vo.setAppLogoUrl(app.getLogoUrl());
            }
        }
    }

    @Override
    public IPage<TodoFlowVo> pageISubmitTodo(TodoPageQo pageQo) {
        IPage<TodoFlowVo> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        //获取当前用户
        String userId = SecurityContextHolder.getUserId();
        pageQo.setUserid(userId);

        //加入白名单校验逻辑
        if (StringUtils.isNotEmpty(pageQo.getAppId())) {
            boolean checkWhiteListResult = checkIsWhiteList(pageQo);
            if (checkWhiteListResult) {
//            page.setRecords(new ArrayList<TodoVo>());
//            page.setTotal(0);
                return page;
            }
        } else {
            if (StringUtils.isNotEmpty(pageQo.getWhiteListSign()) && WHITE_LIST_SIGN.equals(pageQo.getWhiteListSign())) {
//                存在白名单应用开启的状态，但是白名单一个用户都没有的情况吗?
                List<TodoWhitelistUser> whitelistUsers = todoWhitelistUserMapper.getWhiteUsers();
                if (CollectionUtil.isNotEmpty(whitelistUsers)) {
                    Map<Long, List> result = dealData(whitelistUsers);
                    List<String> blackAppListStr = getBlackList(result, pageQo.getUserid());
                    pageQo.setBlackListAppId(blackAppListStr);
                }

            }
        }
        //分页查询我提交的待办
        List<TodoFlowVo> voList = todoDefMapper.pageISubmitTodo(page, pageQo);

        if (CollUtil.isEmpty(voList)) {
            return page;
        }

        //填充字段信息
        fillFlowField(voList);

        page.setRecords(voList);
        return page;
    }

    @Transactional(rollbackFor = Exception.class, timeout = 15)
    @Override
    public void delay(TodoDelayQo qo) {
        //查询代办明细
        TodoDetail detail = todoDetailService.getById(qo.getTodoDetailId());

        //更新待办明细
        detail.setIsDelay(YnEnum.YES.getValue());
        detail.setStartTime(DateUtil.offsetDay(detail.getStartTime(), qo.getDelayDays()));
        Date endTime = detail.getEndTime();
        //如果结束时间不为2100，则更新
        if (DateUtil.year(endTime) != 2100) {
            detail.setEndTime(DateUtil.offsetDay(detail.getEndTime(), qo.getDelayDays()));
        }
        todoDetailService.updateById(detail);
    }

    @Override
    public List<TodoVo> listOverdueMyTodo(TodoPageQo qo) {
        return listOverdueTodo(qo);
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public void saveFlowInfo(TodoSaveFlowInfoQo qo) {

        //参数校验
        paramCheck(qo);

        //获取当前访问信息
        ApiAccessInfoDto apiAccessInfoDto = SecurityContextHolder.get(SecurityConstant.API_ACCESS_INFO, ApiAccessInfoDto.class);

        //校验流水号是否存在
        String appId = String.valueOf(apiAccessInfoDto.getAppId());
        LambdaQueryWrapper<TodoFlowInfo> flowQw = Wrappers.lambdaQuery(TodoFlowInfo.class);
        flowQw.eq(TodoFlowInfo::getSerialNo, qo.getSerialNo());
        flowQw.eq(TodoFlowInfo::getAppId, String.valueOf(appId));
        if (todoFlowInfoService.exists(flowQw)) {
            throw ExceptionCodeEnum.TODO_FLOW_SERIAL_NO_EXIST.toServiceException();
        }

        //查询应用
        WbApp wbApp = wbAppService.getById(appId);

        //新增节点信息
        TodoFlowInfo flowInfo = new TodoFlowInfo();
        flowInfo.setAppId(apiAccessInfoDto.getAppId());
        flowInfo.setAppName(wbApp.getAppName());
        flowInfo.setSerialNo(qo.getSerialNo());
        flowInfo.setName(qo.getName());
        flowInfo.setFlowStatus(qo.getFlowStatus());
        flowInfo.setDescription(qo.getDescription());
        flowInfo.setSubmitterId(qo.getSubmitterId());
        flowInfo.setSubmitterName(qo.getSubmitterName());
        flowInfo.setSubmitInfo(qo.getSubmitInfo());
        flowInfo.setFlowNodeList((ArrayNode) JacksonUtils.toObj(JacksonUtils.toJson(qo.getFlowNodeList())));
        flowInfo.setCreateDate(Objects.isNull(qo.getCreateTime()) ? new Date() : qo.getCreateTime());
        flowInfo.setType(qo.getType());
        todoFlowInfoService.save(flowInfo);

    }

    /**
     * 参数校验
     */
    private void paramCheck(TodoSaveFlowInfoQo qo) {
        //名称不能为空且长度不能大于500
        if (StringUtils.isBlank(qo.getName()) || qo.getName().length() > 500) {
            throw ExceptionCodeEnum.TODO_NAME_ADD_INVALID.toServiceException();
        }
        //描述长度不能大于500
        if (StringUtils.isNotBlank(qo.getDescription()) && qo.getDescription().length() > 500) {
            throw ExceptionCodeEnum.TODO_DESCRIPTION_INVALID.toServiceException();
        }
        //待办类型长度不能大于30
        if (StringUtils.isNotBlank(qo.getType()) && qo.getType().length() > 30) {
            throw ExceptionCodeEnum.TODO_TYPE_INVALID.toServiceException();
        }
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public void delFlowInfo(TodoDelFlowInfoQo qo) {

        //获取当前访问信息
        ApiAccessInfoDto apiAccessInfoDto = SecurityContextHolder.get(SecurityConstant.API_ACCESS_INFO, ApiAccessInfoDto.class);
        Long appId = apiAccessInfoDto.getAppId();

        //查询待办信息
        LambdaQueryWrapper<TodoFlowInfo> flowQw = Wrappers.lambdaQuery(TodoFlowInfo.class);
        flowQw.eq(TodoFlowInfo::getSerialNo, qo.getSerialNo());
        flowQw.eq(TodoFlowInfo::getAppId, appId);
        TodoFlowInfo flowInfo = todoFlowInfoService.getOne(flowQw);
        if (flowInfo == null) {
            throw ExceptionCodeEnum.TODO_FLOW_NOT_EXIST.toServiceException();
        }

        //删除流程信息
        todoFlowInfoService.removeById(flowInfo.getId());

        LambdaQueryWrapper<TodoDef> defQw = Wrappers.lambdaQuery(TodoDef.class);
        defQw.eq(TodoDef::getFlowId, flowInfo.getId());
        List<TodoDef> list = super.list(defQw);

        //如果没有待办定义，则说明没有创建节点，直接返回
        if (CollUtil.isEmpty(list)) {
            return;
        }

        //删除待办定义
        List<Long> defIdList = list.stream().map(TodoDef::getId).collect(Collectors.toList());
        todoDefService.removeByIds(defIdList);

        //删除待办明细
        todoDetailService.remove(Wrappers.<TodoDetail>lambdaQuery().in(TodoDetail::getTodoDefId, defIdList));

        //删除用户关联
        todoUserRelationService.remove(Wrappers.<TodoUserRelation>lambdaQuery().in(TodoUserRelation::getTodoDefId, defIdList));

        //删除抄送用户
        todoFlowCopyUserService.remove(Wrappers.<TodoFlowCopyUser>lambdaQuery().eq(TodoFlowCopyUser::getFlowId, flowInfo.getId()));
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public void saveFlowNode(TodoSaveFlowNodeQo qo) {
        //获取当前访问信息
        ApiAccessInfoDto apiAccessInfoDto = SecurityContextHolder.get(SecurityConstant.API_ACCESS_INFO, ApiAccessInfoDto.class);
        Long appId = apiAccessInfoDto.getAppId();

        //查询流程信息
        TodoFlowInfo flowInfo = todoFlowInfoService.getFlowInfoBySerialNoAndAppId(qo.getSerialNo(), appId);
        if (flowInfo == null) {
            throw ExceptionCodeEnum.TODO_FLOW_NOT_EXIST.toServiceException();
        }

        //查询待办节点是否已经创建过，如果创建过，走更新流程节点逻辑
        List<TodoDef> todoDefList = this.list(Wrappers.lambdaQuery(TodoDef.class).eq(TodoDef::getFlowId, flowInfo.getId()).eq(TodoDef::getNodeId, qo.getFlowNode().getNodeId()));
        if (CollUtil.isNotEmpty(todoDefList)) {
            todoDefService.updateFlowNode(qo);
            return;
        }

        //新增待办节点
        addFlowNode(qo, flowInfo, apiAccessInfoDto);
    }

    /**
     * 新增待办节点
     *
     * @param qo               待办节点入参
     * @param flowInfo         流程信息
     * @param apiAccessInfoDto 访问服务商信息
     */
    private void addFlowNode(TodoSaveFlowNodeQo qo, TodoFlowInfo flowInfo, ApiAccessInfoDto apiAccessInfoDto) {

        //节点信息
        TodoFlowNodeDto flowNode = qo.getFlowNode();

        //查询节点用户入参
        TodoNodeUserExistsByFlowInfoDto userExistsDto = new TodoNodeUserExistsByFlowInfoDto();
        userExistsDto.setSerialNo(flowInfo.getSerialNo());
        userExistsDto.setNodeId(flowNode.getNodeId());
        userExistsDto.setAppId(apiAccessInfoDto.getAppId());

        //抄送人
        if (flowNode.getNodeType() == TodoFlowNodeTypeEnum.COPY_USER.getCode()) {
            //新增抄送人
            addCopyUser(qo, flowInfo, userExistsDto, flowNode);

        } else {
            //新增节点用户
            addUser(flowInfo, apiAccessInfoDto, userExistsDto, flowNode);
        }
        Set<String> userIds = Optional.ofNullable(qo.getFlowNode().getUserInfoList()).orElseGet(ArrayList::new)
                .stream().map(TodoFlowNodeDto.NodeUserInfo::getUserid).collect(Collectors.toSet());
        cacheChangeUserIds(userIds);
    }

    /**
     * 缓存待办变更用户id
     *
     * @param userIds 用户变更集合
     */
    private void cacheChangeUserIds(Set<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return;
        }

        // 使用异步方式添加，避免阻塞主业务流程
        commonExecutor.execute(() -> {
            RLock lock = redissonClient.getLock(CommonServiceRedisKeys.TODO_SHOW_CHANGE_USER_ID_SET_LOCK);
            boolean locked = false;
            try {
                // 使用更长的等待时间和更短的锁持有时间
                locked = lock.tryLock(1000, 3000, TimeUnit.MILLISECONDS);
                if (locked) {
                    redissonClient.getSet(CommonServiceRedisKeys.TODO_SHOW_CHANGE_USER_ID_SET).addAll(userIds);
                    log.debug("成功缓存待办变更用户id，用户数量: {}", userIds.size());
                } else {
                    // 如果获取锁失败，使用延迟重试
                    log.warn("获取缓存待办变更用户id锁失败，将在500ms后重试，用户数量: {}", userIds.size());
                    ThreadUtil.sleep(500, TimeUnit.MILLISECONDS);
                    // 重试一次
                    try {
                        locked = lock.tryLock(1000, 3000, TimeUnit.MILLISECONDS);
                        if (locked) {
                            redissonClient.getSet(CommonServiceRedisKeys.TODO_SHOW_CHANGE_USER_ID_SET).addAll(userIds);
                            log.debug("重试成功缓存待办变更用户id，用户数量: {}", userIds.size());
                        } else {
                            log.error("重试获取缓存待办变更用户id锁失败，用户数量: {}", userIds.size());
                        }
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        log.error("重试缓存待办变更用户id被中断，用户数量: {}", userIds.size(), ie);
                    } catch (Exception ex) {
                        log.error("重试缓存待办变更用户id失败，用户数量: {}", userIds.size(), ex);
                    } finally {
                        if (locked && lock.isHeldByCurrentThread()) {
                            lock.unlock();
                        }
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("缓存待办变更用户id被中断，用户数量: {}", userIds.size(), e);
            } catch (Exception e) {
                log.error("缓存待办变更用户id失败，用户数量: {}", userIds.size(), e);
            } finally {
                if (locked && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        });
    }

    /**
     * 新增节点用户
     */
    private void addUser(TodoFlowInfo flowInfo, ApiAccessInfoDto apiAccessInfoDto, TodoNodeUserExistsByFlowInfoDto userExistsDto, TodoFlowNodeDto flowNode) {
        //查询代办节点用户
        List<String> useridList = todoDefMapper.listTodoUserByFlowNode(userExistsDto);

        for (TodoFlowNodeDto.NodeUserInfo userInfo : flowNode.getUserInfoList()) {
            //用户节点存在
            if (useridList.contains(userInfo.getUserid())) {
                log.error("待办节点用户【{}】已存在", userInfo.getUserid());
                continue;
            }

            //审核人
            //待办新增入参
            TodoSaveQo todoSaveQo = new TodoSaveQo();
            todoSaveQo.setIsFlow(YnEnum.YES.getValue());
            todoSaveQo.setFlowId(flowInfo.getId());
            todoSaveQo.setNodeId(flowNode.getNodeId());
            todoSaveQo.setCreator(String.valueOf(apiAccessInfoDto.getAppId()));
            todoSaveQo.setCreatorName(apiAccessInfoDto.getAppName());
            todoSaveQo.setCreateDate(flowInfo.getCreateDate());
            todoSaveQo.setCreatorType(TodoCreatorTypeEnum.APP.getCode());
            todoSaveQo.setName(flowInfo.getName());
            todoSaveQo.setDescription(flowInfo.getDescription());
            todoSaveQo.setCompleteMode(Collections.singletonList(TodoCompleteModeEnum.API.getCode()));

            //开始时间
            Date startTime = flowNode.getStartTime();
            if (startTime == null) {
                //如果为空，则设置为当前待办信息的上一个节点的完成时间或开始时间，如果没有上一个节点则使用待办信息的创建时间
                TodoDef todoDef = this.getCurrentNode(flowInfo.getId());
                if (Objects.nonNull(todoDef)) {
                    startTime = todoDef.getStartTime();
                } else {
                    startTime = flowInfo.getCreateDate();
                }
            }
            todoSaveQo.setStartTime(startTime);

            //结束时间
            Date endTime = flowNode.getEndTime();
            if (endTime == null) {
                //如果为空，则设置2100-01-01
                endTime = DateUtil.parse("2100-01-01");
            }
            todoSaveQo.setEndTime(endTime);
            todoSaveQo.setIsRepeat(YnEnum.NO.getValue());
            todoSaveQo.setIsRemind(YnEnum.NO.getValue());

            //设置用户
            todoSaveQo.setUseridList(Collections.singletonList(userInfo.getUserid()));
            todoSaveQo.setLinkUrl(userInfo.getLinkUrl());
            todoSaveQo.setLinkUrlPc(userInfo.getLinkUrlPc());

            //获取当前类的代理类
            TodoDefServiceImpl proxy = (TodoDefServiceImpl) AopContext.currentProxy();
            //新增待办
            Long defId = proxy.saveTodo(todoSaveQo);

            //如果节点操作状态不为空且不为待处理，则用户关联设置为已完成
            if (userInfo.getOperateStatus() != null && userInfo.getOperateStatus() != 0) {
                LambdaUpdateWrapper<TodoUserRelation> urUw = Wrappers.lambdaUpdate(TodoUserRelation.class);
                urUw.set(TodoUserRelation::getStatus, CompleteTypeEnum.COMPLETE.getCode());
                urUw.set(TodoUserRelation::getCompleteTime, userInfo.getCompleteTime());
                urUw.eq(TodoUserRelation::getTodoDefId, defId);
                todoUserRelationService.update(urUw);
            }
            if (todoNoticeFlag) {
                //给用户发送待办消息通知
                commonExecutor.execute(() -> {
                    sendTodoNotice(flowInfo.getName(), userInfo.getLinkUrlPc(), userInfo.getLinkUrl(), userInfo.getUserid());
                });
            }
        }
    }

    /**
     * 发送待办消息通知
     *
     * @param title  标题
     * @param pcUrl  PC端地址
     * @param h5Url  移动端地址
     * @param toUser 发送对象多个用 | 分格
     */
    private void sendTodoNotice(String title, String pcUrl, String h5Url, String toUser) {
        TextMsgBody textMsgBody = new TextMsgBody();
        textMsgBody.setMsgType(MsgTypeEnum.TEXT.getValue());
        textMsgBody.setAgentId(Integer.valueOf(agentProperties.getTodoAgentId()));
        StringBuilder sb = new StringBuilder("您有新的待办！\n ");
        if (StringUtils.isNotBlank(title) && title.length() > 100) {
            title = title.substring(0, 100) + "......";
        }
        sb.append(title).append("\n");
        if (StringUtils.isNotBlank(pcUrl)) {
            sb.append("<a href=\"").append(pcUrl).append("\">去PC端处理</a>\n");
        }
        if (StringUtils.isNotBlank(h5Url)) {
            sb.append("<a href=\"").append(h5Url).append("\">去手机端处理</a>");
        }
        textMsgBody.setContent(sb.toString());
        textMsgBody.setToUser(toUser);
        try {
            Result<Object> result = channelHandlerFactory.getChannelHandler(MsgChannelEnum.WEIXIN_CORP_CHANNEL).handle(textMsgBody);
            if (result.hasError()) {
                log.error("用户{}，待办消息发送失败，错误码：{}，失败信息：{}", toUser, result.getCode(), result.getMsg());
            }
        } catch (Exception e) {
            log.error("用户{}，待办消息发送失败，失败信息：{}", toUser, e.getMessage(), e);
        }
    }

    /**
     * 新增抄送人
     *
     * @param qo            待办节点入参
     * @param flowInfo      流程信息
     * @param userExistsDto 节点用户存在查询入参
     * @param flowNode      待办节点信息
     */
    private void addCopyUser(TodoSaveFlowNodeQo qo, TodoFlowInfo flowInfo, TodoNodeUserExistsByFlowInfoDto userExistsDto, TodoFlowNodeDto flowNode) {
        //查询当前节点的抄送人
        List<String> copyUseridList = todoFlowCopyUserMapper.listTodoCopyUserByFlowNode(userExistsDto);

        //新增抄送人
        List<TodoFlowCopyUser> copyUserList = new ArrayList<>();
        for (TodoFlowNodeDto.NodeUserInfo nodeUserInfo : qo.getFlowNode().getUserInfoList()) {
            String userid = nodeUserInfo.getUserid();
            if (copyUseridList.contains(userid)) {
                log.error("待办节点抄送人【{}】已存在", userid);
                continue;
            }

            TodoFlowCopyUser copyUser = new TodoFlowCopyUser();
            copyUser.setFlowId(flowInfo.getId());
            copyUser.setNodeId(flowNode.getNodeId());
            copyUser.setUserid(userid);
            //设置抄送人创建时间
            copyUser.setCreateDate(Objects.isNull(nodeUserInfo.getCompleteTime()) ? new Date() : nodeUserInfo.getCompleteTime());
            copyUserList.add(copyUser);
        }
        todoFlowCopyUserService.saveBatch(copyUserList);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class, timeout = 15)
    public void updateFlowInfo(TodoUpdateFlowInfoQo qo) {
        String serialNo = qo.getSerialNo();
        paramCheck(qo);

        ApiAccessInfoDto apiAccessInfoDto = SecurityContextHolder.get(SecurityConstant.API_ACCESS_INFO, ApiAccessInfoDto.class);
        Long appId = apiAccessInfoDto.getAppId();

        // 使用分布式锁防止并发更新同一条记录导致的死锁
        String lockKey = "TODO_UPDATE_FLOW_INFO_LOCK:" + serialNo + ":" + appId;
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;

        try {
            // 尝试获取锁，最多等待800毫秒，锁自动释放时间15秒
            // 增加等待时间，减少锁争用导致的失败
            locked = lock.tryLock(800, 15000, TimeUnit.MILLISECONDS);
            if (!locked) {
                log.warn("获取更新流程信息锁失败，serialNo={}, appId={}", serialNo, appId);
                throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
            }

            // 在获取锁后再查询流程信息，避免在获取锁前后数据发生变化
            TodoFlowInfo flowInfo = todoFlowInfoService.getFlowInfoBySerialNoAndAppId(serialNo, appId);
            if (flowInfo == null) {
                throw ExceptionCodeEnum.TODO_FLOW_NOT_EXIST.toServiceException();
            }
            Long flowId = flowInfo.getId();

            WbApp wbApp = wbAppService.getById(appId);
            // 更新流程信息
            TodoFlowInfo update = BeanUtil.copyProperties(qo, TodoFlowInfo.class);
            update.setId(flowId);
            update.setAppName(wbApp.getAppName());
            update.setFlowNodeList(qo.getFlowNodeList());
            update.setSubmitInfo(qo.getSubmitInfo());
            todoFlowInfoService.updateById(update);

            // 批量更新TodoDef，使用单次更新减少锁争用
            LambdaUpdateWrapper<TodoDef> defUw = Wrappers.lambdaUpdate(TodoDef.class);
            defUw.set(TodoDef::getName, qo.getName())
                    .set(TodoDef::getDescription, qo.getDescription())
                    .eq(TodoDef::getFlowId, flowId);
            // 执行更新并返回结果
            super.update(defUw);
        } catch (InterruptedException e) {
            log.error("获取更新流程信息锁被中断，serialNo={}", serialNo, e);
            Thread.currentThread().interrupt();
            throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
        } catch (Exception e) {
            // 如果不是已知的ServiceException，则包装为系统错误
            if (!(e instanceof ServiceException)) {
                log.error("更新流程信息失败，serialNo={}", serialNo, e);
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("更新流程信息失败");
            }
            throw e;
        } finally {
            // 确保锁被正确释放
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 参数校验
     */
    private void paramCheck(TodoUpdateFlowInfoQo qo) {
        //名称长度不能大于500
        if (StringUtils.isNotBlank(qo.getName()) && qo.getName().length() > 500) {
            throw ExceptionCodeEnum.TODO_NAME_ADD_INVALID.toServiceException();
        }
        //描述长度不能大于500
        if (StringUtils.isNotBlank(qo.getDescription()) && qo.getDescription().length() > 500) {
            throw ExceptionCodeEnum.TODO_DESCRIPTION_INVALID.toServiceException();
        }
        //待办类型长度不能大于30
        if (StringUtils.isNotBlank(qo.getType()) && qo.getType().length() > 30) {
            throw ExceptionCodeEnum.TODO_TYPE_INVALID.toServiceException();
        }
    }


    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public void delFlowNode(TodoDelFlowNodeQo qo) {
        //获取当前访问信息
        ApiAccessInfoDto apiAccessInfoDto = SecurityContextHolder.get(SecurityConstant.API_ACCESS_INFO, ApiAccessInfoDto.class);
        Long appId = apiAccessInfoDto.getAppId();

        //查询流程信息
        TodoFlowInfo nodeInfo = todoFlowInfoService.getFlowInfoBySerialNoAndAppId(qo.getSerialNo(), appId);
        if (nodeInfo == null) {
            throw ExceptionCodeEnum.TODO_FLOW_NOT_EXIST.toServiceException();
        }

        //删除待办节点
        delNode(nodeInfo.getId(), qo.getNodeId());
    }

    /**
     * 删除待办节点
     */
    private void delNode(Long flowId, Long nodeId) {
        //查询代办定义
        LambdaQueryWrapper<TodoDef> defQw = Wrappers.lambdaQuery(TodoDef.class);
        defQw.eq(TodoDef::getFlowId, flowId);
        defQw.eq(TodoDef::getNodeId, nodeId);
        List<TodoDef> defList = super.list(defQw);

        //删除代办定义
        List<Long> defIdList = defList.stream().map(TodoDef::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(defIdList)) {
            todoDefService.removeByIds(defIdList);

            //删除代办明细
            todoDetailService.remove(Wrappers.<TodoDetail>lambdaQuery().in(TodoDetail::getTodoDefId, defIdList));

            //删除代办用户关联
            LambdaQueryWrapper<TodoUserRelation> queryWrapper = Wrappers.<TodoUserRelation>lambdaQuery().in(TodoUserRelation::getTodoDefId, defIdList);
            Set<String> changeUserIdSet = Optional.ofNullable(todoUserRelationService.list(queryWrapper)).orElseGet(ArrayList::new)
                    .stream().map(TodoUserRelation::getUserid).collect(Collectors.toSet());
            todoUserRelationService.remove(queryWrapper);
            cacheChangeUserIds(changeUserIdSet);
        }

        //删除待办抄送人
        todoFlowCopyUserService.remove(Wrappers.<TodoFlowCopyUser>lambdaQuery().eq(TodoFlowCopyUser::getFlowId, flowId).eq(TodoFlowCopyUser::getNodeId, nodeId));
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public void updateFlowNode(TodoSaveFlowNodeQo qo) {
        //获取当前访问信息
        ApiAccessInfoDto apiAccessInfoDto = SecurityContextHolder.get(SecurityConstant.API_ACCESS_INFO, ApiAccessInfoDto.class);
        Long appId = apiAccessInfoDto.getAppId();

        //查询待办流程信息
        TodoFlowInfo flowInfo = todoFlowInfoService.getFlowInfoBySerialNoAndAppId(qo.getSerialNo(), appId);
        if (flowInfo == null) {
            throw ExceptionCodeEnum.TODO_FLOW_NOT_EXIST.toServiceException();
        }

        //查询之前已经创建的待办节点
        TodoDef lastTodoDef = this.getOne(Wrappers.lambdaQuery(TodoDef.class)
                .eq(TodoDef::getFlowId, flowInfo.getId()).eq(TodoDef::getNodeId, qo.getFlowNode().getNodeId()).last(LIMIT_ONE));
        if (Objects.nonNull(lastTodoDef)) {
            Date startTime = qo.getFlowNode().getStartTime();
            if (startTime == null) {
                //如果为空，取之前已经创建的开始时间
                qo.getFlowNode().setStartTime(lastTodoDef.getStartTime());
            }
        }

        //增加判断开始时间是否小于结束时间，如果结束时间在开始时间之后一分钟内，将结束时间变成开始时间。
        Date startTime = qo.getFlowNode().getStartTime();
        Date endTime = qo.getFlowNode().getEndTime();
        if (startTime != null && endTime != null) {
            long between = DateUtil.between(startTime, endTime, DateUnit.SECOND, false);
            if (0 > between && between >= -60) {
                qo.getFlowNode().setEndTime(startTime);
            }
        }


        //删除旧的节点
        delNode(flowInfo.getId(), qo.getFlowNode().getNodeId());
        //新增节点
        addFlowNode(qo, flowInfo, apiAccessInfoDto);
    }

    @Override
    public List<AcAppVO> listApp(boolean isAdmin, String todoType, String whiteListSign) {
        //查询应用列表
        List<AcApp> appList = acAppService.listByCache();

        //获取当前用户
        String userid = SecurityContextHolder.getUserId();
        List<String> blackAppListStr = new ArrayList<>();
        List<Long> blackApplist = new ArrayList<>();
        if (isAdmin) {
            userid = "";
        } else {
            //加入白名单校验逻辑
            if (StringUtils.isNotEmpty(whiteListSign) && WHITE_LIST_SIGN.equals(whiteListSign)) {
//                存在白名单应用开启的状态，但是白名单一个用户都没有的情况吗?
                List<TodoWhitelistUser> whitelistUsers = todoWhitelistUserMapper.getWhiteUsers();
                if (CollectionUtil.isNotEmpty(whitelistUsers)) {
                    Map<Long, List> result = dealData(whitelistUsers);
                    blackAppListStr = getBlackList(result, userid);
                    blackApplist = getLongBlackList(result, userid);
                }

            }
        }

        List<Long> appIdList;
        //待办类型 1 待完成 2 已完成 3 抄送我 4 我提交的，默认查询全部
        switch (todoType) {
            case "1":
                //查询待办相关应用id
                appIdList = todoDefMapper.selectAppIdList(userid, blackAppListStr);
                break;
            case "2":
                //查询已完成相关应用id
                appIdList = todoUserRelationMapper.listCompletedAppId(userid, blackAppListStr);
                break;
            case "3":
                //查询抄送我相关应用id
                appIdList = todoFlowCopyUserMapper.listCopyAppId(userid, blackApplist);
                break;
            case "4":
                //查询我提交的相关应用id
                if (CollectionUtil.isEmpty(blackApplist)) {
                    appIdList = todoFlowInfoService.list(Wrappers.lambdaQuery(TodoFlowInfo.class).select(TodoFlowInfo::getAppId)
                                    .eq(StringUtils.isNotBlank(userid), TodoFlowInfo::getSubmitterId, userid).groupBy(TodoFlowInfo::getAppId))
                            .stream().map(TodoFlowInfo::getAppId).collect(Collectors.toList());
                } else {
                    appIdList = todoFlowInfoService.list(Wrappers.lambdaQuery(TodoFlowInfo.class).select(TodoFlowInfo::getAppId)
                                    .eq(StringUtils.isNotBlank(userid), TodoFlowInfo::getSubmitterId, userid).notIn(TodoFlowInfo::getAppId, blackApplist).groupBy(TodoFlowInfo::getAppId))
                            .stream().map(TodoFlowInfo::getAppId).collect(Collectors.toList());
                }

                break;
            default:
                //全部
                appIdList = appList.stream().map(AcApp::getId).collect(Collectors.toList());
        }
        //过滤待办相关应用
        appList = appList.stream().filter(item -> appIdList.contains(item.getId())).collect(Collectors.toList());
        return BeanUtil.copyToList(appList, AcAppVO.class);
    }

    @Override
    public TodoDef getMinCreateDate() {
        return this.getOne(Wrappers.lambdaQuery(TodoDef.class).orderByAsc(TodoDef::getCreateDate).last(LIMIT_ONE));
    }

    @Override
    public void todoRemind() {
        //查询当前时间小时
        Integer hour = DateUtil.thisHour(true);

        //根据配置获取当前提醒待办用户
        List<String> useridList = todoUserAppStatMapper.listTodoUserWithConfig(hour);
        if (CollUtil.isEmpty(useridList)) {
            return;
        }

        //拆分成16组
        List<List<String>> useridSplit = ListUtil.splitAvg(useridList, 16);
        for (List<String> userids : useridSplit) {
            if (CollUtil.isEmpty(userids)) {
                continue;
            }

            //处理待办提醒
            commonExecutor.execute(() -> processTodoRemind(userids));
        }
    }

    /**
     * 处理待办提醒
     */
    private void processTodoRemind(List<String> userids) {
        List<TodoTodayDto> dtoList = new ArrayList<>();
        for (String userid : userids) {
            //查询用户今日待办
            PersonalStatOverviewVO personStatOverview = toDoStatService.getPersonStatOverview(userid, WHITE_LIST_SIGN);
            if (personStatOverview != null) {
                UserTodoCompletedStatVO statVO = personStatOverview.getUserTodoCompletedStatVO();
                if (statVO != null) {
                    TodoTodayDto todoTodayDto = new TodoTodayDto();
                    todoTodayDto.setUserid(userid);
                    todoTodayDto.setTodayTodo(statVO.getTodayTodo());
                    todoTodayDto.setTodayDelayTodo(statVO.getTodayDelayTodo());
                    dtoList.add(todoTodayDto);
                }
            }
        }
        //发送消息
        sendMsg(dtoList);
    }

    /**
     * 发送消息
     */
    private void sendMsg(List<TodoTodayDto> dtoList) {
        //查询代办应用
        WbApp app = wbAppService.getByAgentIdAndCorpId(Integer.valueOf(agentProperties.getTodoAgentId()), agentProperties.getCorpId());

        for (TodoTodayDto dto : dtoList) {
            int totalTodo = dto.getTodayTodo();
            if (totalTodo == 0) {
                continue;
            }
            //拼接提醒文案
            String dateYHM = DatePattern.NORM_DATETIME_MINUTE_FORMAT.format(DateUtil.date());
            String msg = "截至 " + dateYHM + "，您一共有 " + totalTodo + " 条待办任务，请前往待办中心办理。";

            //发送消息提醒
            TextcardMsgBody textcardMsgBody = new TextcardMsgBody();
            textcardMsgBody.setMsgType(MsgTypeEnum.TEXTCARD.value());
            textcardMsgBody.setAgentId(Integer.valueOf(agentProperties.getTodoAgentId()));
            textcardMsgBody.setToUser(dto.getUserid());
            textcardMsgBody.setTitle(msg);
            textcardMsgBody.setUrl(app.getHomeUrl());
            textcardMsgBody.setDescription("<div class=\"gray\">待办提醒</div>");
            try {
                Result<Object> textcardResult = channelHandlerFactory.getChannelHandler(MsgChannelEnum.WEIXIN_CORP_CHANNEL).handle(textcardMsgBody);
                if (textcardResult.hasError()) {
                    log.error("待办提醒发送失败，错误码：{}，失败信息：{}", textcardResult.getCode(), textcardResult.getMsg());
                }
            } catch (Exception e) {
                log.error("待办提醒发送失败，失败信息：{}", e.getMessage(), e);
            }
        }
    }

    @Override
    public void updateTodoWorkbenchData(String needUpdateAll) {
        // 查询代办应用
        WbApp app = wbAppService.getByAgentIdAndCorpId(Integer.valueOf(agentProperties.getTodoAgentId()), agentProperties.getCorpId());
        // 获取代办展示类型
        String showStyle = app.getShowStyle();
        // 默认展示类型无需处理
        if (AppShowStyleEnum.NORMAL.code.equals(showStyle)) {
            return;
        }
        List<String> useridList;
        if (StringUtils.isNotBlank(needUpdateAll) && "all".equalsIgnoreCase(needUpdateAll)) {
            useridList = todoUserRelationService.listAllUserId();
        } else {
            useridList = readCacheChangeUseridsAsync();
        }

        if (CollUtil.isEmpty(useridList)) {
            log.info("没有找到需要更新的人员信息！");
            return;
        }
        //去重用户
        useridList = useridList.stream().distinct().collect(Collectors.toList());

        WxCpService wxCpService = wxCpServiceFactory.get(agentProperties.getCorpId(), agentProperties.getTodoAgentId());

        // 获取待办当前的配置
        WbAppWxset appWxset = wbAppWxsetService.getOne(Wrappers.<WbAppWxset>lambdaQuery().eq(WbAppWxset::getAgentId, Integer.valueOf(agentProperties.getTodoAgentId())));
        AppShowStyleDTO appShowStyleDTO = new AppShowStyleDTO();
        if (appWxset.getShowTemplateSet() != null) {
            appShowStyleDTO = JSONObject.parseObject(appWxset.getShowTemplateSet().toString(), AppShowStyleDTO.class);
        }
        // 拆分16组
        List<List<String>> useridSplit = ListUtil.splitAvg(useridList, 16);
        log.info("需要操作工作台的用户：{}", JSON.toJSONString(useridList));
        for (List<String> userids : useridSplit) {
            if (CollUtil.isEmpty(userids)) {
                continue;
            }

            // 处理工作台展示数据
            AppShowStyleDTO finalAppShowStyleDTO = appShowStyleDTO;

            commonExecutor.execute(() -> {
                try {
                    processWorkBenchData(userids, wxCpService, showStyle, finalAppShowStyleDTO);
                } catch (Exception e) {
                    log.error("处理工作台数据失败，失败信息：{}", e.getMessage(), e);
                }
            });
        }
    }

    /**
     * 异步读取待办展示变更用户
     *
     * @return 待办展示需要变更用户
     */
    private List<String> readCacheChangeUseridsAsync() {
        List<String> useridList = new ArrayList<>();
        RSet<String> set = redissonClient.getSet(CommonServiceRedisKeys.TODO_SHOW_CHANGE_USER_ID_SET);
        RLock lock = redissonClient.getLock(CommonServiceRedisKeys.TODO_SHOW_CHANGE_USER_ID_SET_LOCK);
        boolean locked = false;
        try {
            // 使用更短的等待时间和更短的锁持有时间
            locked = lock.tryLock(500, 5000, TimeUnit.MILLISECONDS);
            if (locked) {
                // 获取待办变更用户
                useridList = new ArrayList<>(set.readAll());
                if (!useridList.isEmpty()) {
                    log.info("读取待办展示变更用户成功，用户数量: {}", useridList.size());
                    set.clear();
                }
            } else {
                log.warn("获取待办展示变更用户锁失败，将在下次调度时重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取待办展示变更用户被中断", e);
        } catch (Exception e) {
            log.error("获取待办展示变更用户失败", e);
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return useridList;
    }

    /**
     * 处理工作台数据
     */
    private void processWorkBenchData(List<String> userids, WxCpService wxCpService, String showStyle, AppShowStyleDTO appShowStyleDTO) {
        log.info("开始执行 processWorkBenchData 方法");
        List<TodoTodayDto> dtoList = new ArrayList<>();
        for (String userid : userids) {
            // 查询用户今日待办
            PersonalStatOverviewVO personStatOverview = toDoStatService.getPersonStatOverview(userid, WHITE_LIST_SIGN);
            log.info("查询用户{}今日待办：{}", userid, JSON.toJSONString(personStatOverview));
            if (personStatOverview != null) {
                UserTodoCompletedStatVO statVO = personStatOverview.getUserTodoCompletedStatVO();
                if (statVO == null) {
                    continue;
                }
                TodoTodayDto todoTodayDto = new TodoTodayDto();
                todoTodayDto.setUserid(userid);
                todoTodayDto.setTodayTodo(ObjectUtil.defaultIfNull(statVO.getTodayTodo(), 0));
                todoTodayDto.setTodayCompleted(ObjectUtil.defaultIfNull(statVO.getTodayCompleted(), 0));
                todoTodayDto.setCurrentYearCompleted(ObjectUtil.defaultIfNull(statVO.getCurrentYearCompleted(), 0));
                todoTodayDto.setTotalCompleted(ObjectUtil.defaultIfNull(statVO.getTotalCompleted(), 0));
                // 今日代办
                Integer todayTodo = todoTodayDto.getTodayTodo();
                // 今日已完成代办
                Integer todayCompleted = todoTodayDto.getTodayCompleted();
                String todayCompleteRate = "";
                Integer todayTotalTodo = todayTodo + todayCompleted;
                if (todayTotalTodo != 0) {
                    // 今日完成率 = 今日已完成代办 / (今日待办 + 今日已完成代办) * 100%
                    todayCompleteRate = NumberUtil.decimalFormat("#%", NumberUtil.div(todayCompleted, todayTotalTodo));
                }
                todoTodayDto.setTodayCompleteRate(todayCompleteRate);
                dtoList.add(todoTodayDto);
            }
        }
        // 设置工作台展示数据
        setWorkbench(dtoList, wxCpService, showStyle, appShowStyleDTO);
        log.info("结束执行 processWorkBenchData 方法");
    }

    @Override
    public Map<Date, List<TodoVo>> listMyTodoCalendar(MyTodoCalendarQo qo) {
        //获取当前用户
        String userId = SecurityContextHolder.getUserId();
        qo.setUserid(userId);
        //加入白名单校验逻辑
        if (StringUtils.isNotEmpty(qo.getWhiteListSign()) && WHITE_LIST_SIGN.equals(qo.getWhiteListSign())) {
//                存在白名单应用开启的状态，但是白名单一个用户都没有的情况吗?
            List<TodoWhitelistUser> whitelistUsers = todoWhitelistUserMapper.getWhiteUsers();
            if (CollectionUtil.isNotEmpty(whitelistUsers)) {
                Map<Long, List> result = dealData(whitelistUsers);
                List<String> blackAppListStr = getBlackList(result, qo.getUserid());
                qo.setBlackListAppId(blackAppListStr);
            }

        }
        //查询我的代办日历
        List<TodoVo> voList = todoDefMapper.listMyTodoCalendar(qo);
        if (CollUtil.isEmpty(voList)) {
            return Collections.emptyMap();
        }

        //填充字段信息
        fillField(voList);

        //按开始时间分组
        return voList.stream().collect(Collectors.groupingBy(TodoVo::getStartDate));
    }

    @Override
    public IPage<TodoVo> pageTodoRecord(TodoRecordPageQo pageQo) {
        IPage<TodoVo> page = new Page<>(pageQo.getPage(), pageQo.getSize());
        if (pageQo.getAppId() != null) {
            pageQo.setAppIdStr(String.valueOf(pageQo.getAppId()));
        }

        List<TodoVo> todoList = todoDefMapper.pageTodoRecord(page, pageQo);
        if (CollUtil.isEmpty(todoList)) {
            return page;
        }

        //填充字段信息
        fillField(todoList);

        page.setRecords(todoList);
        return page;
    }

    @Override
    public TodoInfoVo getTodoInfo() {
        TodoInfoVo todoInfoVo = new TodoInfoVo();

        //查询代办应用
        WbApp app = wbAppService.getByAgentIdAndCorpId(Integer.valueOf(agentProperties.getTodoAgentId()), agentProperties.getCorpId());
        todoInfoVo.setAppId(app.getId());

        //查询待办接口分组
        String groupId = sysConfigService.selectConfigByKey("api.groupId.todo");
        todoInfoVo.setGroupId(Long.valueOf(groupId));

        return todoInfoVo;
    }

    @Transactional(rollbackFor = Exception.class, timeout = 15)
    @Override
    public void setTodoAppShowStyle(String type) throws WxErrorException {
        //查询代办应用id
        TodoInfoVo todoInfo = getTodoInfo();
        String todoAppId = String.valueOf(todoInfo.getAppId());

        AppShowStyleDTO showStyleDTO = new AppShowStyleDTO();
        showStyleDTO.setAppId(todoAppId);
        //普通类型
        if (AppShowStyleEnum.NORMAL.code.equals(type)) {
            showStyleDTO.setType(AppShowStyleEnum.NORMAL.code);

        } else if (AppShowStyleEnum.KEY_DATA.code.equals(type)) {
            //关键数据类型
            showStyleDTO.setType(AppShowStyleEnum.KEY_DATA.code);
            //设置关键数据类型展示数据
            AppShowStyleDTO.KeyData keyData1 = new AppShowStyleDTO.KeyData();
            keyData1.setKey(TODAY_TODO);
            keyData1.setData("");
            AppShowStyleDTO.KeyData keyData2 = new AppShowStyleDTO.KeyData();
            keyData2.setKey(TODAY_COMPLETED_TODO);
            keyData2.setData("");
            AppShowStyleDTO.KeyData keyData3 = new AppShowStyleDTO.KeyData();
            keyData3.setKey(TODAY_COMPLETE_RATE);
            keyData3.setData("");
            AppShowStyleDTO.KeyDataItem keyDataItem = new AppShowStyleDTO.KeyDataItem();
            keyDataItem.setItems(CollUtil.toList(keyData1, keyData2, keyData3));
            showStyleDTO.setKeyData(keyDataItem);

        } else if (AppShowStyleEnum.LIST.code.equals(type)) {
            //列表型
            showStyleDTO.setType(AppShowStyleEnum.LIST.code);
            AppShowStyleDTO.ListData listData1 = new AppShowStyleDTO.ListData();
            listData1.setTitle(TODAY_TODO);
            AppShowStyleDTO.ListData listData2 = new AppShowStyleDTO.ListData();
            listData2.setTitle(TODAY_COMPLETED_TODO);
            AppShowStyleDTO.ListData listData3 = new AppShowStyleDTO.ListData();
            listData3.setTitle(TODAY_COMPLETE_RATE);
            AppShowStyleDTO.ListItem listItem = new AppShowStyleDTO.ListItem();
            listItem.setItems(CollUtil.toList(listData1, listData2, listData3));
            showStyleDTO.setList(listItem);

        } else {
            return;
        }
        //设置应用展示样式
        appManageService.setAppShowStyle(showStyleDTO);

        //更新代办工作台展示数据
        updateTodoWorkbenchData("all");
    }

    @Override
    public TodoFlowInfoVO getFlowInfo(String serialNo) {
        //获取当前访问信息
        ApiAccessInfoDto apiAccessInfoDto = SecurityContextHolder.get(SecurityConstant.API_ACCESS_INFO, ApiAccessInfoDto.class);
        Long appId = apiAccessInfoDto.getAppId();

        //查询待办流程信息
        LambdaQueryWrapper<TodoFlowInfo> tfiQw = Wrappers.lambdaQuery(TodoFlowInfo.class);
        tfiQw.eq(TodoFlowInfo::getSerialNo, serialNo);
        tfiQw.eq(TodoFlowInfo::getAppId, appId);
        TodoFlowInfo flowInfo = todoFlowInfoService.getOne(tfiQw);
        if (flowInfo == null) {
            return null;
        }

        return JacksonUtils.toObj(JacksonUtils.toJson(flowInfo), TodoFlowInfoVO.class);
    }

    @Override
    public List<String> listType() {
        //获取当前用户
        String userid = SecurityContextHolder.getUserId();

        return todoDefMapper.selectTypeList(userid);
    }

    @Override
    public TodoDef getCurrentNode(Long flowInfoId) {
        return this.getOne(Wrappers.lambdaQuery(TodoDef.class).eq(TodoDef::getFlowId, flowInfoId)
                .orderByDesc(TodoDef::getCreateDate).last(LIMIT_ONE));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    public void addUserTodo(AddUserTodoQO qo) {
        //获取当前访问信息
        ApiAccessInfoDto apiAccessInfoDto = SecurityContextHolder.get(SecurityConstant.API_ACCESS_INFO, ApiAccessInfoDto.class);
        //查询待办流程信息
        TodoFlowInfo flowInfo = todoFlowInfoService.getFlowInfoBySerialNoAndAppId(qo.getSerialNo(), apiAccessInfoDto.getAppId());
        if (flowInfo == null) {
            throw ExceptionCodeEnum.TODO_FLOW_NOT_EXIST.toServiceException();
        }

        // 使用分布式锁防止并发添加同一条记录导致的死锁
        RLock lock = redissonClient.getLock("TODO_ADD_LOCK:" + flowInfo.getId() + ":" + qo.getFlowNode().getNodeId());
        boolean locked = false;
        try {
            // 尝试获取锁，最多等待500毫秒，锁自动释放时间10秒
            locked = lock.tryLock(500, 10000, TimeUnit.MILLISECONDS);
            if (!locked) {
                log.warn("获取待办添加锁失败，flowId={}, nodeId={}", flowInfo.getId(), qo.getFlowNode().getNodeId());
                throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
            }

            TodoSaveFlowNodeQo flowNodeQo = TodoBeanConverter.INSTANCE.toTodoSaveFlowNodeQo(qo);
            if (qo.getFlowNode().getNodeType() != TodoFlowNodeTypeEnum.COPY_USER.getCode()) {
                //删除用户下指定节点的待办
                UserTodoRelatedIdDTO userTodoRelated = getUserTodoRelated(flowNodeQo, flowInfo.getId());
                List<Long> todoDefIdList = userTodoRelated.getTodoDefIdList();
                List<Long> todoDetailIdList = userTodoRelated.getTodoDetailIdList();
                List<Long> todoUserRelationIdList = userTodoRelated.getTodoUserRelationIdList();

                // 批量删除操作，减少数据库访问次数
                if (CollUtil.isNotEmpty(todoDefIdList)) {
                    todoDefService.removeBatchByIds(todoDefIdList);
                }
                if (CollUtil.isNotEmpty(todoDetailIdList)) {
                    todoDetailService.removeBatchByIds(todoDetailIdList);
                }
                if (CollUtil.isNotEmpty(todoUserRelationIdList)) {
                    todoUserRelationService.removeBatchByIds(todoUserRelationIdList);
                }
            }

            flowNodeQo.getFlowNode().setStartTime(DateUtil.date());
            //新增待办
            addFlowNode(flowNodeQo, flowInfo, apiAccessInfoDto);
        } catch (InterruptedException e) {
            log.error("获取待办添加锁被中断，flowId={}, nodeId={}", flowInfo.getId(), qo.getFlowNode().getNodeId(), e);
            Thread.currentThread().interrupt();
            throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
        } catch (Exception e) {
            if (!(e instanceof ServiceException)) {
                log.error("添加待办失败，flowId={}, nodeId={}", flowInfo.getId(), qo.getFlowNode().getNodeId(), e);
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("添加待办失败");
            }
            throw e;
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    public void completeUserTodo(CompleteUserTodoQO qo) {
        // 输出日志
        String serialNo = qo.getSerialNo();
        log.info("completeUserTodo日志{}：开始处理", serialNo);

        TodoSaveFlowNodeQo flowNodeQo = TodoBeanConverter.INSTANCE.toTodoSaveFlowNodeQo(qo);
        // 获取当前访问信息
        ApiAccessInfoDto apiAccessInfoDto = SecurityContextHolder.get(SecurityConstant.API_ACCESS_INFO, ApiAccessInfoDto.class);
        Long appId = apiAccessInfoDto.getAppId();
        Long nodeId = qo.getFlowNode().getNodeId();

        // 使用分布式锁防止并发更新同一条记录导致的死锁
        String lockKey = "TODO_COMPLETE_LOCK:" + serialNo + ":" + appId + ":" + nodeId;
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked = false;

        try {
            // 尝试获取锁，最多等待800毫秒，锁自动释放时间15秒
            // 增加等待时间，减少锁争用导致的失败
            locked = lock.tryLock(800, 15000, TimeUnit.MILLISECONDS);
            if (!locked) {
                log.warn("获取待办完成锁失败，serialNo={}, nodeId={}", serialNo, nodeId);
                throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
            }

            // 在获取锁后再查询流程信息，避免在获取锁前后数据发生变化
            TodoFlowInfo flowInfo = todoFlowInfoService.getFlowInfoBySerialNoAndAppId(serialNo, appId);
            if (flowInfo == null) {
                throw ExceptionCodeEnum.TODO_FLOW_NOT_EXIST.toServiceException();
            }

            // 查询节点信息
            List<TodoDef> defList = this.list(Wrappers.lambdaQuery(TodoDef.class)
                    .eq(TodoDef::getFlowId, flowInfo.getId())
                    .eq(TodoDef::getNodeId, nodeId));

            if (CollUtil.isEmpty(defList)) {
                throw ExceptionCodeEnum.TODO_FLOW_NODE_NOT_EXIST.toServiceException();
            }

            // 获取当前时间
            DateTime currentDate = DateUtil.date();
            flowNodeQo.getFlowNode().setEndTime(currentDate);

            // 获取相关ID信息
            UserTodoRelatedIdDTO userTodoRelated = getUserTodoRelated(flowNodeQo, flowInfo.getId());
            List<Long> todoDefIdList = userTodoRelated.getTodoDefIdList();
            List<Long> todoDetailIdList = userTodoRelated.getTodoDetailIdList();
            List<Long> todoUserRelationIdList = userTodoRelated.getTodoUserRelationIdList();

            if (CollUtil.isEmpty(todoDefIdList) || CollUtil.isEmpty(todoUserRelationIdList)) {
                throw ExceptionCodeEnum.TODO_USER_TODO_NOT_EXIST.toServiceException();
            }

            // 使用批量更新减少锁争用
            if (CollUtil.isNotEmpty(todoDefIdList)) {
                todoDefService.update(Wrappers.<TodoDef>lambdaUpdate().set(TodoDef::getEndTime, currentDate).in(TodoDef::getId, todoDefIdList));
            }

            if (CollUtil.isNotEmpty(todoDetailIdList)) {
                todoDetailService.update(Wrappers.<TodoDetail>lambdaUpdate().set(TodoDetail::getEndTime, currentDate)
                        .in(TodoDetail::getId, todoDetailIdList));
            }

            // 使用批量更新减少锁争用
            if (CollUtil.isNotEmpty(todoUserRelationIdList)) {
                todoUserRelationService.update(Wrappers.<TodoUserRelation>lambdaUpdate()
                        .set(TodoUserRelation::getCompleteTime, currentDate)
                        .set(TodoUserRelation::getStatus, CompleteTypeEnum.COMPLETE.getCode())
                        .in(TodoUserRelation::getId, todoUserRelationIdList));
            }


            // 异步处理缓存更新
            final Set<String> userids = qo.getFlowNode().getUserInfoList().stream()
                    .map(CompleteUserTodoFlowNodeQO.NodeUserInfo::getUserid)
                    .collect(Collectors.toSet());

            if (!userids.isEmpty()) {
                // 使用线程池异步处理缓存更新
                commonExecutor.execute(() -> cacheChangeUserIds(userids));
            }
        } catch (InterruptedException e) {
            log.error("获取待办完成锁被中断，serialNo={}, nodeId={}", serialNo, qo.getFlowNode().getNodeId(), e);
            Thread.currentThread().interrupt();
            throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
        } catch (Exception e) {
            // 如果不是已知的ServiceException，则包装为系统错误
            if (!(e instanceof ServiceException)) {
                log.error("完成待办失败，serialNo={}, nodeId={}", serialNo, qo.getFlowNode().getNodeId(), e);
                throw ExceptionCodeEnum.OPERATE_FAIL.toServiceException();
            }
            throw e;
        } finally {
            // 确保锁被正确释放
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED, timeout = 15)
    @Override
    public void delUserTodo(DelUserTodoQO qo) {
        //获取当前访问信息
        ApiAccessInfoDto apiAccessInfoDto = SecurityContextHolder.get(SecurityConstant.API_ACCESS_INFO, ApiAccessInfoDto.class);
        //查询待办流程信息
        TodoFlowInfo flowInfo = todoFlowInfoService.getFlowInfoBySerialNoAndAppId(qo.getSerialNo(), apiAccessInfoDto.getAppId());
        if (flowInfo == null) {
            throw ExceptionCodeEnum.TODO_FLOW_NOT_EXIST.toServiceException();
        }

        // 使用分布式锁防止并发删除同一条记录导致的死锁
        RLock lock = redissonClient.getLock("TODO_DELETE_LOCK:" + flowInfo.getId() + ":" + qo.getNodeId());
        boolean locked = false;
        try {
            // 尝试获取锁，最多等待500毫秒，锁自动释放时间10秒
            locked = lock.tryLock(500, 10000, TimeUnit.MILLISECONDS);
            if (!locked) {
                log.warn("获取待办删除锁失败，flowId={}, nodeId={}", flowInfo.getId(), qo.getNodeId());
                throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
            }

            //查询节点信息
            List<TodoDef> defList = this.list(Wrappers.lambdaQuery(TodoDef.class).eq(TodoDef::getFlowId, flowInfo.getId())
                    .eq(TodoDef::getNodeId, qo.getNodeId()));
            if (CollUtil.isEmpty(defList)) {
                throw ExceptionCodeEnum.TODO_FLOW_NODE_NOT_EXIST.toServiceException();
            }

            //如果传入类型是抄送节点删除抄送节点并返回
            if (qo.getNodeType() == TodoFlowNodeTypeEnum.COPY_USER.getCode()) {
                todoFlowCopyUserService.remove(Wrappers.lambdaQuery(TodoFlowCopyUser.class)
                        .eq(TodoFlowCopyUser::getFlowId, flowInfo.getId()).eq(TodoFlowCopyUser::getNodeId, qo.getNodeId())
                        .in(CollUtil.isNotEmpty(qo.getUserIds()), TodoFlowCopyUser::getUserid, qo.getUserIds()));
                return;
            }

            // 使用批量操作减少锁争用
            List<Long> defIdList = defList.stream().map(TodoDef::getId).collect(Collectors.toList());
            List<Long> todoDefIdList = new ArrayList<>();
            List<Long> todoDetailIdList = new ArrayList<>();
            List<Long> todoUserRelationIdList = new ArrayList<>();
            List<TodoUserRelation> todoUserRelations;

            // 使用单次查询减少数据库访问
            if (CollUtil.isEmpty(qo.getUserIds())) {
                todoUserRelations = todoUserRelationService.list(Wrappers.lambdaQuery(TodoUserRelation.class).in(TodoUserRelation::getTodoDefId, defIdList));
            } else {
                todoUserRelations = todoUserRelationService.list(Wrappers.lambdaQuery(TodoUserRelation.class)
                        .in(TodoUserRelation::getTodoDefId, defIdList)
                        .in(TodoUserRelation::getUserid, qo.getUserIds()));
            }

            // 收集需要删除的ID
            for (TodoUserRelation todoUserRelation : todoUserRelations) {
                todoDefIdList.add(todoUserRelation.getTodoDefId());
                todoDetailIdList.add(todoUserRelation.getTodoDetailId());
                todoUserRelationIdList.add(todoUserRelation.getId());
            }

            // 批量删除操作，减少数据库访问次数
            if (CollUtil.isNotEmpty(todoDefIdList)) {
                todoDefService.removeBatchByIds(todoDefIdList);
            }
            if (CollUtil.isNotEmpty(todoDetailIdList)) {
                todoDetailService.removeBatchByIds(todoDetailIdList);
            }
            if (CollUtil.isNotEmpty(todoUserRelationIdList)) {
                todoUserRelationService.removeBatchByIds(todoUserRelationIdList);
            }

            // 缓存变更的用户ID
            if (CollUtil.isNotEmpty(todoUserRelations)) {
                Set<String> userIds = todoUserRelations.stream()
                        .map(TodoUserRelation::getUserid)
                        .collect(Collectors.toSet());
                cacheChangeUserIds(userIds);
            }
        } catch (InterruptedException e) {
            log.error("获取待办删除锁被中断，flowId={}, nodeId={}", flowInfo.getId(), qo.getNodeId(), e);
            Thread.currentThread().interrupt();
            throw ExceptionCodeEnum.SYSTEM_BUSY.toServiceException();
        } catch (Exception e) {
            if (!(e instanceof ServiceException)) {
                log.error("删除待办失败，flowId={}, nodeId={}", flowInfo.getId(), qo.getNodeId(), e);
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("删除待办失败");
            }
            throw e;
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public void initTodoWorkbenchData() {
        //查询代办应用
        WbApp app = wbAppService.getByAgentIdAndCorpId(Integer.valueOf(agentProperties.getTodoAgentId()), agentProperties.getCorpId());
        //获取代办展示类型
        String showStyle = app.getShowStyle();
        //默认展示类型无需处理
        if (AppShowStyleEnum.NORMAL.code.equals(showStyle)) {
            return;
        }
        WxCpService wxCpService = wxCpServiceFactory.get(agentProperties.getCorpId(), agentProperties.getTodoAgentId());
        Set<String> initUserIds = new HashSet<>();
        try {
            WxCpAgent wxCpAgent = wxCpService.getAgentService().get(Integer.valueOf(agentProperties.getTodoAgentId()));
            Optional.ofNullable(wxCpAgent.getAllowUserInfos()).ifPresent(users -> {
                Optional.of(users).ifPresent(items -> {
                    Set<String> userIds = Optional.of(users.getUsers()).orElseGet(ArrayList::new).stream().map(WxCpAgent.User::getUserId).collect(Collectors.toSet());
                    initUserIds.addAll(userIds);
                });
                Optional.ofNullable(wxCpAgent.getAllowParties()).flatMap(Optional::of).ifPresent(items -> {
                    List<String> userIds = userDepartmentRelationService.listUserIdsByDepartmentIdList(items.getPartyIds());
                    initUserIds.addAll(userIds);
                });
                Optional.ofNullable(wxCpAgent.getAllowTags()).flatMap(Optional::of).ifPresent(tags -> {
                    List<String> userIds = userTagRelationService.listUserIdsByTagIdList(tags.getTagIds());
                    initUserIds.addAll(userIds);
                });
            });
        } catch (WxErrorException e) {
            log.error("获取待办应用信息失败", e);
            return;
        }

        //需要处理的用户
        List<String> useridList = new ArrayList<>(initUserIds);
        if (CollUtil.isEmpty(useridList)) {
            return;
        }

        //获取待办当前的配置
        WbAppWxset appWxset = wbAppWxsetService.getOne(Wrappers.<WbAppWxset>lambdaQuery().eq(WbAppWxset::getAgentId, Integer.valueOf(agentProperties.getTodoAgentId())));
        AppShowStyleDTO appShowStyleDTO = new AppShowStyleDTO();
        if (appWxset.getShowTemplateSet() != null) {
            appShowStyleDTO = JSONObject.parseObject(appWxset.getShowTemplateSet().toString(), AppShowStyleDTO.class);
        }

        //拆分16组
        List<List<String>> useridSplit = ListUtil.splitAvg(useridList, 16);
        for (List<String> userids : useridSplit) {
            if (CollUtil.isEmpty(userids)) {
                continue;
            }

            //处理工作台展示数据
            AppShowStyleDTO finalAppShowStyleDTO = appShowStyleDTO;
            commonExecutor.execute(() -> {
                List<TodoTodayDto> dtoList = new ArrayList<>();
                for (String userid : userids) {
                    TodoTodayDto todoTodayDto = new TodoTodayDto();
                    todoTodayDto.setUserid(userid);
                    todoTodayDto.setTodayTodo(0);
                    todoTodayDto.setTodayCompleted(0);
                    dtoList.add(todoTodayDto);
                }
                setWorkbench(dtoList, wxCpService, showStyle, finalAppShowStyleDTO);
            });
        }
    }

    /**
     * 获取用户待办关联 Id 信息
     *
     * @param qo         待办信息
     * @param flowInfoId 待办流程信息 Id
     * @return 用户待办关联 Id 信息对象
     */
    private UserTodoRelatedIdDTO getUserTodoRelated(TodoSaveFlowNodeQo qo, Long flowInfoId) {
        List<TodoDef> defList = this.list(Wrappers.lambdaQuery(TodoDef.class).eq(TodoDef::getFlowId, flowInfoId)
                .eq(TodoDef::getNodeId, qo.getFlowNode().getNodeId()));
        UserTodoRelatedIdDTO userTodoRelatedIdDTO = new UserTodoRelatedIdDTO();
        if (CollUtil.isEmpty(defList)) {
            return userTodoRelatedIdDTO;
        }
        List<Long> defIdList = defList.stream().map(TodoDef::getId).collect(Collectors.toList());
        List<TodoFlowNodeDto.NodeUserInfo> userInfoList = qo.getFlowNode().getUserInfoList();
        List<String> userIds = userInfoList.stream().map(TodoFlowNodeDto.NodeUserInfo::getUserid).collect(Collectors.toList());
        //删除节点下的用户的待办
        List<TodoUserRelation> todoUserRelationList = todoUserRelationService.list(Wrappers.lambdaQuery(TodoUserRelation.class)
                .in(TodoUserRelation::getUserid, userIds)
                .in(TodoUserRelation::getTodoDefId, defIdList));
        List<Long> todoDefIdList = new ArrayList<>();
        List<Long> todoDetailIdList = new ArrayList<>();
        List<Long> todoUserRelationIdList = new ArrayList<>();
        for (TodoUserRelation todoUserRelation : todoUserRelationList) {
            todoDefIdList.add(todoUserRelation.getTodoDefId());
            todoDetailIdList.add(todoUserRelation.getTodoDetailId());
            todoUserRelationIdList.add(todoUserRelation.getId());
        }

        userTodoRelatedIdDTO.setTodoDefIdList(todoDefIdList);
        userTodoRelatedIdDTO.setTodoDetailIdList(todoDetailIdList);
        userTodoRelatedIdDTO.setTodoUserRelationIdList(todoUserRelationIdList);
        return userTodoRelatedIdDTO;
    }

    /**
     * 设置工作台展示数据
     */
    private void setWorkbench(List<TodoTodayDto> dtoList, WxCpService wxCpService, String showStyle, AppShowStyleDTO appShowStyleDTO) {
        log.info("开始设置工作台展示数据,dtoList数量: {}", dtoList.size());
        for (TodoTodayDto dto : dtoList) {
            try (TodoWorkbenchMetrics.SingleUserRecorder recorder = todoWorkbenchMetrics.singleUserExecution()) {
                WxCpAgentWorkBench workBench = new WxCpAgentWorkBench();
                workBench.setAgentId(Long.valueOf(agentProperties.getTodoAgentId()));
                workBench.setUserId(dto.getUserid());
                workBench.setType(showStyle);


                //设置工作台展示数据
                if (AppShowStyleEnum.KEY_DATA.code.equals(showStyle)) {
                    //设置关键数据类型展示数据
                    setKeyData(dto, workBench, appShowStyleDTO);
                } else if (AppShowStyleEnum.LIST.code.equals(showStyle)) {
                    //设置列表类型展示数据
                    setList(dto.getTodayTodo(), dto.getTodayCompleted(), dto.getTodayCompleteRate(), workBench);
                }

                //设置工作台展示数据至企微
                wxCpService.getWorkBenchService().setWorkBenchData(workBench);
                log.info("人员：{}，设置工作台展示数据至企微成功，展示类型：{}", dto.getUserid(), JSON.toJSONString(workBench));
            } catch (WxErrorException e) {
                log.error("人员：{}，设置工作台展示数据至企微失败，错误码：{}，失败信息：{}", dto.getUserid(), e.getError().getErrorCode(), e.getError().getErrorMsg());
            } catch (Exception e) {
                log.error("人员：{}，设置工作台展示数据至企微失败", dto.getUserid(), e);
            }
        }
        log.info("结束设置工作台展示数据");
    }

    /**
     * 设置列表类型展示数据
     */
    private void setList(Integer todayTodo, Integer todayCompleted, String todayCompleteRate, WxCpAgentWorkBench workBench) {
        WorkBenchList list1 = new WorkBenchList();
        list1.setTitle(TODAY_TODO + "：" + todayTodo);
        WorkBenchList list2 = new WorkBenchList();
        list2.setTitle(TODAY_COMPLETED_TODO + "：" + todayCompleted);
        WorkBenchList list3 = new WorkBenchList();
        list3.setTitle(TODAY_COMPLETE_RATE + "：" + todayCompleteRate);
        workBench.setLists(CollUtil.toList(list1, list2, list3));
    }

    /**
     * 设置关键数据类型展示数据
     */
    private void setKeyData(TodoTodayDto todoTodayDto, WxCpAgentWorkBench workBench, AppShowStyleDTO appShowStyleDTO) {
        AppShowStyleDTO.KeyDataItem keyData = appShowStyleDTO.getKeyData();
        if (Objects.isNull(keyData)) {
            return;
        }
        List<WorkBenchKeyData> keyDataList = new ArrayList<>();
        for (AppShowStyleDTO.KeyData item : keyData.getItems()) {
            WorkBenchKeyData workBenchKeyData = new WorkBenchKeyData();
            workBenchKeyData.setKey(item.getKey());
            if (TODAY_TODO.equals(item.getKey())) {
                workBenchKeyData.setData(String.valueOf(todoTodayDto.getTodayTodo()));
            } else if (TODAY_COMPLETED_TODO.equals(item.getKey())) {
                workBenchKeyData.setData(String.valueOf(todoTodayDto.getTodayCompleted()));
            } else if (TODAY_COMPLETE_RATE.equals(item.getKey())) {
                workBenchKeyData.setData(todoTodayDto.getTodayCompleteRate());
            } else if (CURRENT_YEAR_COMPLETED.equals(item.getKey())) {
                workBenchKeyData.setData(String.valueOf(todoTodayDto.getCurrentYearCompleted()));
            } else if (TOTAL_COMPLETED.equals(item.getKey())) {
                workBenchKeyData.setData(String.valueOf(todoTodayDto.getTotalCompleted()));
            }
            keyDataList.add(workBenchKeyData);
        }
        workBench.setKeyDataList(keyDataList);
    }


    /**
     * 填充字段信息
     */
    private void fillField(IPage<TodoDefVo> pageVo) {
        for (TodoDefVo vo : pageVo.getRecords()) {
            //是否重复
            vo.setIsRepeatDesc(EnumUtil.getFieldBy(YnEnum::getDescription, YnEnum::getValue, vo.getIsRepeat()));
            //是否提醒
            vo.setIsRemindDesc(EnumUtil.getFieldBy(YnEnum::getDescription, YnEnum::getValue, vo.getIsRemind()));
            //提醒
            if (CollUtil.isNotEmpty(vo.getRemindTimeDiffs())) {
                List<String> remindTimeDiffsDesc = vo.getRemindTimeDiffs().stream().map(item ->
                        EnumUtil.getFieldBy(TodoRemindTimeDiffsEnum::getMsg, TodoRemindTimeDiffsEnum::getCode, item)).collect(Collectors.toList());
                vo.setRemindTimeDiffsDesc(remindTimeDiffsDesc);
            }
            //完成方式
            if (CollUtil.isNotEmpty(vo.getCompleteMode())) {
                List<String> completeModeDesc = vo.getCompleteMode().stream().map(item ->
                        EnumUtil.getFieldBy(TodoCompleteModeEnum::getMsg, TodoCompleteModeEnum::getCode, item)).collect(Collectors.toList());
                vo.setCompleteModeDesc(completeModeDesc);
            }
            //重复信息
            if (YnEnum.YES.getValue() == vo.getIsRepeat()) {
                vo.setRepeatInfo(genRepeatInfo(vo.getRepeatType(), vo.getRepeatInterval(), vo.getRepeatUntil(), vo.getRepeatDayOfWeek(), vo.getRepeatDayOfMonth()));
            }
        }
    }

    /**
     * 生成重复信息
     *
     * @param repeatType       重复类型
     * @param repeatInterval   重复间隔
     * @param repeatUntil      重复截止日期
     * @param repeatDayOfWeek  每周周几重复
     * @param repeatDayOfMonth 每月哪几天重复
     */
    private String genRepeatInfo(Integer repeatType, Integer repeatInterval, Date repeatUntil, List<Integer> repeatDayOfWeek, List<Integer> repeatDayOfMonth) {
        //拼接该样式：每周（周五），直到2024-01-31
        StringBuilder repeatInfo = new StringBuilder();
        if (TodoRepeatTypeEnum.WEEK.getCode() == repeatType) {
            repeatInfo.append("每").append(repeatInterval).append("个周");
            //拼接每周
            String repeatDayOfWeekStr = repeatDayOfWeek.stream().map(item -> {
                //Calendar中关于Week的int值，1表示Sunday，2表示Monday ~ 到7表示Saturday
                if (item == 7) {
                    return Week.of(1).toChinese("周");
                } else {
                    return Week.of(item + 1).toChinese("周");
                }
            }).collect(Collectors.joining(","));
            repeatInfo.append("（").append(repeatDayOfWeekStr).append("），");
        } else if (TodoRepeatTypeEnum.MONTH.getCode() == repeatType) {
            repeatInfo.append("每").append(repeatInterval).append("个月");
            //拼接每月
            String repeatDayOfMonthStr = repeatDayOfMonth.stream().map(item -> item + "号").collect(Collectors.joining(","));
            repeatInfo.append("（").append(repeatDayOfMonthStr).append("），");
        } else if (TodoRepeatTypeEnum.DAY.getCode() == repeatType) {
            //拼接每天
            repeatInfo.append("每天重复");
        }
        repeatInfo.append("直到").append(DateUtil.format(repeatUntil, DatePattern.NORM_DATE_PATTERN));
        return repeatInfo.toString();
    }
}

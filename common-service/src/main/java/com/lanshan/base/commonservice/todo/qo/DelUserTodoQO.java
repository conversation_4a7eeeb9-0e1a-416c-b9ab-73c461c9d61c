package com.lanshan.base.commonservice.todo.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 删除用户待办
 */
@ApiModel(value = "删除用户待办对象")
@Data
public class DelUserTodoQO {

    @ApiModelProperty("流水号")
    @NotBlank(message = "流水号不能为空")
    private String serialNo;

    @ApiModelProperty("节点id")
    @NotNull(message = "节点id不能为空")
    private Long nodeId;

    @ApiModelProperty("节点类型")
    @NotNull(message = "节点类型不能为空")
    private Integer nodeType;

    @ApiModelProperty("用户id列表")
    private List<String> userIds;
}

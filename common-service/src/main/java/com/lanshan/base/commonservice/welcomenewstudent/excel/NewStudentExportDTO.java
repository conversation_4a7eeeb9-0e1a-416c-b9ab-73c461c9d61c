package com.lanshan.base.commonservice.welcomenewstudent.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 新生错误数据导出对象
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("新生错误数据导出对象")
public class NewStudentExportDTO extends NewStudentImportDTO {

    @ExcelProperty(value = "错误信息", index = 12)
    private String errorMsg;
}

package com.lanshan.base.commonservice.access.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lanshan.base.api.constant.CommonConstant;
import com.lanshan.base.api.dto.app.AppDTO;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.enums.YnEnum;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.access.converter.AcAppConverter;
import com.lanshan.base.commonservice.access.dao.AcAppDao;
import com.lanshan.base.commonservice.access.dao.AcCompanyDao;
import com.lanshan.base.commonservice.access.entity.AcApiControl;
import com.lanshan.base.commonservice.access.entity.AcApp;
import com.lanshan.base.commonservice.access.entity.AcCompany;
import com.lanshan.base.commonservice.access.entity.AcDataScope;
import com.lanshan.base.commonservice.access.enums.ApiControlTypeEnum;
import com.lanshan.base.commonservice.access.enums.EnableStatusEnum;
import com.lanshan.base.commonservice.access.qo.*;
import com.lanshan.base.commonservice.access.service.AcApiControlService;
import com.lanshan.base.commonservice.access.service.AcAppService;
import com.lanshan.base.commonservice.access.service.AcDataScopeService;
import com.lanshan.base.commonservice.access.utils.StpAppUtil;
import com.lanshan.base.commonservice.access.vo.AcAppVO;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.workbench.event.AppChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 接入方应用(AcApp)表服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("acAppService")
public class AcAppServiceImpl extends ServiceImpl<AcAppDao, AcApp> implements AcAppService {

    @Resource
    private AcApiControlService acApiControlService;

    @Resource
    private AcDataScopeService acDataScopeService;

    @Resource
    private AcCompanyDao acCompanyDao;

    @Resource
    private RedisService redisService;

    @Resource(name = "commonTaskExecutor")
    private ThreadPoolTaskExecutor commonExecutor;
    @Resource
    private AcApiServiceImpl acApiService;

    private final Object cacheLock = new Object();

    /**
     * 项目启动时，初始化APP列表到缓存
     */
    @PostConstruct
    public void initCache() {
        commonExecutor.execute(this::cacheApp);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(AcAppQO qo) {
        LambdaQueryWrapper<AcApp> queryWrapper = Wrappers.lambdaQuery(AcApp.class);
        queryWrapper.eq(AcApp::getCompanyId, qo.getCompanyId())
                .eq(AcApp::getAppName, qo.getAppName());
        //查询应用是否存在
        boolean exists = super.exists(queryWrapper);
        if (exists) {
            throw ExceptionCodeEnum.APP_NAME_EXIST.toServiceException();
        }

        //生成keyId（长度24）和secret（长度32）
        String keyId = IdUtil.objectId();
        String secret = IdUtil.fastSimpleUUID();

        //新增应用
        AcApp acApp = AcAppConverter.INSTANCE.toEntity(qo);
        acApp.setKeyId(keyId);
        acApp.setSecret(secret);
        acApp.setCreateBy(SecurityContextHolder.getUserId());
        super.save(acApp);

        Long appId = acApp.getId();
        // 新增API权限控制
        addApiControl(qo.getApiIdList(), appId);

        // 新增数据权限控制
        if (CollUtil.isNotEmpty(qo.getDataScopeList())) {
            addDataScope(qo.getDataScopeList(), Collections.singletonList(appId));
        }
        //更新缓存
        refreshCache();
    }

    @EventListener(classes = {AppChangeEvent.class})
    public void handleWbAppCreate(AppChangeEvent event) {

        if (!(event.getSource() instanceof AppDTO)) {
            return;
        }
        if (!AppChangeEvent.ChangeType.CREATE.equals(event.getChangeType())
                && !AppChangeEvent.ChangeType.UPDATE.equals(event.getChangeType())) {
            return;
        }
        AppDTO dto = (AppDTO) event.getSource();
        dto.setId(event.getAppIds().get(0));

        AcAppQO appQO = AcAppConverter.INSTANCE.toAppQO(dto);

        AcCompany acCompany = acCompanyDao.selectById(dto.getCompanyId());
        if (acCompany != null) {
            appQO.setCompanyName(acCompany.getName());
        }

        AcApp acApp = getById(appQO.getId());
        if (acApp == null) {
            appQO.setOperateType(1);
            appQO.setStatus(EnableStatusEnum.ENABLE.getCode());
            appQO.setNeedUserAuth("0");
            SpringUtil.getBean(AcAppService.class).save(appQO);
        } else {
            SpringUtil.getBean(AcAppService.class).update(appQO);
        }
    }

    @Override
    public AcApp getById(Serializable id) {
        if (id == null) {
            return null;
        }
        // 使用缓存
        String cacheKey = CommonServiceRedisKeys.ACCESS_APP_MAP;
        if (Boolean.TRUE.equals(redisService.hasKey(cacheKey))) {
            Map<String, AcApp> appMap = redisService.getCacheMap(cacheKey);
            Map<String, AcApp> acAppMap = JacksonUtils.toObj(JacksonUtils.toJson(appMap), new TypeReference<Map<String, AcApp>>() {
            });
            AcApp app = acAppMap.get(id.toString());
            if (app != null) {
                return app;
            }
        }

        // 从数据库查询并缓存单个应用
        AcApp app = super.getById(id);
        if (app != null) {
            redisService.setCacheMapValue(cacheKey, id.toString(), app);
        }
        return app;
    }


    @EventListener(classes = {AppChangeEvent.class})
    public void handleWbAppDelete(AppChangeEvent event) {
        if (AppChangeEvent.ChangeType.DELETE.equals(event.getChangeType())) {
            List<Long> appIds = event.getAppIds();
            acCompanyDao.deleteBatchIds(appIds);
        }
    }

    /**
     * 新增数据权限控制
     */
    private void addDataScope(List<AcDataScopeQO> dataScopeList, List<Long> appIdList) {
        List<AcDataScope> entityList = new ArrayList<>();
        for (Long appId : appIdList) {
            List<AcDataScope> dataScopes = dataScopeList.stream().map(item ->
                    AcDataScope.builder()
                            .dataKey(item.getDataKey())
                            .dataName(item.getDataName())
                            .scopeValue(item.getScopeValue())
                            .appId(appId).build()).collect(Collectors.toList());
            entityList.addAll(dataScopes);
        }
        //批量新增数据权限控制
        acDataScopeService.saveBatch(entityList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AcAppQO qo) {
        AcApp acApp = getById(qo.getId());
        //查询应用是否存在
        if (acApp == null) {
            throw ExceptionCodeEnum.APP_NOT_EXIST.toServiceException();
        }

        acApp.setUpdateBy(SecurityContextHolder.getUserId());
        acApp.setUpdateTime(new Date());
        //更新应用
        super.updateById(AcAppConverter.INSTANCE.toEntity(qo));

        //更新缓存
        cacheApp();
        acApiControlService.cacheApiControl();
    }

    @Override
    public IPage<AcAppVO> pageApp(AcAppPageQO pageQO) {
        Page<AcApp> page = new Page<>(pageQO.getPage(), pageQO.getSize());
        LambdaQueryWrapper<AcApp> queryWrapper = Wrappers.lambdaQuery(AcApp.class);

        //操作类型
        if (pageQO.getOperateType() != null) {
            queryWrapper.eq(AcApp::getOperateType, pageQO.getOperateType());
        }
        //应用名称
        if (StringUtils.isNotBlank(pageQO.getAppName())) {
            queryWrapper.like(AcApp::getAppName, pageQO.getAppName());
        }
        //接入方id
        if (pageQO.getCompanyId() != null) {
            queryWrapper.eq(AcApp::getCompanyId, pageQO.getCompanyId());
        }
        //接入方名称
        if (StringUtils.isNotBlank(pageQO.getCompanyName())) {
            queryWrapper.like(AcApp::getCompanyName, pageQO.getCompanyName());
        }
        //id列表
        if (CollUtil.isNotEmpty(pageQO.getIdList())) {
            queryWrapper.in(AcApp::getId, pageQO.getIdList());
        }
        //apiId
        if (pageQO.getApiId() != null) {
            //查询API对应的应用
            List<AcApiControl> list = acApiControlService.list(Wrappers.lambdaQuery(AcApiControl.class)
                    .eq(AcApiControl::getApiId, pageQO.getApiId())
                    .eq(AcApiControl::getControlType, ApiControlTypeEnum.APP.getCode()));
            if (CollUtil.isEmpty(list)) {
                return new Page<>(pageQO.getPage(), pageQO.getSize());
            }

            queryWrapper.in(AcApp::getId, list.stream().map(AcApiControl::getControlId).collect(Collectors.toList()));
        }

        //按照创建时间倒序排序
        queryWrapper.orderByDesc(AcApp::getCreateTime);

        //分页查询
        IPage<AcApp> result = super.page(page, queryWrapper);
        if (CollUtil.isEmpty(result.getRecords())) {
            return new Page<>(pageQO.getPage(), pageQO.getSize());
        }

        //转换VO
        IPage<AcAppVO> pageVo = result.convert(AcAppConverter.INSTANCE::toVO);
        for (AcAppVO vo : pageVo.getRecords()) {
            //设置状态
            vo.setStatusDesc(EnumUtil.getFieldBy(EnableStatusEnum::getMsg, EnableStatusEnum::getCode, vo.getStatus()));
            //如果过期，则将状态设置为过期
            if (DateUtil.compare(vo.getEndTime(), DateUtil.date(), DatePattern.NORM_DATE_PATTERN) < 0) {
                vo.setStatusDesc(CommonConstant.EXPIRED);
            }
            //设置是否需要用户授权
            vo.setNeedUserAuthDesc(EnumUtil.getFieldBy(YnEnum::getDescription, YnEnum::getValue, vo.getNeedUserAuth()));
        }

        return pageVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchChangeStatus(BatchChangeStatusQO qo) {
        List<Long> idList = qo.getIdList();
        LambdaUpdateWrapper<AcApp> updateWrapper = Wrappers.lambdaUpdate(AcApp.class)
                .set(AcApp::getStatus, qo.getStatus())
                .in(AcApp::getId, idList);
        //批量改变状态
        super.update(updateWrapper);

        //如果是禁用，则将APP对应的token清除
        for (Long id : idList) {
            StpAppUtil.kickout(id);
        }
    }

    @Override
    public List<AcAppVO> listByDataKey(String dataKey) {
        LambdaQueryWrapper<AcDataScope> queryWrapper = Wrappers.lambdaQuery(AcDataScope.class);
        queryWrapper.eq(AcDataScope::getDataKey, dataKey);
        //根据dataKey查询appId
        List<AcDataScope> dataScopeList = acDataScopeService.list(queryWrapper);
        if (CollUtil.isEmpty(dataScopeList)) {
            return Collections.emptyList();
        }
        List<Long> appIdList = dataScopeList.stream().map(AcDataScope::getAppId).distinct().collect(Collectors.toList());
        //根据appId不分页查询应用
        AcAppPageQO appPageQO = AcAppPageQO.builder().idList(appIdList).build();
        appPageQO.setSize(Long.MAX_VALUE);
        IPage<AcAppVO> acAppVOIPage = pageApp(appPageQO);

        return acAppVOIPage.getRecords();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSetData(BatchSetDataQO qo) {
        //删除旧的指定数据权限控制
        LambdaQueryWrapper<AcDataScope> queryWrapper = Wrappers.lambdaQuery(AcDataScope.class);
        queryWrapper.in(AcDataScope::getAppId, qo.getAppIdList());
        queryWrapper.eq(AcDataScope::getDataKey, qo.getAcDataScope().getDataKey());
        acDataScopeService.remove(queryWrapper);

        //批量新增数据权限控制
        addDataScope(Collections.singletonList(qo.getAcDataScope()), qo.getAppIdList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delByIdList(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        super.removeByIds(idList);

        // 异步处理缓存和关联数据
        commonExecutor.execute(() -> {
            try {
                // 删除相关数据
                deleteRelatedData(idList);

                // 更新缓存
                refreshCache();

                log.info("成功删除{}个应用及其关联数据", idList.size());
            } catch (Exception e) {
                log.error("异步处理应用删除后续操作异常", e);
            }
        });
    }


    /**
     * 删除应用相关的关联数据
     */
    private void deleteRelatedData(List<Long> appIds) {
        // 删除API控制数据
        LambdaQueryWrapper<AcApiControl> apiControlWrapper = Wrappers.lambdaQuery(AcApiControl.class)
                .eq(AcApiControl::getControlType, ApiControlTypeEnum.APP.getCode())
                .in(AcApiControl::getControlId, appIds);
        acApiControlService.remove(apiControlWrapper);

        // 删除数据权限
        LambdaQueryWrapper<AcDataScope> dataScopeWrapper = Wrappers.lambdaQuery(AcDataScope.class)
                .in(AcDataScope::getAppId, appIds);
        acDataScopeService.remove(dataScopeWrapper);
    }

    /**
     * 刷新所有相关缓存
     */
    private void refreshCache() {
        // 刷新应用缓存
        cacheApp();

        // 刷新API控制缓存
        acApiControlService.cacheApiControl();

        redisService.deleteObject(CommonServiceRedisKeys.ACCESS_APP_MAP);
    }

    @Override
    public List<AcApp> cacheApp() {
        try {
            synchronized (cacheLock) {
                List<AcApp> list = super.list();
                if (CollUtil.isNotEmpty(list)) {
                    //缓存到redis
                    Map<String, AcApp> apiMap = CollUtil.toMap(list, null, item -> String.valueOf(item.getId()), item -> item);
                    redisService.deleteObject(CommonServiceRedisKeys.ACCESS_APP_MAP);
                    redisService.setCacheMap(CommonServiceRedisKeys.ACCESS_APP_MAP, apiMap);

                    log.info("成功缓存{}个应用到Redis", list.size());
                }
                return list;
            }
        } catch (Exception e) {
            log.error("缓存应用到Redis失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<AcApp> listByCache() {
        List<AcApp> list;
        //判断缓存中是否存数据
        if (Boolean.TRUE.equals(redisService.hasKey(CommonServiceRedisKeys.ACCESS_APP_MAP))) {
            //从缓存中获取数据
            Map<String, AcApp> apiMap = redisService.getCacheMap(CommonServiceRedisKeys.ACCESS_APP_MAP);
            list = JacksonUtils.toObj(JacksonUtils.toJson(apiMap.values()), new TypeReference<>() {
            });
        } else {
            //如果缓存中不存在，则从数据库中查询并放入缓存
            synchronized (cacheLock) {
                list = cacheApp();
            }
        }
        return list;
    }

    @Override
    public Map<Long, Long> getAppCompanyMap() {
        // 查询所有正常状态的应用
        List<AcApp> appList = super.list(Wrappers.lambdaQuery(AcApp.class).eq(AcApp::getStatus, EnableStatusEnum.ENABLE.getCode()));
        // 将appId和companyId的映射关系
        return appList.stream().filter(item -> item.getCompanyId() != null).collect(Collectors.toMap(AcApp::getId, AcApp::getCompanyId));
    }

    @Override
    public Boolean saveAppApiList(AppApiDTO dto) {

        processApiControl(dto.getApiIdList(), dto.getAppId());

        //更新缓存
        acApiControlService.cacheApiControl();
        acApiService.cacheApi();
        return Boolean.TRUE;
    }

    @Override
    public Boolean saveAppDataScope(List<AcDataScopeQO> dataScopeList) {
        List<Long> appIdList = dataScopeList.stream().map(AcDataScopeQO::getAppId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        processDataScope(dataScopeList, appIdList);
        return Boolean.TRUE;
    }

    /**
     * 批量处理数据权限控制 删除旧数据、新增新数据
     */
    private void processDataScope(List<AcDataScopeQO> dataScopeList, List<Long> appIdList) {
        //删除旧的数据权限控制
        LambdaQueryWrapper<AcDataScope> queryWrapper = Wrappers.lambdaQuery(AcDataScope.class);
        queryWrapper.in(AcDataScope::getAppId, appIdList);
        acDataScopeService.remove(queryWrapper);

        //新增数据权限控制
        if (CollUtil.isNotEmpty(dataScopeList)) {
            addDataScope(dataScopeList, appIdList);
        }
    }

    /**
     * 处理API权限控制 删除旧数据、新增新数据
     */
    private void processApiControl(List<Long> apiIds, Long acAppId) {
        LambdaQueryWrapper<AcApiControl> queryWrapper = Wrappers.lambdaQuery(AcApiControl.class)
                .eq(AcApiControl::getControlType, ApiControlTypeEnum.APP.getCode())
                .eq(AcApiControl::getControlId, acAppId);
        //删除旧的API权限控制
        acApiControlService.remove(queryWrapper);

        //新增API权限控制
        addApiControl(apiIds, acAppId);
    }

    /**
     * 新增API权限控制
     */
    private void addApiControl(List<Long> apiIds, Long appId) {
        if (CollUtil.isNotEmpty(apiIds)) {
            List<AcApiControl> apiControlList = apiIds.stream().map(item ->
                    AcApiControl.builder()
                            .apiId(item)
                            .controlType(ApiControlTypeEnum.APP.getCode())
                            .controlId(appId)
                            .build()).collect(Collectors.toList());
            acApiControlService.saveBatch(apiControlList);
        }
    }
}


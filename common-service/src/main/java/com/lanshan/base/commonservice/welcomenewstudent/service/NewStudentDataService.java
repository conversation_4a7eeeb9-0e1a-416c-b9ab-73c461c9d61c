package com.lanshan.base.commonservice.welcomenewstudent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentExportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentStatVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 新生信息表(NewStudentData)表服务接口
 *
 * <AUTHOR>
 */
public interface NewStudentDataService extends IService<NewStudentData> {

    /**
     * 获取新生信息统计
     *
     * @return 新生信息统计
     */
    List<NewStudentStatVO> getNewStudentStat(String year);

    /**
     * 导入新生信息
     *
     * @param file 文件
     */
    Boolean importData(MultipartFile file);

    /**
     * 获取导入新生信息错误数据
     *
     * @return 新生信息错误数据
     */
    List<NewStudentExportDTO> getImportNewStudentErrorData();

    /**
     * 发送欢迎信息
     */
    void sendWelcomeMsg();

    /**
     * 新生入群
     */
    void joinUserToWelcomeGroup();

    /**
     * 导出错误数据
     *
     * @param response 响应
     */
    void exportErrorData(HttpServletResponse response);
}


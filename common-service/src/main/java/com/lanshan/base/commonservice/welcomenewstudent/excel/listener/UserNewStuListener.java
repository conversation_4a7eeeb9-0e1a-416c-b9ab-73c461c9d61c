package com.lanshan.base.commonservice.welcomenewstudent.excel.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup;
import com.lanshan.base.commonservice.welcomenewstudent.enums.NewStuGroupTypeEnum;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentExportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.excel.NewStudentImportDTO;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

/**
 * 新生导入监听器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class UserNewStuListener implements ReadListener<NewStudentImportDTO> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 1000;

    private final RedisService redisService;

    private final NewStudentDataService newStudentDataService;

    private final NewStudentGroupService newStudentGroupService;

    private final String groupType;

    private final List<NewStudentExportDTO> errorImportDataList = new ArrayList<>(BATCH_COUNT);

    private final LongAdder successAdder = new LongAdder();

    /**
     * 缓存的数据
     */
    private final List<NewStudentImportDTO> cachedDataList = new ArrayList<>(BATCH_COUNT * 2);

    public UserNewStuListener(RedisService redisService, NewStudentDataService newStudentDataService, NewStudentGroupService newStudentGroupService, String groupType) {
        this.redisService = redisService;
        this.newStudentDataService = newStudentDataService;
        this.newStudentGroupService = newStudentGroupService;
        this.groupType = groupType;
    }

    @Override
    public void invoke(NewStudentImportDTO data, AnalysisContext context) {
        //校验数据完整性
        if (!checkParam(data)) {
            return;
        }
        //将数据缓存到集合中
        cachedDataList.add(data);
        //当达到BATCH_COUNT时，进行存储并清空当前缓存数据
        if (cachedDataList.size() >= BATCH_COUNT) {
            doSaveUserInfo();
            cachedDataList.clear();
        }
        successAdder.increment();
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("所有数据解析完成！,成功条数：{},失败条数：{}", successAdder.longValue(), errorImportDataList.size());
        //保存错误人员数据到缓存中
        redisService.deleteObject(CommonServiceRedisKeys.USER_INFO_NEW_STUDENT_IMPORT_ERROR_LIST);
        if (CollUtil.isNotEmpty(errorImportDataList)) {
            redisService.setCacheList(CommonServiceRedisKeys.USER_INFO_NEW_STUDENT_IMPORT_ERROR_LIST, errorImportDataList);
        }
        log.info("保存错误人员数据到缓存中，条数：{}", errorImportDataList.size());
        if (CollUtil.isNotEmpty(cachedDataList)) {
            doSaveUserInfo();
        }
    }

    private boolean checkParam(NewStudentImportDTO data) {
        String errorMsg = "";
        if (StrUtil.isBlank(data.getName())) {
            errorMsg = errorMsg + "姓名不能为空；";
        }
        if (StrUtil.isBlank(data.getIdentityType())) {
            errorMsg = errorMsg + "证件类型不能为空；";
        }
        if (StrUtil.isBlank(data.getIdCardNum())) {
            errorMsg = errorMsg + "证件号码不能为空；";
        }
        switch (NewStuGroupTypeEnum.getByValue(groupType)) {
            case COLLEGE:
                if (StrUtil.isBlank(data.getCollegeCode())) {
                    errorMsg = errorMsg + "院系编号不能为空；";
                }
                if (StrUtil.isBlank(data.getCollegeName())) {
                    errorMsg = errorMsg + "院系名称不能为空；";
                }
                break;
            case MAJOR:
                if (StrUtil.isBlank(data.getCollegeCode())) {
                    errorMsg = errorMsg + "院系编号不能为空；";
                }
                if (StrUtil.isBlank(data.getCollegeName())) {
                    errorMsg = errorMsg + "院系名称不能为空；";
                }
                if (StrUtil.isBlank(data.getMajorCode())) {
                    errorMsg = errorMsg + "专业编号不能为空；";
                }
                if (StrUtil.isBlank(data.getMajorCode())) {
                    errorMsg = errorMsg + "专业名称不能为空；";
                }
                break;
            case CLASS:
                if (StrUtil.isBlank(data.getCollegeCode())) {
                    errorMsg = errorMsg + "院系编号不能为空；";
                }
                if (StrUtil.isBlank(data.getCollegeName())) {
                    errorMsg = errorMsg + "院系名称不能为空；";
                }
                if (StrUtil.isBlank(data.getMajorCode())) {
                    errorMsg = errorMsg + "专业编号不能为空；";
                }
                if (StrUtil.isBlank(data.getMajorName())) {
                    errorMsg = errorMsg + "专业名称不能为空；";
                }
                if (StrUtil.isBlank(data.getClassCode())) {
                    errorMsg = errorMsg + "班级编号不能为空；";
                }
                if (StrUtil.isBlank(data.getClassName())) {
                    errorMsg = errorMsg + "班级名称不能为空；";
                }
                break;
            default:
                if (StrUtil.isBlank(data.getCollegeCode())) {
                    errorMsg = errorMsg + "院系编号不能为空；";
                }
                if (StrUtil.isBlank(data.getCollegeName())) {
                    errorMsg = errorMsg + "院系名称不能为空；";
                }
                break;
        }
        if (StrUtil.isNotBlank(errorMsg)) {
            NewStudentExportDTO exportDTO = new NewStudentExportDTO();
            BeanUtil.copyProperties(data, exportDTO);
            exportDTO.setErrorMsg(errorMsg);
            errorImportDataList.add(exportDTO);
            return false;
        }
        return true;
    }

    /**
     * 批量保存
     */
    private void doSaveUserInfo() {
        if (cachedDataList.isEmpty()) {
            return;
        }
        String thisYear = String.valueOf(DateUtil.thisYear());
        Map<String, String> groupMap = new HashMap<>();
        //查询 userInfo 表中是否已存在改身份证号的数据，如果存在则不用导入
        Set<String> idCardSet = cachedDataList.stream().map(NewStudentImportDTO::getIdCardNum).collect(Collectors.toSet());
        List<NewStudentData> existUserInfoList = newStudentDataService.list(Wrappers.<NewStudentData>lambdaQuery()
                .eq(NewStudentData::getDeleteFlag, false)
                .eq(NewStudentData::getYear, thisYear)
                .in(NewStudentData::getIdCardNum, idCardSet));

        Map<String, NewStudentData> existStuMap = Optional.ofNullable(existUserInfoList).orElseGet(ArrayList::new).stream()
                .collect(Collectors.toMap(NewStudentData::getIdCardNum, item -> item, (o, n) -> o));
        //判断数据是否已经存在,如果存在则更新，如果不存在则新增，已经认证过的不更新
        List<NewStudentData> insertList = new ArrayList<>();
        List<NewStudentData> updateList = new ArrayList<>();
        for (NewStudentImportDTO data : cachedDataList) {
            NewStudentData existNewStu = existStuMap.getOrDefault(data.getIdCardNum(), null);
            //如果有值判断年份是否一致，判断是否已认证，如果未认证允许修改。
            if (Objects.nonNull(existNewStu)) {
                BeanUtil.copyProperties(data, existNewStu, "id");
                updateList.add(existNewStu);
            } else {
                NewStudentData newStudentData = new NewStudentData();
                BeanUtils.copyProperties(data, newStudentData);
                newStudentData.setYear(thisYear);
                insertList.add(newStudentData);
            }
            switch (NewStuGroupTypeEnum.getByValue(groupType)) {
                case COLLEGE:
                    groupMap.put(thisYear + data.getCollegeCode(), data.getCollegeName());
                    break;
                case MAJOR:
                    groupMap.put(thisYear + data.getCollegeCode() + data.getMajorCode(), data.getCollegeName() + data.getMajorName());
                    break;
                case CLASS:
                    groupMap.put(thisYear + data.getCollegeCode() + data.getMajorCode() + data.getClassCode(), data.getCollegeName() + data.getMajorName() + data.getClassName());
                    break;
                default:
                    groupMap.put(thisYear + data.getCollegeCode(), data.getCollegeName());
                    break;
            }
        }
        if (CollUtil.isNotEmpty(insertList)) {
            //保存数据到新生表
            newStudentDataService.saveBatch(insertList, BATCH_COUNT);
        }
        if (CollUtil.isNotEmpty(updateList)) {
            //更新数据到新生表
            newStudentDataService.updateBatchById(updateList);
        }
        if (CollUtil.isNotEmpty(groupMap)) {
            createNewStuGroup(groupMap);
        }
    }

    /**
     * 创建新生群信息
     *
     * @param groupMap 新生群信息
     */
    private void createNewStuGroup(Map<String, String> groupMap) {
        Set<String> groupCodeSet = groupMap.keySet();
        List<NewStudentGroup> dbNewStudentGroupList = newStudentGroupService
                .list(Wrappers.<NewStudentGroup>lambdaQuery().in(NewStudentGroup::getGroupCode, groupCodeSet));
        List<NewStudentGroup> insertList = new ArrayList<>();
        List<NewStudentGroup> updateList = new ArrayList<>();
        for (NewStudentGroup studentGroup : dbNewStudentGroupList) {
            groupMap.remove(studentGroup.getGroupCode());
            if (StrUtil.isNotBlank(studentGroup.getChatId())) {
                continue;
            }
            studentGroup.setGroupName(groupMap.get(studentGroup.getGroupCode()));
            updateList.add(studentGroup);
        }
        Set<Map.Entry<String, String>> entries = groupMap.entrySet();
        for (Map.Entry<String, String> entry : entries) {
            NewStudentGroup newStudentGroup = new NewStudentGroup();
            newStudentGroup.setGroupCode(entry.getKey());
            newStudentGroup.setGroupName(entry.getValue() + "(新生群)");
            newStudentGroup.setYear(DateUtil.thisYear() + "");
            insertList.add(newStudentGroup);
        }
        if (CollUtil.isNotEmpty(insertList)) {
            newStudentGroupService.saveBatch(insertList);
        }
        if (CollUtil.isNotEmpty(updateList)) {
            newStudentGroupService.updateBatchById(updateList);
        }
    }
}

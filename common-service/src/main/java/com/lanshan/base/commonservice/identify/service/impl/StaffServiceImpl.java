package com.lanshan.base.commonservice.identify.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.exception.ServiceException;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.config.rabbitmq.MqConstant;
import com.lanshan.base.commonservice.identify.dto.AddStaffDto;
import com.lanshan.base.commonservice.identify.dto.StaffImportDto;
import com.lanshan.base.commonservice.identify.entity.IdentifyOperateLogEntity;
import com.lanshan.base.commonservice.identify.entity.IdentifyTypeEntity;
import com.lanshan.base.commonservice.identify.entity.StaffEntity;
import com.lanshan.base.commonservice.identify.entity.StaffIdentifyRelation;
import com.lanshan.base.commonservice.identify.mapper.IdentifyOperateLogMapper;
import com.lanshan.base.commonservice.identify.mapper.IdentifyTypeMapper;
import com.lanshan.base.commonservice.identify.mapper.StaffIdentifyRelationMapper;
import com.lanshan.base.commonservice.identify.mapper.StaffMapper;
import com.lanshan.base.commonservice.identify.service.StaffService;
import com.lanshan.base.commonservice.identify.uitls.IdCardValidator;
import com.lanshan.base.starter.rabbitmq.service.EnhancedMqMessageSender;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lanshan.base.starter.db.IdGenerator.uuid;

@Service("staffService")
public class StaffServiceImpl extends ServiceImpl<StaffMapper, StaffEntity> implements StaffService {

    @Resource
    private StaffMapper staffMapper;
    @Resource
    private StaffIdentifyRelationMapper staffIdentifyRelationMapper;
    @Resource
    private IdentifyTypeMapper identifyTypeMapper;
    @Resource
    private EnhancedMqMessageSender enhancedMqMessageSender;
    @Resource
    private IdentifyOperateLogMapper identifyOperateLogMapper;
    @Value("${message.queueexchange}")
    private String queueexchange;

    @Override
    public Map<String, Object> importUserData(MultipartFile file) throws Exception {
        Map<String, Object> result = new HashMap<>();
        List<StaffIdentifyRelation> srList = new ArrayList<>();
        List<StaffImportDto> errorList = new ArrayList<>();
        try (InputStream is = file.getInputStream()) {
            // 判断Excel版本
            Workbook workbook = WorkbookFactory.create(is);
            Sheet sheet = workbook.getSheetAt(0);

            // 获取标题行
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new Exception("Excel文件格式不正确，缺少标题行");
            }
            if (sheet.getLastRowNum() >= 1000) {
                throw new Exception("最多导入1000行");
            }

            // 读取数据行
            int n = 0;
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                StringBuffer sb = new StringBuffer("");
                Row row = sheet.getRow(i);
                if (row == null) continue;
                n++;
                StaffImportDto dto = parseToDto(row);
                if (StringUtils.isEmpty(dto.getStaffName())) {
//                    dto.setErrorMsg("姓名不能为空");
                    sb.append("姓名不能为空");
//                    errorList.add(dto);
//                    continue;
                }
                if (StringUtils.isEmpty(dto.getSex())) {
//                    dto.setErrorMsg("性别不能为空");
                    sb.append("性别不能为空");
//                    errorList.add(dto);
//                    continue;
                }
                if (!dto.getSex().equals("男") && !dto.getSex().equals("女")) {
//                    dto.setErrorMsg("性别不合法");
                    sb.append("性别不合法");
//                    errorList.add(dto);
//                    continue;
                }
                //1 身份证、2 护照、3 港澳居民身份证、4港澳来往内地通行证,、5台湾居住的有效身份证明、6台湾居民来往大陆通行
                //证、7台湾居民居住证、8暂住证。
                if (StringUtils.isEmpty(dto.getIdCardType())) {
//                    dto.setErrorMsg("证件类型不能为空");
//                    errorList.add(dto);
//                    continue;
                    sb.append("证件类型不能为空");
                }
                if (!dto.getIdCardType().equals("身份证") && !dto.getIdCardType().equals("护照") && !dto.getIdCardType().equals("港澳居民身份证")
                        && !dto.getIdCardType().equals("港澳来往内地通行证") && !dto.getIdCardType().equals("台湾居住的有效身份证明") && !dto.getIdCardType().equals("台湾居民来往大陆通行证")
                        && !dto.getIdCardType().equals("台湾居民居住证") && !dto.getIdCardType().equals("暂住证")
                ) {
//                    dto.setErrorMsg("证件类型不合法");
//                    errorList.add(dto);
//                    continue;
                    sb.append("证件类型不合法");
                }
                if (StringUtils.isEmpty(dto.getIdCard())) {
//                    dto.setErrorMsg("证件号不能为空");
//                    errorList.add(dto);
//                    continue;
                    sb.append("证件号不能为空");
                }
                if("身份证".equals(dto.getIdCardType()) && !IdCardValidator.isValidIdCard(dto.getIdCard())){
//                    dto.setErrorMsg("身份证号不合法");
//                    errorList.add(dto);
//                    continue;
                    sb.append("身份证号不合法");
                }
                if(!checkID(dto.getIdCardType(),dto.getIdCard())){
//                    dto.setErrorMsg("身份证("+dto.getIdCard()+")记录已存在，新增失败");
//                    errorList.add(dto);
//                    continue;
                    sb.append("身份证("+dto.getIdCard()+")记录已存在，新增失败");
                }
                if (StringUtils.isEmpty(dto.getPhone())) {
//                    dto.setErrorMsg("手机号不能为空");
//                    errorList.add(dto);
//                    continue;
                    sb.append("手机号不能为空");
                }
                if(!isValidPhoneNumber(dto.getPhone())){
//                    dto.setErrorMsg("手机号不合法");
//                    errorList.add(dto);
//                    continue;
                    sb.append("手机号不合法");
                }
                if (StringUtils.isEmpty(dto.getFClassIdentify())) {
//                    dto.setErrorMsg("一级身份不能为空");
//                    errorList.add(dto);
//                    continue;
                    sb.append("一级身份不能为空");
                }
                if (StringUtils.isEmpty(dto.getSClassIdentify())) {
//                    dto.setErrorMsg("二级身份不能为空");
//                    errorList.add(dto);
//                    continue;
                    sb.append("二级身份不能为空");
                }
                if (StringUtils.isEmpty(dto.getEndTime())) {
//                    dto.setErrorMsg("到期时间不能为空");
//                    errorList.add(dto);
//                    continue;
                    sb.append("到期时间不能为空");
                }
                IdentifyTypeEntity identifyTypeEntity = identifyTypeMapper.selectIdentify(dto.getFClassIdentify(), dto.getSClassIdentify());
                if (identifyTypeEntity == null || identifyTypeEntity.getStatus() == 0) {
//                    dto.setErrorMsg("身份类型错误或者身份类型不可用");
//                    errorList.add(dto);
//                    continue;
                    sb.append("身份类型错误或者身份类型不可用");
                }
                String staffNoType = "";
                String reNum = "";
                if (StringUtils.isNotEmpty(dto.getStuNo())) {
                    staffNoType = "学号";
                    reNum = dto.getStuNo();
                }
                if (StringUtils.isNotEmpty(dto.getGNo())) {
                    staffNoType = "工号";
                    reNum = dto.getGNo();
                }
                if (StringUtils.isNotEmpty(dto.getYktNo())) {
                    staffNoType = "一卡通号";
                    reNum = dto.getYktNo();
                }
                if(StringUtils.isEmpty(staffNoType) || StringUtils.isEmpty(reNum)){
//                    dto.setErrorMsg("学号，工号，一卡通号不能为空");
//                    errorList.add(dto);
//                    continue;
                    sb.append("学号，工号，一卡通号不能为空");
                }
                if(StringUtils.isNotEmpty(sb.toString())){
                    errorList.add(dto);
                    dto.setErrorMsg(sb.toString());
                    continue;
                }
                String staffId = "LS" + UuidUtils.generateUuid();
                staffMapper.insertOne(new StaffEntity(staffId, dto.getStaffName(), dto.getSex(), dto.getIdCardType(), dto.getIdCard(), dto.getPhone()));
                StaffIdentifyRelation sr = new StaffIdentifyRelation(staffId, identifyTypeEntity.getId(), dto.getFClassIdentify(), dto.getSClassIdentify(), staffNoType, reNum, dto.getFClassDept(), dto.getSClassDept(), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dto.getEndTime()), "管理员", 1);
                staffIdentifyRelationMapper.insertByMe(sr);
                srList.add(sr);
            }
            if(CollUtil.isNotEmpty(srList)){
//                staffIdentifyRelationMapper.insertBatch(srList);
                enhancedMqMessageSender.sendToDirectExchange(
                        MqConstant.IDENTIFY_EXCHANGE,
                        MqConstant.IDENTIFY_ROUTER_KEY,
                        srList
                );
            }
            result.put("errorList", errorList);
            result.put("errCount", errorList.size());
            result.put("successCount", n - errorList.size());
        } catch (Exception e) {
            log.error("导入Excel文件失败", e);
            throw e;
        }
        return result;
    }

    @Override
    @Transactional
    public void add(AddStaffDto dto) {
        IdentifyTypeEntity identifyTypeEntity = identifyTypeMapper.selectIdentify(dto.getFClassIdentify(), dto.getSClassIdentify());
        if (identifyTypeEntity == null || identifyTypeEntity.getStatus() == 0) {
            throw new ServiceException("身份类型错误或者身份类型不可用", 400);
        }
        if(!checkID(dto.getIdCardType(),dto.getIdCard())){
            throw new ServiceException("身份证("+dto.getIdCard()+")记录已存在，新增失败", 400);
        }
        if(!isValidPhoneNumber(dto.getPhone())){
            throw new ServiceException("手机号不合法", 400);
        }
        if("身份证".equals(dto.getIdCardType()) && !IdCardValidator.isValidIdCard(dto.getIdCard())){
            throw new ServiceException("身份证不合法", 400);
        }
        if(!isValidPhoneNumber(dto.getPhone())){
            throw new ServiceException("手机号不合法", 400);
        }
        try {
            String staffId = "LS" + uuid();
            staffMapper.insertOne(new StaffEntity(staffId, dto.getStaffName(), dto.getSex(), dto.getIdCardType(), dto.getIdCard(), dto.getPhone()));
            StaffIdentifyRelation staffIdentifyRelation = new StaffIdentifyRelation(staffId, identifyTypeEntity.getId(), dto.getFClassIdentify(), dto.getSClassIdentify(), dto.getStaffNoType(), dto.getStaffNo(), dto.getFClassDept(), dto.getSClassDept(), new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dto.getEndTime()), dto.getSource(), 1);
            staffIdentifyRelationMapper.insertByMe(staffIdentifyRelation);
            IdentifyOperateLogEntity identifyOperateLog = new IdentifyOperateLogEntity(SecurityContextHolder.getUserName() == null ? "" : SecurityContextHolder.getUserName(), "管理员", "新增人员(含身份)", dto.getStaffName() + ",证件号" + dto.getIdCard(), "新增" + dto.getSClassIdentify(), 1, "-");
            identifyOperateLogMapper.insertByMe(identifyOperateLog);
            List<StaffIdentifyRelation> mqList = new ArrayList<>();
            mqList.add(staffIdentifyRelation);
            enhancedMqMessageSender.sendToDirectExchange(
                    MqConstant.IDENTIFY_EXCHANGE,
                    MqConstant.IDENTIFY_ROUTER_KEY,
                    mqList
            );

        } catch (ParseException pe) {
            IdentifyOperateLogEntity identifyOperateLog = new IdentifyOperateLogEntity(SecurityContextHolder.getUserName(), "管理员", "新增人员(含身份)", dto.getStaffName() + ",证件号" + dto.getIdCard(), "新增" + dto.getSClassIdentify(), 1, "新增身份日期转化失败");
            identifyOperateLogMapper.insertByMe(identifyOperateLog);
            throw new ServiceException("新增身份日期转化失败", 400);
        } catch (Exception e) {
            IdentifyOperateLogEntity identifyOperateLog = new IdentifyOperateLogEntity(SecurityContextHolder.getUserName(), "管理员", "新增人员(含身份)", dto.getStaffName() + ",证件号" + dto.getIdCard(), "新增" + dto.getSClassIdentify(), 1, "服务器内部错误");
            identifyOperateLogMapper.insertByMe(identifyOperateLog);
            throw e;
        }

    }
    /**
     * 校验手机号是否合法
     * @param phoneNumber 手机号码
     * @return true-合法 false-不合法
     */
    public static boolean isValidPhoneNumber(String phoneNumber) {
        if (StringUtils.isEmpty(phoneNumber)) {
            return false;
        }
        // 匹配以1开头，第二位为3-9，后面跟9位数字
        String regex = "^1[3-9]\\d{9}$";
        return phoneNumber.matches(regex);
    }
    private boolean checkID(String idCardType, String idCard) {
     return   staffMapper.selectByIdCard(idCardType,idCard) == null?true:false;
    }


    private StaffImportDto parseToDto(Row row) {
        StaffImportDto dto = new StaffImportDto();
        dto.setStaffName(row.getCell(0) == null ? null : row.getCell(0).getStringCellValue());
        dto.setSex(row.getCell(1) == null ? null : row.getCell(1).getStringCellValue());
        dto.setIdCardType(row.getCell(2) == null ? null : row.getCell(2).getStringCellValue());
        dto.setIdCard(row.getCell(3) == null ? null : row.getCell(3).getStringCellValue());
        dto.setPhone(row.getCell(4) == null ? null : row.getCell(4).getStringCellValue());
        dto.setFClassIdentify(row.getCell(5) == null ? null : row.getCell(5).getStringCellValue());
        dto.setSClassIdentify(row.getCell(6) == null ? null : row.getCell(6).getStringCellValue());
        dto.setStuNo(row.getCell(7) == null ? null : row.getCell(7).getStringCellValue());
        dto.setGNo(row.getCell(8) == null ? null : row.getCell(8).getStringCellValue());
        dto.setYktNo(row.getCell(9) == null ? null : row.getCell(9).getStringCellValue());
        dto.setFClassDept(row.getCell(10) == null ? null : row.getCell(10).getStringCellValue());
        dto.setSClassDept(row.getCell(11) == null ? null : row.getCell(11).getStringCellValue());
        dto.setEndTime(row.getCell(12) == null ? null : row.getCell(12).getStringCellValue());
        return dto;
    }
}


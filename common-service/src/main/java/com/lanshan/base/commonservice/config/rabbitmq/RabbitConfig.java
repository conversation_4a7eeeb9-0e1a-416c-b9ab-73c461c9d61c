package com.lanshan.base.commonservice.config.rabbitmq;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;


@Configuration
public class RabbitConfig {
    @Value("${message.queuename}")
    private String queuename;
    @Value("${message.queueexchange}")
    private String queueexchange;


    @Bean
    public Queue identifyQueue() {
        return new Queue(queuename, true);
    }

    @Bean
    public DirectExchange identifyExchange() {
        return new DirectExchange(queueexchange);
    }

    @Bean
    public Binding identifyBinding(Queue identifyQueue, DirectExchange identifyExchange) {
        return BindingBuilder.bind(identifyQueue)
                .to(identifyExchange)
                .with(MqConstant.IDENTIFY_ROUTER_KEY);
    }

    // 第二个绑定（使用正确的Exchange引用）
    @Bean
    public Binding identifyImportBinding(Queue identifyQueue, DirectExchange identifyExchange) {
        return BindingBuilder.bind(identifyQueue)
                .to(identifyExchange)
                .with(MqConstant.IDENTIFY_CHANGE_ROUTER_KEY);
    }

//    @Bean
//    public RabbitTemplate newRabbitTemplate(ConnectionFactory connectionFactory) {
//        RabbitTemplate template1 = new RabbitTemplate(connectionFactory);
//        template1.setMessageConverter(jsonMessageConverter());
//        return template1;
//    }

    @Bean
    public Jackson2JsonMessageConverter jsonMessageConverter() {
        return new Jackson2JsonMessageConverter();
    }

//    @Bean
//    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
//            ConnectionFactory connectionFactory) {
//        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
//        factory.setConnectionFactory(connectionFactory);
//        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL); // 手动ACK
//        return factory;
//    }

//    @Bean
//    public Queue identifyQueue() {
//        Map<String, Object> args = new HashMap<>();
//        args.put("x-dead-letter-exchange", "dlx.exchange");
//        args.put("x-dead-letter-routing-key", "dlx.routing.key");
//        return new Queue(MqConstant.IDENTIFY_QUEUE, true, false, false, args);
//    }
}
package com.lanshan.base.commonservice.task;

import cn.hutool.core.collection.CollUtil;
import com.lanshan.base.commonservice.config.rabbitmq.MqConstant;
import com.lanshan.base.commonservice.identify.entity.*;
import com.lanshan.base.commonservice.identify.mapper.*;
import com.lanshan.base.starter.rabbitmq.service.EnhancedMqMessageSender;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @program: capability-platform-base
 * @Description: 身份任务处理类
 * @createTime: 2025-04-28 10:12
 */
@Slf4j
@Component
public class IdentifyTask {

    @Resource
    StaffIdentifyRelationMapper staffIdentifyRelationMapper;

    @Resource
    IdentifyChangeRuleMapper identifyChangeRuleMapper;

    @Resource
    IdentifyTypeMapper identifyTypeMapper;

    @Resource
    IdentifyRemoveQueueMapper identifyRemoveQueueMapper;

    @Resource
    IdentifyStatusMapper identifyStatusMapper;

    @Resource
    private EnhancedMqMessageSender enhancedMqMessageSender;

    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private StaffMapper staffMapper;

    /**
     * 变更身份状态
     */
    @XxlJob("endIdentify")
    public void endIdentify() {
        try {
            List<StaffIdentifyRelation> needDealIdentifys = staffIdentifyRelationMapper.selectAllIdentifyAfterEndTime();
            if(CollUtil.isEmpty(needDealIdentifys)){
                return;
            }
            List<String> staffIds = needDealIdentifys.stream()
                    .map(StaffIdentifyRelation::getStaffId)
                    .collect(Collectors.toList());
            staffIdentifyRelationMapper.updateByIds(staffIds);
        }catch (Exception e){
            log.error("endIdentify - error ",e);
        }
    }

    /**
     * 变更身份状态
     */
    @XxlJob("identifyChange")
    public void identifyChange() {
        try {
            List<IdentifyChangeRuleEntity> rules = identifyChangeRuleMapper.selectByStatus(1);
           if(CollUtil.isEmpty(rules)){
               return;
           }
            for (IdentifyChangeRuleEntity rule:rules) {
                if(rule.getOperateStatus() == 1){
                    List<StaffIdentifyRelation> staffIdentifys =  staffIdentifyRelationMapper.getIdentifysByF(rule.getFromIdentifyType(),rule.getFromIdentifyStatus());
                    if(CollUtil.isEmpty(staffIdentifys)){
                        continue;
                    }
                    List<StaffIdentifyRelation> insertResult = new ArrayList<>();
                    for (StaffIdentifyRelation sr:staffIdentifys){
                        StaffIdentifyRelation relation = new StaffIdentifyRelation();
                        //必须要先创建好目的身份类型  todo
                        IdentifyTypeEntity identifyTypeEntity = identifyTypeMapper.selectIdentify(null,rule.getToIdentifyType());
                        if(identifyTypeEntity == null ){
                            log.info("当前身份类型不存在");
                            continue;
                        }
                        relation.setIdentifyId(identifyTypeEntity.getId());
                        relation.setStaffId(sr.getStaffId());
                        relation.setFClassIdentify(identifyTypeEntity.getFClassIdentify());
                        relation.setSClassIdentify(identifyTypeEntity.getSClassIdentify());
                        relation.setSClassDept(rule.getSDept());
                        relation.setFClassDept(rule.getFDept());
                        relation.setStatus(1);
                        if(rule.getFDept().equals("校友")){
                            relation.setStaffNoType(rule.getAccount());
                            StaffEntity staff = staffMapper.selectByStaffId(sr.getStaffId());
                            relation.setStaffNo(staff==null?"":staff.getIdCard());
                            relation.setIdentifyStatus("正常");
                        }

                        relation.setDefaultIdentify(0);
                        relation.setSource("管理员");
                        relation.setCreateTime(new Date());
                        relation.setLastUpdateTime(new Date());
                        StaffIdentifyRelation existIdentify =  staffIdentifyRelationMapper.selectIdentifyByStaffIdAndIdentifyId(sr.getStaffId(), identifyTypeEntity.getId());
                        if(existIdentify==null){
                            log.info("当前身份不存在，需要新增");
                            insertResult.add(relation);
                        }
                    }
                    if(CollUtil.isNotEmpty(insertResult)){
                        //批量插入身份
                        staffIdentifyRelationMapper.insertBatch(insertResult);
                    }
                }else if(rule.getOperateStatus() == 2){
                    List<StaffIdentifyRelation> staffIdentifys =  staffIdentifyRelationMapper.getIdentifysByF(rule.getFromIdentifyType(),rule.getFromIdentifyStatus());
                    if(CollUtil.isNotEmpty(staffIdentifys)){
                        List<String> staffIds = staffIdentifys.stream()
                                .map(StaffIdentifyRelation::getStaffId)
                                .distinct()  // 去重
                                .collect(Collectors.toList());
                        if(CollUtil.isNotEmpty(staffIds)){
                            //正常的数据如果有新生
                            List<StaffIdentifyRelation>  newStaffs =  staffIdentifyRelationMapper.getNewIdentify(staffIds);
                            if(CollUtil.isNotEmpty(newStaffs)){
                                List<String> newstaffIds = newStaffs.stream()
                                        .map(StaffIdentifyRelation::getStaffId)
                                        .distinct()  // 去重
                                        .collect(Collectors.toList());
                                List<Long> newIdentifyIds = newStaffs.stream()
                                        .map(StaffIdentifyRelation::getIdentifyId)
                                        .distinct()  // 去重
                                        .collect(Collectors.toList());
                                //todo 加上id
                                staffIdentifyRelationMapper.removeIdentify(newstaffIds,newIdentifyIds,rule.getToIdentifyType());
                            }

                        }
                    }
                }
            }
            log.info("identifyChange -------------------- success");
        }catch (Exception e){
            log.info("identifyChange -------------------- error");
            log.error("identifyChange - error: {}",e);
        }
    }

    @XxlJob("identifyQueue")
    public void identifyQueue() {
        try {
            //将需要处理的状态放入队列
           List<IdentifyRemoveQueueEntity> existQueues =  identifyRemoveQueueMapper.queryList();
           //终止状态数据
            List<IdentifyStatusEntity> identifyStatusList =  identifyStatusMapper.selectEndStatusList();
            if(CollUtil.isEmpty(identifyStatusList)){
                return;
            }
            List<String> statusNameList = identifyStatusList.stream()
                    .map(IdentifyStatusEntity::getStatusName)
                    .collect(Collectors.toList());
            List<StaffIdentifyRelation> relations =  staffIdentifyRelationMapper.selectByIdentifyByStatus(statusNameList);
            if(CollUtil.isEmpty(relations)){
                return;
            }
            Map<String,Boolean> result = null;
            if(CollUtil.isNotEmpty(existQueues)){
                result = dealQueueToMap(existQueues);
            }

           List<IdentifyRemoveQueueEntity> needDealList = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            // 增加 10 天
            calendar.add(Calendar.DATE, 30);
            // 获取处理后的日期
            Date futureDate = calendar.getTime();
           for (StaffIdentifyRelation sr : relations){
               if(CollUtil.isNotEmpty(existQueues) && result.get(sr.getStaffId()+sr.getIdentifyId())!=null  && result.get(sr.getStaffId()+sr.getIdentifyId())){
                       continue;
               }
               IdentifyRemoveQueueEntity queue = new IdentifyRemoveQueueEntity();
               queue.setIdentifyId(sr.getIdentifyId());
               queue.setStaffId(sr.getStaffId());
               queue.setEndTime(futureDate);
               queue.setOperateStatus(0);
               needDealList.add(queue);
           }
           if(CollUtil.isNotEmpty(needDealList)){
               identifyRemoveQueueMapper.insertBatch(needDealList);
           }
            log.info("identifyQueue -------------------- success");
        }catch (Exception e){
            log.info("identifyQueue -------------------- error");
            log.error("identifyQueue - error ",e);
        }
    }
    @Value("${message.queueexchange}")
    private String queueexchange;

    @XxlJob("putQueueToQW")
    public void putQueueToQW() {
        List<StaffIdentifyRelation> relations = staffIdentifyRelationMapper.selectByIdentifyStaus();
        if(CollUtil.isNotEmpty(relations)){
            // 使用新的增强消息发送服务
            enhancedMqMessageSender.sendToDirectExchange(
                    MqConstant.IDENTIFY_EXCHANGE,
                    MqConstant.IDENTIFY_CHANGE_ROUTER_KEY,
                    relations
            );
            log.info("putQueueToQW put -------------------- success");
        }
        log.info("putQueueToQW is null -------------------- is");
    }

    /**
     * 变更队列里的身份状态
     */
    @XxlJob("endQueueIdentify")
    public void endQueueIdentify() {
        try {
            List<IdentifyRemoveQueueEntity> queues = identifyRemoveQueueMapper.selectAllIdentifyAfterEndTimeAndStatus();
            if(CollUtil.isEmpty(queues)){
                return;
            }
            for (IdentifyRemoveQueueEntity entity : queues){
                staffIdentifyRelationMapper.updateByStaffAndIdentify(entity.getStaffId(),entity.getIdentifyId());
            }
            identifyRemoveQueueMapper.updateStatus();
            log.info("endQueueIdentify put -------------------- success");
        }catch (Exception e){
            log.error("endQueueIdentify - error ",e);
        }
    }
    private Map<String, Boolean> dealQueueToMap(List<IdentifyRemoveQueueEntity> existQueues) {
        Map<String, Boolean> resultMap = new HashMap<>();
        for (IdentifyRemoveQueueEntity user : existQueues) {
            resultMap.put(user.getStaffId()+user.getIdentifyId(),true);
        }
        return resultMap;
    }
}

package com.lanshan.base.commonservice.access.openapi;

import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.access.service.OpenApiService;
import com.lanshan.base.starter.wxcpsdk.WxCpServiceFactory;
import me.chanjar.weixin.cp.api.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class OpenApi {

    @Resource
    protected OpenApiService openApiService;

    @Resource
    protected WxCpServiceFactory wxCpServiceFactory;

    protected WxCpService getWxCpService() {
        return wxCpServiceFactory.get(getCorpId(), getAgentId());
    }

    protected WxCpOaService getWxCpOaService() {
        WxCpService wxCpService = wxCpServiceFactory.get(getCorpId(), getAgentId());
        return wxCpService.getOaService();
    }

    protected WxCpAgentWorkBenchService getWorkbenchService() {
        WxCpService wxCpService = wxCpServiceFactory.get(getCorpId(), getAgentId());
        return wxCpService.getWorkBenchService();
    }

    protected WxCpAgentService getAgentService() {
        WxCpService wxCpService = wxCpServiceFactory.get(getCorpId(), getAgentId());
        return wxCpService.getAgentService();
    }

    protected WxCpMessageService getMessageService() {
        WxCpService wxCpService = wxCpServiceFactory.get(getCorpId(), getAgentId());
        return wxCpService.getMessageService();
    }

    protected String getAgentId() {
        return openApiService.getAgentId(SecurityContextHolder.getUserId());
    }

    protected String getCorpId() {
        return openApiService.getCorpId();
    }
}

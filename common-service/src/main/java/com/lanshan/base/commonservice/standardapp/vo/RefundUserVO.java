package com.lanshan.base.commonservice.standardapp.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ExcelIgnoreUnannotated
@HeadFontStyle(fontHeightInPoints = 12)
@ApiModel(value = "RefundUserVO", description = "退费用户视图对象")
public class RefundUserVO implements Serializable {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ExcelProperty(value = "所属年份", order = 12)
    @ApiModelProperty(value = "所属年份")
    private Integer year;

    @ColumnWidth(14)
    @ExcelProperty(value = "学号", order = 0)
    @ApiModelProperty(value = "学号")
    private String userId;

    @ColumnWidth(14)
    @ExcelProperty(value = "姓名", order = 1)
    @ApiModelProperty(value = "姓名")
    private String userName;

    @ColumnWidth(25)
    @ExcelProperty(value = "开户银行", order = 6)
    @ApiModelProperty(value = "开户银行")
    private String bankName;

    @ColumnWidth(25)
    @ExcelProperty(value = "银行卡号", order = 7)
    @ApiModelProperty(value = "银行卡号")
    private String bankNumber;

    @ColumnWidth(20)
    @ExcelProperty(value = "持卡人姓名", order = 8)
    @ApiModelProperty(value = "持卡人姓名")
    private String bankUserName;

    @ColumnWidth(20)
    @ExcelProperty(value = "是否捐赠", order = 9)
    @ApiModelProperty(value = "是否捐赠")
    private Boolean donate;

    @ColumnWidth(20)
    @ExcelProperty(value = "校园网余额", order = 10)
    @ApiModelProperty(value = "校园网余额")
    private String balance;

    @ColumnWidth(20)
    @ExcelProperty(value = "一卡通余额", order = 11)
    @ApiModelProperty(value = "一卡通余额")
    private String balanceCard;

    @ColumnWidth(25)
    @ExcelProperty(value = "学院", order = 2)
    @ApiModelProperty(value = "学院")
    private String college;

    @ColumnWidth(20)
    @ExcelProperty(value = "班级", order = 4)
    @ApiModelProperty(value = "班级")
    private String className;

    @ColumnWidth(20)
    @ExcelProperty(value = "学历层次", order = 3)
    @ApiModelProperty(value = "学历层次")
    private String userType;

    @ColumnWidth(20)
    @ExcelProperty(value = "年级", order = 5)
    @ApiModelProperty(value = "年级")
    private String grade;

    @ApiModelProperty(value = "头像")
    private String avatar;
}
package com.lanshan.base.commonservice.schooldata.whut.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.commonservice.schooldata.whut.properties.DrpConstant;
import com.lanshan.base.commonservice.schooldata.whut.qo.*;
import com.lanshan.base.commonservice.schooldata.whut.service.DrpService;
import com.lanshan.base.commonservice.schooldata.whut.util.HttpRequestUtil;
import com.lanshan.base.commonservice.schooldata.whut.vo.*;
import org.springframework.stereotype.Service;

/**
 * @Description:
 * @ProjectName: base-work-weixin-platform
 * @Package: com.lanshan.service.rpc.impl
 * @ClassName: DrpServiceImpl
 * @Author: zhaoyong
 * @Email: <EMAIL>
 * @Date: 2023/6/29 14:14
 * @Version: 1.0
 */
@Service
public class DrpServiceImpl implements DrpService {


    private String getUrl(String host, String url) {
        if (!DrpConstant.excludeUrl.contains(url)) {
            refreshAccessToken();
            url = url + "?access_token=" + DrpConstant.access_token;
        }
        return host + url;
    }

    private void refreshAccessToken() {
        ResponseSet<GetAccessTokenResponseSet> getAccessTokenResponseSetResponseSet =
                get_access_token(new GetAccessTokenQo(DrpConstant.KEY, DrpConstant.SECRET));
        if (ObjectUtil.isNotNull(getAccessTokenResponseSetResponseSet)) {
            DrpConstant.access_token = getAccessTokenResponseSetResponseSet.getResult().getAccess_token();
        }
    }

    private void checkCode(ResponseSet responseSet) {
        if (ObjectUtil.isNull(responseSet) || !ObjectUtil.equal(responseSet.getCode(), DrpConstant.SUCCESS_CODE)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("请求理工数据平台失败");
        }
    }

    @Override
    public ResponseSet<GetAccessTokenResponseSet> get_access_token(GetAccessTokenQo getAccessTokenQo) {
        try {
            ResponseSet<GetAccessTokenResponseSet> getAccessTokenResponseSetResponseSet = HttpRequestUtil.get(
                    getUrl(DrpConstant.HOST, "/open_api/authentication/get_access_token"),
                    null,
                    HttpRequestUtil.convertJavaObjectToMap(getAccessTokenQo),
                    new TypeReference<ResponseSet<GetAccessTokenResponseSet>>() {
                    }
            );
            checkCode(getAccessTokenResponseSetResponseSet);

            return getAccessTokenResponseSetResponseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public ResponseSet<ViewEchoCharlieResponseSet> viewEchoCharlie(ViewEchoCharlieQo viewEchoCharlieQo) {
        try {
            ResponseSet<ViewEchoCharlieResponseSet> viewEchoCharlieResponseSetResponseSet = HttpRequestUtil.post(
                    getUrl(DrpConstant.HOST, "/open_api/customization/view_echo_charlie/full"),
                    null,
                    JSONObject.toJSONString(viewEchoCharlieQo),
                    new TypeReference<ResponseSet<ViewEchoCharlieResponseSet>>() {
                    }
            );
            checkCode(viewEchoCharlieResponseSetResponseSet);

            return viewEchoCharlieResponseSetResponseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    @Override
    public ResponseSet<ViewEchoLimaResponseSet> viewEchoLima(ViewEchoLimaQo viewEchoLimaQo) {
        try {
            ResponseSet<ViewEchoLimaResponseSet> viewEchoLimaResponseSetResponseSet = HttpRequestUtil.post(
                    getUrl(DrpConstant.HOST, "/open_api/customization/view_echo_lima/full"),
                    null,
                    JSONObject.toJSONString(viewEchoLimaQo),
                    new TypeReference<ResponseSet<ViewEchoLimaResponseSet>>() {
                    }
            );
            checkCode(viewEchoLimaResponseSetResponseSet);

            return viewEchoLimaResponseSetResponseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public ResponseSet<ViewEchoMikeResponseSet> viewEchoMike(ViewEchoMikeQo viewEchoMikeQo) {
        try {
            ResponseSet<ViewEchoMikeResponseSet> viewEchoMikeResponseSetResponseSet = HttpRequestUtil.post(
                    getUrl(DrpConstant.HOST, "/open_api/customization/view_echo_mike/full"),
                    null,
                    JSONObject.toJSONString(viewEchoMikeQo),
                    new TypeReference<ResponseSet<ViewEchoMikeResponseSet>>() {
                    }
            );
            checkCode(viewEchoMikeResponseSetResponseSet);

            return viewEchoMikeResponseSetResponseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public ResponseSet<ViewEchoNovemberResponseSet> viewEchoNovember(ViewEchoNovemberQo viewEchoNovemberQo) {
        try {
            ResponseSet<ViewEchoNovemberResponseSet> responseSet = HttpRequestUtil.post(
                    getUrl(DrpConstant.HOST, "/open_api/customization/view_echo_november/full"),
                    null,
                    JSONObject.toJSONString(viewEchoNovemberQo),
                    new TypeReference<ResponseSet<ViewEchoNovemberResponseSet>>() {
                    }
            );
            checkCode(responseSet);

            return responseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public ResponseSet<ViewFoxtrotYankeeResponseSet> viewFoxtrotYankee(ViewFoxtrotYankeeQo viewFoxtrotYankeeQo) {
        try {
            ResponseSet<ViewFoxtrotYankeeResponseSet> responseSet = HttpRequestUtil.post(
                    getUrl(DrpConstant.HOST, "/open_api/customization/view_foxtrot_yankee/full"),
                    null,
                    JSONObject.toJSONString(viewFoxtrotYankeeQo),
                    new TypeReference<ResponseSet<ViewFoxtrotYankeeResponseSet>>() {
                    }
            );
            checkCode(responseSet);

            return responseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public ResponseSet<ViewEchoSierraResponseSet> viewEchoSierra(ViewEchoSierraQo viewEchoSierraQo) {
        try {
            ResponseSet<ViewEchoSierraResponseSet> viewEchoSierraResponseSetResponseSet = HttpRequestUtil.post(
                    getUrl(DrpConstant.HOST, "/open_api/customization/view_echo_sierra/full"),
                    null,
                    JSONObject.toJSONString(viewEchoSierraQo),
                    new TypeReference<ResponseSet<ViewEchoSierraResponseSet>>() {
                    }
            );
            checkCode(viewEchoSierraResponseSetResponseSet);
            return viewEchoSierraResponseSetResponseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public ResponseSet<ViewEchoQuebecResponseSet> viewEchoQuebec(ViewEchoQuebecQo viewEchoQuebecQo) {
        try {
            ResponseSet<ViewEchoQuebecResponseSet> viewEchoQuebecResponseSetResponseSet = HttpRequestUtil.post(
                    getUrl(DrpConstant.HOST, "/open_api/customization/view_echo_quebec/full"),
                    null,
                    JSONObject.toJSONString(viewEchoQuebecQo),
                    new TypeReference<ResponseSet<ViewEchoQuebecResponseSet>>() {
                    }
            );
            checkCode(viewEchoQuebecResponseSetResponseSet);

            return viewEchoQuebecResponseSetResponseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public ResponseSet<ViewEchoPapaResponseSet> viewEchoPapa(ViewEchoPapaQo viewEchoPapaQo) {
        try {
            ResponseSet<ViewEchoPapaResponseSet> viewEchoPapaResponseSetResponseSet = HttpRequestUtil.post(
                    getUrl(DrpConstant.HOST, "/open_api/customization/view_echo_papa/full"),
                    null,
                    JSONObject.toJSONString(viewEchoPapaQo),
                    new TypeReference<ResponseSet<ViewEchoPapaResponseSet>>() {
                    }
            );
            checkCode(viewEchoPapaResponseSetResponseSet);

            return viewEchoPapaResponseSetResponseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public ResponseSet<ViewEchoOscarResponseSet> viewEchoOscar(ViewEchoOscarQo viewEchoOscarQo) {
        try {
            ResponseSet<ViewEchoOscarResponseSet> viewEchoOscarResponseSetResponseSet = HttpRequestUtil.post(
                    getUrl(DrpConstant.HOST, "/open_api/customization/view_echo_oscar/full"),
                    null,
                    JSONObject.toJSONString(viewEchoOscarQo),
                    new TypeReference<ResponseSet<ViewEchoOscarResponseSet>>() {
                    }
            );
            checkCode(viewEchoOscarResponseSetResponseSet);

            return viewEchoOscarResponseSetResponseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public ResponseSet<TgxjxbkszyjbxxResponseSet> tgxjxbkszyjbxx(TgxjxbkszyjbxxQo tgxjxbkszyjbxxQo) {
        try {
            ResponseSet<TgxjxbkszyjbxxResponseSet> tgxjxbkszyjbxxResponseSetResponseSet = HttpRequestUtil.post(
                    getUrl(DrpConstant.HOST, "/open_api/customization/tgxjxbkszyjbxx/full"),
                    null,
                    JSONObject.toJSONString(tgxjxbkszyjbxxQo),
                    new TypeReference<ResponseSet<TgxjxbkszyjbxxResponseSet>>() {
                    }
            );
            checkCode(tgxjxbkszyjbxxResponseSetResponseSet);

            return tgxjxbkszyjbxxResponseSetResponseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public ResponseSet<ViewHotelResponseSet> viewHotel(ViewHotelQo viewHotelQo) {
        try {
            ResponseSet<ViewHotelResponseSet> viewHotelResponseSetResponseSet = HttpRequestUtil.post(
                    getUrl(DrpConstant.HOST, "/open_api/customization/view_hotel/full"),
                    null,
                    JSONObject.toJSONString(viewHotelQo),
                    new TypeReference<ResponseSet<ViewHotelResponseSet>>() {
                    }
            );
            checkCode(viewHotelResponseSetResponseSet);

            return viewHotelResponseSetResponseSet;
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }
}

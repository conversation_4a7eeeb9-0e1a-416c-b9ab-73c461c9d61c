package com.lanshan.base.commonservice.welcomenewstudent.excel.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.lanshan.base.api.enums.UserTypeEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 用户类型转换器
 */
public class UserTypeConverter implements Converter<String> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(ReadCellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return UserTypeEnum.getCodeStrByDesc(cellData.getStringValue());
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String codeStrByDesc = UserTypeEnum.getDescByCodeStr(value);
        return codeStrByDesc == null ? new WriteCellData<>("-") : new WriteCellData<>(codeStrByDesc);
    }
}
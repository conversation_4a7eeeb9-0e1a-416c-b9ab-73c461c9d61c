package com.lanshan.base.commonservice.standarddata.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lanshan.base.starter.mybatis.ListIntegerToListLongTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.List;

/**
 * 用户信息标准表
 * <AUTHOR> yang.
 * @since 2025-06-04
 */

@Data
@TableName(value = "std_user", schema = "standard_data",autoResultMap = true)
public class StdUser implements Serializable {

	private static final long serialVersionUID = 1L;

    //成员UserID
    @TableId
    private String userid;

    //成员名称
    private String name;

    //性别。0表示未定义，1表示男性，2表示女性
    private String gender;

    //年龄
    private String age;

    //职务信息
    private String position;

    //企微中成员所属部门id
    @TableField(typeHandler = ListIntegerToListLongTypeHandler.class)
    private List<Long> department;

    //企微中主部门
    private Long mainDepartment;

    //企微中成员所属部门的父部门id
    private String parentCode;

    //部门编码
    private String deptCode;

    //成员所属部门名称
    private String deptName;

    //手机号码
    private String mobile;

    //邮箱
    private String email;

    //院系所号
    private String yxsh;

    //院系名称
    private String yxmc;

    //所在年级
    private String sznj;

    //专业码
    private String zym;

    //专业名称
    private String zymc;

    //所在班号
    private String szbh;

    //班级名称
    private String bjmc;

    //人员类别码
    private String rylbm;

    //人员类别
    private String rylb;

    //家庭地址
    private String address;

    //籍贯
    private String nativePlace;

    //民族
    private String nation;

    //政治面貌
    private String politicalStatus;

    //身份证件号
    private String sfzjh;

    //身份证件类型
    private String sfzjhlx;

    //出生日期
    private String birthDate;

    //用户类型 教职工，本专科生，研究生
    private String userType;

    //用户当前状态。
    private String userStatus;

    //扩展属性
    private String extattr;

    //表示在所在的部门内是否为上级
    private String isLeaderInDept;

    //直属上级UserID
    private String directLeader;

    //数据来源
    private String sjly;

    //数据同步任务主键
    private String jobExecId;



}

package com.lanshan.base.commonservice.todo.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 添加用户待办事项节点信息对象
 */
@ApiModel(value = "添加用户待办事项节点信息对象")
@Data
public class AddUserTodoFlowNodeQO implements Serializable {

    private static final long serialVersionUID = -7049727468342807091L;

    @ApiModelProperty("节点id")
    @NotNull(message = "节点id不能为空")
    private Long nodeId;

    @ApiModelProperty("节点类型")
    @NotNull(message = "节点类型不能为空")
    private Integer nodeType;

    @ApiModelProperty("流程节点名称")
    private String nodeTypeDesc;

    @ApiModelProperty("节点用户列表")
    @NotEmpty(message = "节点用户列表不能为空")
    @Valid
    private List<AddUserTodoFlowNodeQO.NodeUserInfo> userInfoList;

    @Data
    public static class NodeUserInfo implements Serializable {

        private static final long serialVersionUID = 7723141612784660414L;

        @ApiModelProperty("用户id")
        @NotBlank(message = "用户id不能为空")
        private String userid;

        @ApiModelProperty("用户名称")
        @NotBlank(message = "用户名称不能为空")
        private String name;

        @ApiModelProperty("连接地址")
        private String linkUrl;

        @ApiModelProperty("连接地址-PC端")
        private String linkUrlPc;

    }
}

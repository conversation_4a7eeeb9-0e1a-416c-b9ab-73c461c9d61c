package com.lanshan.base.commonservice.todo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "根据流程信息查询代办节点用户dto")
public class TodoNodeUserExistsByFlowInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("流水号")
    private String serialNo;

    @ApiModelProperty("应用id")
    private Long appId;

    @ApiModelProperty("节点id")
    private Long nodeId;
}

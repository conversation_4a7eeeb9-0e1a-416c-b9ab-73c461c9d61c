package com.lanshan.base.commonservice.todo.handler.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.lanshan.base.api.enums.CompleteTypeEnum;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.enums.YnEnum;
import com.lanshan.base.api.utils.RedisService;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.entity.CpUser;
import com.lanshan.base.commonservice.addressbook.service.UserService;
import com.lanshan.base.commonservice.constant.CommonServiceRedisKeys;
import com.lanshan.base.commonservice.schooldata.hbou.constant.HbouOpenApiConstant;
import com.lanshan.base.commonservice.schooldata.hbou.constant.HbouRedisKeys;
import com.lanshan.base.commonservice.schooldata.hbou.dto.SearchTodoResultDTO;
import com.lanshan.base.commonservice.schooldata.hbou.properties.HbouOaProperties;
import com.lanshan.base.commonservice.todo.entity.TodoDef;
import com.lanshan.base.commonservice.todo.entity.TodoDetail;
import com.lanshan.base.commonservice.todo.entity.TodoFlowInfo;
import com.lanshan.base.commonservice.todo.entity.TodoUserRelation;
import com.lanshan.base.commonservice.todo.enums.TodoCompleteModeEnum;
import com.lanshan.base.commonservice.todo.enums.TodoCreatorTypeEnum;
import com.lanshan.base.commonservice.todo.handler.TodoHandler;
import com.lanshan.base.commonservice.todo.mapper.TodoDefMapper;
import com.lanshan.base.commonservice.todo.service.TodoDefService;
import com.lanshan.base.commonservice.todo.service.TodoDetailService;
import com.lanshan.base.commonservice.todo.service.TodoFlowInfoService;
import com.lanshan.base.commonservice.todo.service.TodoUserRelationService;
import com.lanshan.base.starter.db.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.util.set.Sets;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Component
public class HbouTodoHandler extends AbstractTodoHandler implements TodoHandler {


    @Resource
    private HbouOaProperties hbouOaProperties;

    @Resource
    private RedisService redisService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TodoDefMapper todoDefMapper;

    @Resource
    private TodoFlowInfoService todoFlowInfoService;

    @Resource
    private UserService userService;

    @Resource
    private TodoDefService todoDefService;

    @Resource
    private TodoDetailService todoDetailService;

    @Resource
    private TodoUserRelationService todoUserRelationService;

    @Resource
    private TaskExecutor todoApiExecutor;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public void syncTodoInfo() {
        // 判断是否开启同步
        Boolean oaTodoSyncEnabled = hbouOaProperties.getOaTodoSyncEnabled();
        if (Boolean.FALSE.equals(oaTodoSyncEnabled)) {
            return;
        }

        String userId = SecurityContextHolder.getUserId();

        if (StringUtils.isBlank(userId)) {
            userId = hbouOaProperties.getLoginName();
        }

        // 判断距离上次同步是否超过同步时间间隔（分钟），超过则进行同步
        Long lastTime = redisService.getCacheObject(HbouRedisKeys.HBOU_TODO_OA_SYNC_LAST_TIME + ":" + userId);
        long currentTime = System.currentTimeMillis();
        if (lastTime != null && (currentTime - lastTime) / 1000 / 60 < hbouOaProperties.getOaTodoSyncInterval()) {
            return;
        }

        // 获取开始时间和当前时间
        String beginTime;
        if (Objects.nonNull(lastTime) && lastTime > 0) {
            beginTime = DateUtil.formatDateTime(DateUtil.date(lastTime));
        } else {
            beginTime = hbouOaProperties.getSyncBeginTime();
        }
        String endTime = DateUtil.formatDateTime(new Date());

        // 获取分布式锁
        RLock lock = redissonClient.getLock(HbouRedisKeys.HBOU_TODO_OA_SYNC_LOCK + ":" + userId);
        try {
            boolean lockResult = lock.tryLock(500, 30000, TimeUnit.MILLISECONDS);
            if (!lockResult) {
                log.error("获取待办OA同步锁失败");
                return;
            }

            List<TodoFlowInfo> todoFlowInfoList = new ArrayList<>();
            List<TodoDef> todoDefList = new ArrayList<>();
            List<TodoDetail> todoDetailList = new ArrayList<>();
            List<TodoUserRelation> todoUserRelationList = new ArrayList<>();

            // 查询OA待办列表
            String finalUserId = userId;
            CompletableFuture<List<SearchTodoResultDTO.TodoInfo>> createTodoFuture = CompletableFuture.supplyAsync(() -> queryOATodo(finalUserId, beginTime, endTime), todoApiExecutor);

            CompletableFuture<List<SearchTodoResultDTO.TodoInfo>> completeFuture = CompletableFuture.supplyAsync(() -> {
                if (Objects.nonNull(lastTime) && lastTime > 0) {
                    return queryOACompletedTodo(finalUserId, beginTime, endTime);
                }
                return new ArrayList<>();
            }, todoApiExecutor);

            // 等待两个查询都完成
            CompletableFuture.allOf(createTodoFuture, completeFuture).join();
            List<SearchTodoResultDTO.TodoInfo> createTodoList = createTodoFuture.get();
            List<SearchTodoResultDTO.TodoInfo> completeList = completeFuture.get();

            if (CollUtil.isEmpty(createTodoList) && CollUtil.isEmpty(completeList)) {
                return;
            }

            createTodoList = getRealCreateTodoList(createTodoList, completeList);

            // 处理待办数据
            processTodo(createTodoList, todoFlowInfoList, todoDefList, todoDetailList, userId, todoUserRelationList);

            saveTodoInBatches(todoFlowInfoList, todoDefList, todoDetailList, todoUserRelationList);

            completeTodoInBatches(completeList);

            Set<String> needCreateUserid = createTodoList.stream().map(SearchTodoResultDTO.TodoInfo::getWorkNumber).collect(Collectors.toSet());
            // 缓存需要变更的人员id
            Set<String> needCompleteUserid = completeList.stream().map(SearchTodoResultDTO.TodoInfo::getWorkNumber).collect(Collectors.toSet());
            cacheChangeUserIds(Sets.union(needCreateUserid, needCompleteUserid));

            // 更新同步时间
            redisService.setCacheObject(HbouRedisKeys.HBOU_TODO_OA_SYNC_LAST_TIME + ":" + userId, currentTime);
        } catch (Throwable e) {
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            log.error("【待办OA同步信息】同步失败：{}", e.getMessage(), e);
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 获取真实需要创建的待办
     *
     * @param createTodoList 新建的待办
     * @param completeList   已完成的待办
     * @return
     */
    @NotNull
    private List<SearchTodoResultDTO.TodoInfo> getRealCreateTodoList(List<SearchTodoResultDTO.TodoInfo> createTodoList, List<SearchTodoResultDTO.TodoInfo> completeList) {
        //需要新建的待办所有的流水号
        Set<String> oaTodoSerialNoSet = Optional.of(createTodoList).orElseGet(ArrayList::new).stream()
                .filter(todoInfo -> {
                    return StringUtils.isNotBlank(todoInfo.getSenderNumber()) && StringUtils.isNotBlank(todoInfo.getWorkNumber());
                }).map(todoInfo -> String.valueOf(todoInfo.getId()))
                .collect(Collectors.toSet());

        //完成的待办的所有流水号
        Set<String> oaCompleteSerialNoSet = Optional.of(completeList).orElseGet(ArrayList::new).stream()
                .map(item -> String.valueOf(item.getId()))
                .collect(Collectors.toSet());


        //查询数据库中是否有相关流水号的数据
        Set<String> dbSerialNoSet = todoDefMapper.listTodoBySerialNo(hbouOaProperties.getAppId(), oaTodoSerialNoSet);
        Set<String> dbCompleteSerialNoSet = todoDefMapper.listTodoBySerialNo(hbouOaProperties.getAppId(), oaCompleteSerialNoSet);
        //需要新建的待办
        Set<String> needCreateSet = Sets.difference(oaTodoSerialNoSet, dbSerialNoSet);
        Set<String> needCreateComplSet = Sets.difference(oaCompleteSerialNoSet, dbCompleteSerialNoSet);
        Set<String> realNendCreateSet = Sets.union(needCreateSet, needCreateComplSet);

        createTodoList.addAll(completeList);
        return createTodoList.stream().filter(todoInfo -> realNendCreateSet.contains(String.valueOf(todoInfo.getId()))).collect(Collectors.toList());
    }

    /**
     * 缓存待办变更用户id
     *
     * @param userIds 用户变更集合
     */
    private void cacheChangeUserIds(Set<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return;
        }

        RLock lock = redissonClient.getLock(CommonServiceRedisKeys.TODO_SHOW_CHANGE_USER_ID_SET_LOCK);
        boolean locked = false;
        try {
            // 增加锁的等待时间和持有时间
            locked = lock.tryLock(500, 2000, TimeUnit.MILLISECONDS);
            if (locked) {
                redissonClient.getSet(CommonServiceRedisKeys.TODO_SHOW_CHANGE_USER_ID_SET).addAll(userIds);
            } else {
                log.warn("获取缓存待办变更用户id锁失败，用户数量: {}", userIds.size());
            }
        } catch (InterruptedException e) {
            log.error("缓存待办变更用户id失败,失败人员: {}", userIds, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("缓存待办变更用户id失败,失败人员: {}", userIds, e);
        } finally {
            if (locked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 完成待办
     */
    private void completeTodo(Collection<SearchTodoResultDTO.TodoInfo> needCompleteList) {
        for (SearchTodoResultDTO.TodoInfo todoInfo : needCompleteList) {
            //查询待办流程
            LambdaQueryWrapper<TodoFlowInfo> todoFlowInfoQw = Wrappers.lambdaQuery(TodoFlowInfo.class);
            todoFlowInfoQw.eq(TodoFlowInfo::getSerialNo, String.valueOf(todoInfo.getId()));
            List<TodoFlowInfo> todoFlowInfoCompleteList = todoFlowInfoService.list(todoFlowInfoQw);
            if (CollUtil.isEmpty(todoFlowInfoCompleteList)) {
                return;
            }

            //获取id列表
            List<Long> idList = todoFlowInfoCompleteList.stream().map(TodoFlowInfo::getId).collect(Collectors.toList());

            //更新待办用户关联（OA待办的相关表的id都一致)
            LambdaUpdateWrapper<TodoUserRelation> todoUserRelationUw = Wrappers.lambdaUpdate(TodoUserRelation.class);
            todoUserRelationUw.set(TodoUserRelation::getStatus, CompleteTypeEnum.COMPLETE.getCode());
            String completeTime = todoInfo.getCompleteTime();
            Date completeDateTime = new Date();
            if (StringUtils.isBlank(completeTime)) {
                completeDateTime = DateUtil.parse(completeTime);
            }
            todoUserRelationUw.set(TodoUserRelation::getCompleteTime, completeDateTime);
            todoUserRelationUw.eq(TodoUserRelation::getStatus, CompleteTypeEnum.INCOMPLETE.getCode());
            todoUserRelationUw.in(TodoUserRelation::getId, idList);
            transactionTemplate.execute(status -> {
                try {
                    //修改待办流程状态为已完成
                    todoFlowInfoService.update(Wrappers.lambdaUpdate(TodoFlowInfo.class).set(TodoFlowInfo::getFlowStatus, CompleteTypeEnum.COMPLETE.getCode())
                            .in(TodoFlowInfo::getId, idList));
                    todoUserRelationService.update(todoUserRelationUw);
                    return true;
                } catch (Exception e) {
                    status.setRollbackOnly();
                    log.error("修改待办流程状态为已完成失败", e);
                    return false;
                }
            });
        }

    }

    /**
     * 分批保存待办数据
     */
    private void saveTodoInBatches(List<TodoFlowInfo> todoFlowInfoList, List<TodoDef> todoDefList, List<TodoDetail> todoDetailList, List<TodoUserRelation> todoUserRelationList) {
        int batchSize = 100; // 每批次处理100条数据
        saveBatch(todoFlowInfoList, batchSize, todoFlowInfoService::saveBatch);
        saveBatch(todoDefList, batchSize, todoDefService::saveBatch);
        saveBatch(todoDetailList, batchSize, todoDetailService::saveBatch);
        saveBatch(todoUserRelationList, batchSize, todoUserRelationService::saveBatch);
    }

    private <T> void saveBatch(List<T> list, int batchSize, BatchSaver<T> batchSaver) {
        if (CollUtil.isNotEmpty(list)) {
            for (List<T> subList : Lists.partition(list, batchSize)) {
                transactionTemplate.execute(status -> {
                    try {
                        batchSaver.save(subList);
                        return true;
                    } catch (Exception e) {
                        status.setRollbackOnly();
                        log.error("保存待办发生错误:{}", e.getMessage(), e);
                        return false;
                    }
                });

            }
        }
    }

    @FunctionalInterface
    private interface BatchSaver<T> {
        void save(List<T> list);
    }

    /**
     * 分批完成待办
     */
    private void completeTodoInBatches(Collection<SearchTodoResultDTO.TodoInfo> needCompleteList) {
        int batchSize = 100; // 每批次处理100条数据
        List<SearchTodoResultDTO.TodoInfo> list = new ArrayList<>(needCompleteList);
        for (List<SearchTodoResultDTO.TodoInfo> todoInfos : Lists.partition(list, batchSize)) {
            completeTodo(todoInfos);
        }
    }

    /**
     * 处理待办
     */
    private void processTodo(List<SearchTodoResultDTO.TodoInfo> newList, List<TodoFlowInfo> todoFlowInfoList, List<TodoDef> todoDefList, List<TodoDetail> todoDetailList, String userId, List<TodoUserRelation> todoUserRelationList) {
        for (SearchTodoResultDTO.TodoInfo todoInfo : newList) {
            //发布时间
            Date pubDate = DateUtil.parse(todoInfo.getSendDate());
            //OA系统应用ID
            Long oaAppId = hbouOaProperties.getAppId();
            //OA系统应用名称
            String oaAppName = hbouOaProperties.getAppName();
            Date createDateTime = DateUtil.parse(todoInfo.getCreateTime());
            //生成id
            long id = IdGenerator.generateId();
            //结束时间默认设为2100-01-01
            Date endDate = DateUtil.parse("2100-01-01");

            //创建待办流程
            TodoFlowInfo todoFlowInfo = new TodoFlowInfo();
            todoFlowInfo.setId(id);
            todoFlowInfo.setAppId(oaAppId);
            todoFlowInfo.setAppName(oaAppName);
            todoFlowInfo.setSerialNo(String.valueOf(todoInfo.getId()));
            todoFlowInfo.setName(todoInfo.getTitle());
            todoFlowInfo.setDescription(todoInfo.getTitle());
            todoFlowInfo.setSubmitterId(todoInfo.getSenderNumber());
            todoFlowInfo.setSubmitterName(todoInfo.getSender());
            todoFlowInfo.setCreateDate(createDateTime);

            //设置待办类型 取出标题【】里面的内容作为类型
            if (StringUtils.isNotBlank(todoInfo.getTitle())) {
                String patternString = "【(.*?)】";
                Pattern pattern = Pattern.compile(patternString);
                Matcher matcher = pattern.matcher(todoInfo.getTitle());
                // 找到第一个匹配就停止
                if (matcher.find()) {
                    todoFlowInfo.setType(matcher.group(1));
                }
            }
            todoFlowInfoList.add(todoFlowInfo);

            //创建待办定义
            TodoDef todoDef = new TodoDef();
            todoDef.setId(id);
            todoDef.setName(todoInfo.getTitle());
            todoDef.setDescription(todoInfo.getTitle());
            todoDef.setCompleteMode(Collections.singletonList(TodoCompleteModeEnum.CLICK_LINK.getCode()));
            todoDef.setStartTime(pubDate);
            todoDef.setEndTime(endDate);
            todoDef.setLinkUrl(todoInfo.getH5Url());
            todoDef.setLinkUrlPc(todoInfo.getPcUrl());
            //设为OA系统应用
            todoDef.setCreator(String.valueOf(oaAppId));
            todoDef.setCreatorName(oaAppName);
            todoDef.setCreatorType(TodoCreatorTypeEnum.APP.getCode());
            todoDef.setCreateDate(createDateTime);
            todoDef.setUserCount(1);
            todoDef.setIsFlow(YnEnum.YES.getValue());
            todoDef.setFlowId(id);
            todoDef.setNodeId(1L);
            todoDefList.add(todoDef);

            //创建待办明细
            TodoDetail todoDetail = new TodoDetail();
            todoDetail.setId(id);
            todoDetail.setTodoDefId(id);
            todoDetail.setStartTime(pubDate);
            todoDetail.setEndTime(endDate);
            todoDetailList.add(todoDetail);

            //创建待办用户关联
            TodoUserRelation todoUserRelation = new TodoUserRelation();
            todoUserRelation.setId(id);
            todoUserRelation.setTodoDefId(id);
            todoUserRelation.setTodoDetailId(id);
            String completeTime = todoInfo.getCompleteTime();
            if (StringUtils.isNotBlank(completeTime)) {
                todoUserRelation.setStatus(CompleteTypeEnum.COMPLETE.getCode());
                todoUserRelation.setCompleteTime(DateUtil.parse(completeTime));
            }
            String realUserId = userId;
            if (realUserId.equalsIgnoreCase(hbouOaProperties.getLoginName())) {
                realUserId = todoInfo.getWorkNumber();
            }
            todoUserRelation.setUserid(realUserId);
            //查询名称
            CpUser user = userService.getById(realUserId);
            todoUserRelation.setName(Optional.ofNullable(user).map(CpUser::getName).orElse(null));
            todoUserRelationList.add(todoUserRelation);
        }
    }

    /**
     * 获取 token 值
     *
     * @param userId 用户 id
     * @return token 值
     */
    private String createToken(String userId) {
        if (StringUtils.isBlank(userId)) {
            userId = hbouOaProperties.getLoginName();
        }
        String redisKey = HbouRedisKeys.HBOU_OA_TOKEN + userId;
        if (redisService.hasKey(redisKey)) {
            return redisService.getCacheObject(redisKey);
        }

        String url = hbouOaProperties.getOaBaseUri() + hbouOaProperties.getOaTokenUri();
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("userName", hbouOaProperties.getUserName());
        bodyMap.put("password", hbouOaProperties.getPassword());
        bodyMap.put("loginName", userId);
        String body = HttpUtil.createPost(url).body(JSON.toJSONString(bodyMap)).execute().body();
        JSONObject jsonObject = JSON.parseObject(body);
        String token = jsonObject.getString("id");
        if (StringUtils.isBlank(token)) {
            log.error("请求 token 失败，请求人：{}，返回结果：{}", userId, body);
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("【待办 OA 获取token】请求 token 失败，请求人：" + userId + "，返回结果：" + body).toServiceException();
        }
        redisService.setCacheObject(redisKey, token, 10L, TimeUnit.MINUTES);
        return token;
    }

    /**
     * 查询OA待办列表
     */
    private List<SearchTodoResultDTO.TodoInfo> queryOATodo(String userId, String beginTime, String endTime) {
        List<SearchTodoResultDTO.TodoInfo> list = new ArrayList<>();

        int totalPage = 0;
        int page = 1;
        //获取OA待办列表
        do {
            String url = hbouOaProperties.getOaBaseUri() + hbouOaProperties.getOaTodoUri() + "?token=" + createToken(userId);
            int pageSize = hbouOaProperties.getOaTodoPageSize();
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put(HbouOpenApiConstant.OA_TODO_PAGE, page); //1为待办文件列表
            queryMap.put(HbouOpenApiConstant.OA_TODO_PAGE_SIZE, pageSize);
            queryMap.put(HbouOpenApiConstant.OA_TODO_BEGIN_TIME, beginTime);
            queryMap.put(HbouOpenApiConstant.OA_TODO_END_TIME, endTime);
            SearchTodoResultDTO resultDTO;
            try {
                String result = HttpUtil.createPost(url).body(JSON.toJSONString(queryMap)).execute().body();
                //去除空格
                resultDTO = JSON.parseObject(result, SearchTodoResultDTO.class);
            } catch (Exception e) {
                //查询失败
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("【待办OA同步待办信息】用户【" + userId + "】获取第 " + page + " 页数据失败：" + e.getMessage()).toServiceException();
            }
            List<SearchTodoResultDTO.TodoInfo> item = resultDTO.getData();
            if (CollUtil.isEmpty(item)) {
                log.info("【待办OA同步待办信息】用户【{}】获取数据为空", userId);
                break;
            }

            list.addAll(item);

            log.info("【待办OA同步待办信息】用户【{}】获取第{}页数据成功，当前总共获取{}条数据", userId, page, list.size());

            page++;

            //获取总页数
            if (totalPage == 0) {
                totalPage = resultDTO.getPages();
            }
        } while (page <= totalPage);
        log.info("【待办OA同步待办信息】用户【{}】共获取{}条数据", userId, list.size());
        return list;
    }


    /**
     * 查询已完成的OA待办列表
     *
     * @param userId    用户 userid
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 已完成的OA待办列表
     */
    private List<SearchTodoResultDTO.TodoInfo> queryOACompletedTodo(String userId, String beginTime, String endTime) {
        List<SearchTodoResultDTO.TodoInfo> list = new ArrayList<>();

        int totalPage = 0;
        int page = 1;
        //获取OA待办列表
        do {
            String url = hbouOaProperties.getOaBaseUri() + hbouOaProperties.getOaTodoCompletedUri() + "?token=" + createToken(userId);
            int pageSize = hbouOaProperties.getOaCompletedTodoPageSize();
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put(HbouOpenApiConstant.OA_TODO_PAGE, page); //1为待办文件列表
            queryMap.put(HbouOpenApiConstant.OA_TODO_PAGE_SIZE, pageSize);
            queryMap.put(HbouOpenApiConstant.OA_TODO_BEGIN_TIME, beginTime);
            queryMap.put(HbouOpenApiConstant.OA_TODO_END_TIME, endTime);
            SearchTodoResultDTO resultDTO;
            try {
                String result = HttpUtil.createPost(url).body(JSON.toJSONString(queryMap)).execute().body();
                //去除空格
                resultDTO = JSON.parseObject(result, SearchTodoResultDTO.class);
            } catch (Exception e) {
                //查询失败
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("【待办 OA 同步完成信息】用户【" + userId + "】获取第 " + page + " 页数据失败：" + e.getMessage()).toServiceException();
            }
            List<SearchTodoResultDTO.TodoInfo> item = resultDTO.getData();
            if (CollUtil.isEmpty(item)) {
                log.info("【待办 OA 同步完成信息】用户【{}】获取数据为空", userId);
                break;
            }

            list.addAll(item);

            log.info("【待办 OA 同步完成信息】用户【{}】获取第{}页数据成功，当前总共获取{}条数据", userId, page, list.size());

            page++;

            //获取总页数
            if (totalPage == 0) {
                totalPage = resultDTO.getPages();
            }
        } while (page <= totalPage);

        return list;
    }

}

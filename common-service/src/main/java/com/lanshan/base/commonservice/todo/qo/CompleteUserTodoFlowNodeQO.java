package com.lanshan.base.commonservice.todo.qo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 完成用户待办事项节点对象
 */
@ApiModel(value = "完成用户待办事项节点对象")
@Data
public class CompleteUserTodoFlowNodeQO implements Serializable {
    private static final long serialVersionUID = -2567664154928715406L;

    @ApiModelProperty("节点id")
    @NotNull(message = "节点id不能为空")
    private Long nodeId;

    @ApiModelProperty("节点用户列表")
    @NotEmpty(message = "节点用户列表不能为空")
    @Valid
    private List<CompleteUserTodoFlowNodeQO.NodeUserInfo> userInfoList;

    @Data
    public static class NodeUserInfo implements Serializable {

        private static final long serialVersionUID = 7723141612784660414L;

        @ApiModelProperty("用户id")
        @NotBlank(message = "用户id不能为空")
        private String userid;

        @ApiModelProperty("用户名称")
        @NotBlank(message = "用户名称不能为空")
        private String name;

    }
}

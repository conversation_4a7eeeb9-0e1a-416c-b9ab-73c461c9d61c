package com.lanshan.base.commonservice.welcomenewstudent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description {}
 * @date 2025/6/9 20:43
 */
@Getter
@AllArgsConstructor
public enum NewStuGroupTypeEnum {

    COLLEGE("1", "学院"),
    MAJOR("2", "专业"),
    CLASS("3", "班级"),
    ;

    private final String value;

    private final String desc;

    public static NewStuGroupTypeEnum getByValue(String value) {
        for (NewStuGroupTypeEnum item : values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return COLLEGE;
    }
}

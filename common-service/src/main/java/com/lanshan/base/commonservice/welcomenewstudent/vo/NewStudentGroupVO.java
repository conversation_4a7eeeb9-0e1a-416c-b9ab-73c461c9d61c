package com.lanshan.base.commonservice.welcomenewstudent.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 新生群表(NewStudentGroup)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "新生群表VO")
@Data
@ToString
public class NewStudentGroupVO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "群聊id")
    private String chatId;

    @ApiModelProperty(value = "群名称")
    private String groupName;

    @ApiModelProperty(value = "群主id")
    private String ownerUserid;

    @ApiModelProperty(value = "群主名称")
    private String ownerName;

    @ApiModelProperty(value = "加入群的用户列表")
    private List<String> joinInUser;

    @ApiModelProperty(value = "入学年份")
    private String year;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    @ApiModelProperty(value = "最后更新时间")
    private Date updateDate;
}


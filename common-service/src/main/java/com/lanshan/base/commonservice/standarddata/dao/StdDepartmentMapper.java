package com.lanshan.base.commonservice.standarddata.dao;

import com.lanshan.base.commonservice.standarddata.po.StdDepartment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.base.commonservice.standarddata.vo.StdDepartmentTreeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-06-04
 */
@Mapper
public interface StdDepartmentMapper extends BaseMapper<StdDepartment> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<StdDepartment> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<StdDepartment> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<StdDepartment> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<StdDepartment> entities);

    List<StdDepartmentTreeVO> getDeptTree();

}

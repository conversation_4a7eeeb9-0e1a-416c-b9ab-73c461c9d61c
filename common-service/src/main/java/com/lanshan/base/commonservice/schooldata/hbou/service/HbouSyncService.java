package com.lanshan.base.commonservice.schooldata.hbou.service;

import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxBzks;
import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxJzg;
import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxXs;
import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxZzjg;

import java.util.List;

/**
 * 湖北开放大学数据同步服务
 */
public interface HbouSyncService {
    /**
     * 同步学生信息
     */
    void syncStudent();

    /**
     * 同步教职工信息
     */
    void syncJzg();

    /**
     * 同步组织机构信息
     */
    void syncZzjg();

    /**
     * 同步组织机构信息
     */
    void syncBzks();

    /**
     * 批量保存学生信息
     */
    void saveBatchXs(List<HbouQywxXs> list);

    /**
     * 批量保存教职工信息
     */
    void saveBatchJzg(List<HbouQywxJzg> list);

    /**
     * 批量保存组织机构信息
     */
    void saveBatchZzjg(List<HbouQywxZzjg> list);

    /**
     * 批量保存本专科生信息
     */
    void saveBatchBzks(List<HbouQywxBzks> list);
}
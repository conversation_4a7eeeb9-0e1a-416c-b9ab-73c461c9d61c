package com.lanshan.base.commonservice.standardapp.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 课表-学生信息表(StdStudentInfo)表实体类
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class StdStudentInfo extends Model<StdStudentInfo> {
    /**
     * 学号
     */
    @TableId
    private String userId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 入学时间
     */
    private Date enrollmentDate;
    /**
     * 学院编号
     */
    private String collegeNumber;
    /**
     * 学院名称
     */
    private String collegeName;
    /**
     * 专业编号
     */
    private String professionalNumber;
    /**
     * 专业名称
     */
    private String professionalName;
    /**
     * 班级编号
     */
    private String classNumber;
    /**
     * 班级名称
     */
    private String className;
    /**
     * 学生类型 1 本科生 2 研究生
     */
    private Integer type;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.userId;
    }
}


package com.lanshan.base.commonservice.config;

import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ApiKey;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.List;

import static com.google.common.collect.Lists.newArrayList;

/**
 * <AUTHOR>
 */
@Configuration
public class SwaggerConfiguration {

    @Bean
    public Docket createWelcomeNewStudentApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("迎新应用")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.welcomenewstudent"))
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    @Bean
    public Docket createSchoolDateApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("学校数据")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.schooldata"))
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    @Bean
    public Docket createRepairApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("报修平台")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.repair"))
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }


    @Bean
    public Docket createInviteoutsideApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("邀请外部人员加入企微")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.inviteoutside"))
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    @Bean
    public Docket createAccessAppApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("API 控制")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.access"))
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    @Bean
    public Docket createStandardAppApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("查询应用")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.standardapp"))
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    @Bean
    public Docket createAddressBookApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("通讯录")
                .select()
                //加了ApiOperation注解的类，才生成接口文档
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.addressbook"))
                //包下的类，才生成接口文档
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    @Bean
    public Docket createMessageApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("消息中心")
                .select()
                //加了ApiOperation注解的类，才生成接口文档
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.message"))
                //包下的类，才生成接口文档
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    @Bean
    public Docket createTodoApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("待办中心")
                .select()
                //加了ApiOperation注解的类，才生成接口文档
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.todo"))
                //包下的类，才生成接口文档
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    @Bean
    public Docket createGroupApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("群组群聊")
                .select()
                //加了ApiOperation注解的类，才生成接口文档
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.group"))
                //包下的类，才生成接口文档
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    @Bean
    public Docket createSystemApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("系统控制")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.system")
                        .and(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class)))
                //包下的类，才生成接口文档
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    @Bean
    public Docket createCommonApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("其他通用")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.minio")
                        .or(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.common")))
                //包下的类，才生成接口文档
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    @Bean
    public Docket createWorkbenchApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .groupName("工作台")
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.lanshan.base.commonservice.workbench"))
                .paths(PathSelectors.any())
                .build()
                .directModelSubstitute(java.util.Date.class, String.class)
                .securitySchemes(security());
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("览山科技企业微信应用后台管理系统")
                .description("接口文档")
                .termsOfServiceUrl("https://www.lskejisoft.com")
                .version("0.5")
                .build();
    }

    private List<ApiKey> security() {
        return newArrayList(
                new ApiKey("Authorization", "Authorization", "header")
        );
    }
}

package com.lanshan.base.commonservice.addressbook.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.commonservice.addressbook.entity.UserInfoNewStuGuide;
import com.lanshan.base.commonservice.addressbook.qo.UserInfoNewStuGuideQO;
import com.lanshan.base.commonservice.addressbook.vo.UserInfoNewStuGuideVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 新生指南表(UserInfoNewStuGuide)表数据库访问层
 *
 * <AUTHOR>
 */
public interface UserInfoNewStuGuideDao extends BaseMapper<UserInfoNewStuGuide> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<UserInfoNewStuGuide> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<UserInfoNewStuGuide> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<UserInfoNewStuGuide> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<UserInfoNewStuGuide> entities);

    /**
     * 分页查询
     *
     * @param of 分页对象
     * @param qo 查询对象
     * @return 对象列表
     */
    IPage<UserInfoNewStuGuideVO> pageByParam(@Param("page") Page<UserInfoNewStuGuide> page, @Param("qo") UserInfoNewStuGuideQO qo);
}


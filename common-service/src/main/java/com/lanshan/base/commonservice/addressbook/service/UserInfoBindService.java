package com.lanshan.base.commonservice.addressbook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.addressbook.entity.UserInfoBind;
import com.lanshan.base.commonservice.addressbook.vo.UserIdentityInfoVO;

import java.util.List;

/**
 * 用户认证表(UserInfoBind)表服务接口
 *
 * <AUTHOR>
 */
public interface UserInfoBindService extends IService<UserInfoBind> {

    /**
     * 根据openId获取用户身份列表
     *
     * @param openId 微信openId
     * @return 用户身份列表
     */
    List<UserIdentityInfoVO> listIdentitiesByOpenId(String openId);

    /**
     * 根据openId删除用户绑定身份信息
     *
     * @param openId 微信openId
     */
    void removeByOpenId(String openId);

    /**
     * 获取用户绑定信息
     *
     * @param currentUserId 用户id
     * @return 用户绑定信息
     */
    UserIdentityInfoVO getNewStuIdentityInfo(String currentUserId);
}


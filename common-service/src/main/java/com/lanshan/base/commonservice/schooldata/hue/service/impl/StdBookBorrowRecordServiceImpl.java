package com.lanshan.base.commonservice.schooldata.hue.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.lanshan.base.api.dto.message.TextMsgBody;
import com.lanshan.base.api.dto.system.SysConfigVo;
import com.lanshan.base.api.enums.MsgChannelEnum;
import com.lanshan.base.api.feign.message.MessageFeign;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.config.manager.AsyncManager;
import com.lanshan.base.commonservice.schooldata.hue.converter.StdBookBorrowRecordConverter;
import com.lanshan.base.commonservice.schooldata.hue.converter.TQywxtxlzlTsqfxxConverter;
import com.lanshan.base.commonservice.schooldata.hue.dao.StdBookBorrowRecordDao;
import com.lanshan.base.commonservice.schooldata.hue.entity.StdBookBorrowRecord;
import com.lanshan.base.commonservice.schooldata.hue.entity.TQywxtxlzlTsqfxx;
import com.lanshan.base.commonservice.schooldata.hue.service.StdBookBorrowRecordService;
import com.lanshan.base.commonservice.schooldata.hue.service.TQywxtxlzlTsqfxxService;
import com.lanshan.base.commonservice.schooldata.hue.utils.HueClient;
import com.lanshan.base.commonservice.schooldata.hue.utils.HueClient.BookBorrowRecordResult;
import com.lanshan.base.commonservice.schooldata.hue.vo.StdBookBorrowRecordVO;
import com.lanshan.base.commonservice.schooldata.hue.vo.UserBookBorrowInfoVO;
import com.lanshan.base.commonservice.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 图书借阅记录表(StdBookBorrowRecord)表服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service("stdBookBorrowRecordService")
public class StdBookBorrowRecordServiceImpl extends ServiceImpl<StdBookBorrowRecordDao, StdBookBorrowRecord> implements StdBookBorrowRecordService {

    private final HueClient hueClient;

    private final TransactionTemplate transactionTemplate;
    // 批量大小
    private static final int BATCH_SIZE = 1000;
    // 最大重试次数
    private static final int MAX_RETRIES = 3;

    private static final String NOTICE_TEMPLATE = "你借阅的图书距离归还日期还有{}天，请注意合理安排阅读时间，避免违约。以下为待还书名：{}";

    private final MessageFeign messageFeign;

    private final ISysConfigService sysConfigService;

    private final TQywxtxlzlTsqfxxService tQywxtxlzlTsqfxxService;

    @Override
    public void syncBookBorrowRecord() {
        log.info("开始同步图书借阅记录");
        long startTime = System.currentTimeMillis();

        // 获取第一页数据和总页数
        BookBorrowRecordResult firstPageResult = hueClient.bookBorrowRecord(1, BATCH_SIZE, 0);
        if (Objects.isNull(firstPageResult) || Objects.isNull(firstPageResult.getRecords()) || firstPageResult.getRecords().isEmpty()) {
            log.warn("未获取到借阅记录数据，同步终止");
            return;
        }

        Integer maxPage = firstPageResult.getMaxPage();
        log.info("图书借阅记录总页数: {}, 每页条数: {}", maxPage, BATCH_SIZE);

        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(5);

        // 并行获取所有页的数据
        List<CompletableFuture<List<StdBookBorrowRecord>>> futures = new ArrayList<>();

        // 先添加第一页的数据
        List<StdBookBorrowRecord> allRecords = new ArrayList<>(firstPageResult.getRecords());

        // 并行获取剩余页的数据
        for (int page = 2; page <= maxPage; page++) {
            final int currentPage = page;
            CompletableFuture<List<StdBookBorrowRecord>> future = CompletableFuture.supplyAsync(() -> {
                int retryCount = 0;
                while (retryCount < MAX_RETRIES) {
                    try {
                        BookBorrowRecordResult result = hueClient.bookBorrowRecord(currentPage, BATCH_SIZE, null);
                        log.info("成功获取第 {} 页数据，数量: {}", currentPage,
                                result.getRecords() != null ? result.getRecords().size() : 0);
                        return result.getRecords();
                    } catch (Exception e) {
                        retryCount++;
                        log.error("获取第 {} 页数据失败，重试第 {} 次: {}", currentPage, retryCount, e.getMessage());
                        if (retryCount >= MAX_RETRIES) {
                            log.error("获取第 {} 页数据失败，超过最大重试次数", currentPage, e);
                            return Collections.emptyList();
                        }
                        try {
                            // 等待一段时间再重试
                            Thread.sleep(1000 * retryCount);
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            return Collections.emptyList();
                        }
                    }
                }
                return Collections.emptyList();
            }, executor);
            futures.add(future);
        }

        // 等待所有并行任务完成并收集结果
        for (CompletableFuture<List<StdBookBorrowRecord>> future : futures) {
            try {
                List<StdBookBorrowRecord> records = future.get();
                if (records != null && !records.isEmpty()) {
                    allRecords.addAll(records);
                }
            } catch (InterruptedException e) {
                log.error("等待获取借阅记录数据被中断", e);
                Thread.currentThread().interrupt();
            } catch (ExecutionException e) {
                log.error("获取借阅记录数据失败", e.getCause());
            }
        }

        // 关闭线程池
        executor.shutdown();

        log.info("成功获取所有借阅记录数据，总数量: {}", allRecords.size());

        // 分批处理数据
        List<List<StdBookBorrowRecord>> batches = Lists.partition(allRecords, BATCH_SIZE);
        AtomicInteger batchCount = new AtomicInteger(0);
        int totalBatches = batches.size();

        this.baseMapper.truncate();
        for (List<StdBookBorrowRecord> batch : batches) {
            int currentBatch = batchCount.incrementAndGet();
            try {
                transactionTemplate.executeWithoutResult(status -> {
                    try {
                        this.saveBatch(batch, BATCH_SIZE);
                        log.info("成功保存第 {}/{} 批借阅记录数据，数量: {}", currentBatch, totalBatches, batch.size());
                    } catch (Exception e) {
                        log.error("保存第 {}/{} 批借阅记录数据失败", currentBatch, totalBatches, e);
                        status.setRollbackOnly();
                        throw new RuntimeException(e);
                    }
                });
            } catch (Exception e) {
                log.error("处理第 {}/{} 批借阅记录数据事务失败", currentBatch, totalBatches, e);
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("同步图书借阅记录完成，总耗时: {} 秒", (endTime - startTime) / 1000);
    }

    @Override
    public void notice() {
        Date date = new Date();
        SysConfigVo corpIdConfig = sysConfigService.getByConfigKey("corpId");
        SysConfigVo agentIdConfig = sysConfigService.getByConfigKey("book.borrow.agentId");
        SysConfigVo noticeConfig = sysConfigService.getByConfigKey("book.borrow.notice.config");
        if (Objects.isNull(corpIdConfig) || Objects.isNull(agentIdConfig) || Objects.isNull(noticeConfig)) {
            log.error("corpId 或 agentId 或通知配置不存在");
            return;
        }
        String corpId = corpIdConfig.getConfigValue();
        String agentId = agentIdConfig.getConfigValue();
        String noticeConfigValue = noticeConfig.getConfigValue();
        String[] noticeConfigValueArray = noticeConfigValue.split(",");

        for (String dayAgoStr : noticeConfigValueArray) {
            int dayAgo = Integer.parseInt(dayAgoStr);
            DateTime dayAgoDate = DateUtil.offsetDay(date, dayAgo);
            List<StdBookBorrowRecord> sevenDaysAgoList = lambdaQuery()
                    .ge(StdBookBorrowRecord::getYhrq, DateUtil.formatDate(dayAgoDate))
                    .le(StdBookBorrowRecord::getYhrq, DateUtil.formatDate(dayAgoDate))
                    .eq(StdBookBorrowRecord::getSfgh, "0")
                    .list();
            if (CollUtil.isNotEmpty(sevenDaysAgoList)) {
                AsyncManager.me().execute(() -> {
                    sendNotice(sevenDaysAgoList, corpId, agentId, dayAgo);
                });
            }
        }
    }

    @Override
    public UserBookBorrowInfoVO getUserBookBorrowInfo(String sfgh) {
        String currentUserId = SecurityContextHolder.getUserId();
        //查询所有借书数据
        List<StdBookBorrowRecord> bookBorrowRecordList = this.list(Wrappers.lambdaQuery(StdBookBorrowRecord.class)
                .eq(StdBookBorrowRecord::getDzzjh, currentUserId)
                .eq(StrUtil.isNotBlank(sfgh), StdBookBorrowRecord::getSfgh, sfgh)
                .orderByAsc(StdBookBorrowRecord::getYhrq)
        );
        //查询所有罚款的书籍列表
        List<TQywxtxlzlTsqfxx> tQywxtxlzlTsqfxxList = tQywxtxlzlTsqfxxService.list(Wrappers.lambdaQuery(TQywxtxlzlTsqfxx.class)
                    .eq(TQywxtxlzlTsqfxx::getZjh, currentUserId)
                    .eq(TQywxtxlzlTsqfxx::getClztm, "0")
                    .orderByAsc(TQywxtxlzlTsqfxx::getJilrq)
            );

        List<StdBookBorrowRecordVO> stdBookBorrowRecordVOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(bookBorrowRecordList)) {
            for (StdBookBorrowRecord stdBookBorrowRecord : bookBorrowRecordList) {
                StdBookBorrowRecordVO vo = StdBookBorrowRecordConverter.INSTANCE.toVO(stdBookBorrowRecord);
                addBorrowStatus(vo);
                stdBookBorrowRecordVOList.add(vo);
            }
            stdBookBorrowRecordVOList.sort(Comparator.comparing(StdBookBorrowRecordVO::getBorrowStatus, Comparator.nullsLast(Comparator.reverseOrder()))
                    .thenComparing(Comparator.comparing(StdBookBorrowRecordVO::getYhrq, Comparator.nullsLast(Comparator.naturalOrder()))));
        }
        UserBookBorrowInfoVO userBookBorrowInfoVO = new UserBookBorrowInfoVO();
        userBookBorrowInfoVO.setBorrowRecordList(stdBookBorrowRecordVOList);
        userBookBorrowInfoVO.setTsQfxxVOList(TQywxtxlzlTsqfxxConverter.INSTANCE.toVO(tQywxtxlzlTsqfxxList));
        return userBookBorrowInfoVO;
    }

    private static void addBorrowStatus(StdBookBorrowRecordVO vo) {
        String xjbs = vo.getXjbs();
        String yhrq = vo.getYhrq();
        String sfgh = vo.getSfgh();
        if ("0".equals(sfgh)) {
            DateTime currentDate = DateUtil.date();
            DateTime yhrqDate = DateUtil.parse(yhrq, "yyyy-MM-dd");
            long yhrqTime = yhrqDate.getTime();
            if ("1".equals(xjbs)) {
                vo.setBorrowStatus("2");
            } else if (yhrqTime < currentDate.getTime()) {
                vo.setBorrowStatus("4");
            } else if (yhrqTime > currentDate.getTime() && DateUtil.between(yhrqDate, currentDate, DateUnit.DAY) <= 7) {
                vo.setBorrowStatus("3");
            } else {
                vo.setBorrowStatus("1");
            }
        }
    }

    private void sendNotice(List<StdBookBorrowRecord> sevenDaysAgoList, String corpId, String agentId, Integer dayAgo) {
        Map<String, List<StdBookBorrowRecord>> sevenDaysAgoMap = sevenDaysAgoList.stream().collect(Collectors.groupingBy(StdBookBorrowRecord::getDzzjh));
        for (Map.Entry<String, List<StdBookBorrowRecord>> entry : sevenDaysAgoMap.entrySet()) {
            String userId = entry.getKey();
            List<StdBookBorrowRecord> value = entry.getValue();
            String bookStr = value.stream().map(bookBorrowRecord -> {
                return "《" + bookBorrowRecord.getTsmc() + "》";
            }).collect(Collectors.joining(","));
            TextMsgBody textMsgBody = new TextMsgBody();
            textMsgBody.setMsgType("text");
            textMsgBody.setSendChannels(CollectionUtil.newArrayList(MsgChannelEnum.WEIXIN_CORP_CHANNEL));
            textMsgBody.setToUser(userId);
            textMsgBody.setContent(StrUtil.format(NOTICE_TEMPLATE, dayAgo, bookStr));
            messageFeign.sendText(corpId, agentId, textMsgBody);
            log.info("发送图书通知成功，userId:{},dayAgo:{},bookStr:{}", userId, dayAgo, bookStr);
        }
    }
}


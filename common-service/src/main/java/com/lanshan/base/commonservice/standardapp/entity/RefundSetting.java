package com.lanshan.base.commonservice.standardapp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 退费设置
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
public class RefundSetting extends Model<RefundSetting> {
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 年份
     */
    private Integer year;

    /**
     * 转换日期
     */
    private Date transferDate;

    /**
     * 退费日期
     */
    private Date refundDate;

    /**
     * 捐赠说明
     */
    private String donateRemark;
    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}

package com.lanshan.base.commonservice.welcomenewstudent.task;

import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 新生任务对象
 */
@Slf4j
@Component
public class NewStudentTask {

    @Resource
    private NewStudentDataService newStudentDataService;

    /**
     * 发送迎新消息，并入群
     */
    @XxlJob("NewStudentTask.sendWelcomeMsg")
    public void sendWelcomeMsg() {
        try {
            log.info("开始执行发送迎新消息任务");
            newStudentDataService.sendWelcomeMsg();
            log.info("发送迎新消息任务执行完成");
        } catch (Exception e) {
            log.error("发送迎新消息任务执行失败", e);
            throw e;
        }
    }

    /**
     * 新生入群 每天执行一次，作为发送新生消息同时入群失败的补充
     */
    @XxlJob("NewStudentTask.joinUserToWelcomeGroup")
    public void joinUserToWelcomeGroup() {
        try {
            log.info("开始执行新生入群任务");
            newStudentDataService.joinUserToWelcomeGroup();
            log.info("新生入群任务执行完成");
        } catch (Exception e) {
            log.error("新生入群任务执行失败", e);
            throw e;
        }
    }
}

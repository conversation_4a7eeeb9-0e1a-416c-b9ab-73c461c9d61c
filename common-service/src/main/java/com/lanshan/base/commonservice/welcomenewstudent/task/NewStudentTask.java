package com.lanshan.base.commonservice.welcomenewstudent.task;

import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 新生任务对象
 */
@Slf4j
@Component
public class NewStudentTask {

    private NewStudentDataService newStudentDataService;

    /**
     * 发送迎新消息，并入群
     */
    @XxlJob("NewStudentTask.sendWelcomeMsg")
    public void sendWelcomeMsg() {
        newStudentDataService.sendWelcomeMsg();
    }

    /**
     * 新生入群 每天执行一次，作为发送新生消息同时入群失败的补充
     */
    @XxlJob("NewStudentTask.joinUserToWelcomeGroup")
    public void joinUserToWelcomeGroup() {
        newStudentDataService.joinUserToWelcomeGroup();
    }
}

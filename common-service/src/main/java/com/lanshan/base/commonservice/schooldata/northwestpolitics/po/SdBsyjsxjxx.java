package com.lanshan.base.commonservice.schooldata.northwestpolitics.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

/**
 * 博士研究生学籍信息表
 * <AUTHOR> yang.
 * @since 2025-05-26
 */

@Data
@TableName(value = "sd_bsyjsxjxx", schema = "school_data",autoResultMap = true)
public class SdBsyjsxjxx implements Serializable {

	private static final long serialVersionUID = 1L;

    //学号 
    private String xh;

    //在校类型
    private String zxlx;

    //是否在校
    private String sfzx;



}

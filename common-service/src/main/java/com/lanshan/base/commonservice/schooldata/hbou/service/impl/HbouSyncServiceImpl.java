package com.lanshan.base.commonservice.schooldata.hbou.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.lanshan.base.commonservice.schooldata.hbou.converter.HbouDataConverter;
import com.lanshan.base.commonservice.schooldata.hbou.dao.HbouQywxBzksDao;
import com.lanshan.base.commonservice.schooldata.hbou.dao.HbouQywxJzgDao;
import com.lanshan.base.commonservice.schooldata.hbou.dao.HbouQywxXsDao;
import com.lanshan.base.commonservice.schooldata.hbou.dao.HbouQywxZzjgDao;
import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxBzks;
import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxJzg;
import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxXs;
import com.lanshan.base.commonservice.schooldata.hbou.entity.HbouQywxZzjg;
import com.lanshan.base.commonservice.schooldata.hbou.service.HbouSyncService;
import com.lanshan.base.commonservice.schooldata.hbou.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service("hbouSyncService")
public class HbouSyncServiceImpl implements HbouSyncService {

    private final HbouXsClient hbouXsClient;

    private final CdspClient cdspClient;

    private final HbouQywxXsDao hbouQywxXsDao;

    private final HbouQywxJzgDao hbouQywxJzgDao;

    private final HbouQywxZzjgDao hbouQywxZzjgDao;

    private final HbouQywxBzksDao hbouQywxBzksDao;

    private final ThreadPoolConfig threadPoolConfig;

    private Executor syncExecutor;

    private final int batchSize = 100;
    private final int cdspPageSize = 1000;

    @Autowired
    public HbouSyncServiceImpl(
            HbouXsClient hbouXsClient,
            CdspClient cdspClient,
            HbouQywxXsDao hbouQywxXsDao,
            HbouQywxJzgDao hbouQywxJzgDao,
            HbouQywxZzjgDao hbouQywxZzjgDao,
            HbouQywxBzksDao hbouQywxBzksDao,
            ThreadPoolConfig threadPoolConfig) {
        this.hbouXsClient = hbouXsClient;
        this.cdspClient = cdspClient;
        this.hbouQywxXsDao = hbouQywxXsDao;
        this.hbouQywxJzgDao = hbouQywxJzgDao;
        this.hbouQywxZzjgDao = hbouQywxZzjgDao;
        this.hbouQywxBzksDao = hbouQywxBzksDao;
        this.threadPoolConfig = threadPoolConfig;
    }

    private synchronized Executor getSyncExecutor() {
        if (syncExecutor == null) {
            syncExecutor = threadPoolConfig.syncExecutor();
        }
        return syncExecutor;
    }

    @Override
    public void syncStudent() {
        log.info("开始同步学生数据");
        try {
            // 清空表
            hbouQywxXsDao.truncate();
            AtomicInteger currentPage = new AtomicInteger(0);
            String apiDomain = hbouXsClient.getApiDomain();
            String token = hbouXsClient.getToken(apiDomain);
            XsxxResult firstPageResult = hbouXsClient.getUserList(currentPage.incrementAndGet(),apiDomain,token);
            if (Objects.isNull(firstPageResult) || CollUtil.isEmpty(firstPageResult.getUsers())) {
                log.warn("未获取到学生数据，同步终止");
                return;
            }
            // 计算总页数
            int totalPages = (int) Math.ceil((double) firstPageResult.getTotal() / firstPageResult.getPageSize());
            log.info("学生数据总记录数: {}, 页大小: {}, 总页数: {}",
                    firstPageResult.getTotal(), firstPageResult.getPageSize(), totalPages);
            // 保存第一页数据
            saveBatchXs(HbouDataConverter.INSTANCE.dataToHbouXs(firstPageResult.getUsers()));
            // 创建并行任务处理剩余页
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            while (currentPage.get() < totalPages) {
                final int page = currentPage.incrementAndGet();
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        XsxxResult result = hbouXsClient.getUserList(page, apiDomain, token);
                        if (Objects.nonNull(result) && CollUtil.isNotEmpty(result.getUsers())) {
                            saveBatchXs(HbouDataConverter.INSTANCE.dataToHbouXs(result.getUsers()));
                            log.info("同步学生数据成功, 页码: {}, 数据量: {}", page, result.getUsers().size());
                        }
                    } catch (Exception e) {
                        log.error("同步学生数据失败, 页码: {}", page, e);
                    }
                }, getSyncExecutor());
                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            log.info("学生数据同步完成，总页数: {}", totalPages);
        } catch (Exception e) {
            log.error("同步学生数据过程发生异常", e);
            throw e;
        }
    }

    @Override
    public void syncJzg() {
        log.info("开始同步教职工数据");
        try {
            // 清空表
            hbouQywxJzgDao.truncate();

            // 获取第一页数据和总页数
            AtomicInteger currentPage = new AtomicInteger(0);
            JzgxxResult firstPageResult = cdspClient.getJzgxx(currentPage.incrementAndGet());

            if (Objects.isNull(firstPageResult) || CollUtil.isEmpty(firstPageResult.getItems())) {
                log.warn("未获取到教职工数据，同步终止");
                return;
            }

            // 计算总页数
            int totalPages = (int) Math.ceil((double) firstPageResult.getTotal() / cdspPageSize);
            log.info("教职工数据总记录数: {}, 页大小: {}, 总页数: {}",
                    firstPageResult.getTotal(), cdspPageSize, totalPages);

            // 保存第一页数据
            saveBatchJzg(HbouDataConverter.INSTANCE.dataToHbouJzg(firstPageResult.getItems()));

            // 创建并行任务处理剩余页
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            while (currentPage.get() < totalPages) {
                final int page = currentPage.incrementAndGet();
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        JzgxxResult result = cdspClient.getJzgxx(page);
                        if (Objects.nonNull(result) && CollUtil.isNotEmpty(result.getItems())) {
                            saveBatchJzg(HbouDataConverter.INSTANCE.dataToHbouJzg(result.getItems()));
                            log.info("同步教职工数据成功, 页码: {}, 数据量: {}", page, result.getItems().size());
                        }
                    } catch (Exception e) {
                        log.error("同步教职工数据失败, 页码: {}", page, e);
                    }
                }, getSyncExecutor());
                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            log.info("教职工数据同步完成，总页数: {}", totalPages);
        } catch (Exception e) {
            log.error("同步教职工数据过程发生异常", e);
            throw e;
        }
    }

    @Override
    public void syncZzjg() {
        log.info("开始同步组织机构数据");
        try {
            // 清空表
            hbouQywxZzjgDao.truncate();

            // 获取第一页数据和总页数
            AtomicInteger currentPage = new AtomicInteger(0);
            ZzjgResult firstPageResult = cdspClient.getZzjg(currentPage.incrementAndGet());

            if (Objects.isNull(firstPageResult) || CollUtil.isEmpty(firstPageResult.getItems())) {
                log.warn("未获取到组织机构数据，同步终止");
                return;
            }

            // 计算总页数
            int totalPages = (int) Math.ceil((double) firstPageResult.getTotal() / cdspPageSize);
            log.info("组织机构数据总记录数: {}, 页大小: {}, 总页数: {}",
                    firstPageResult.getTotal(), cdspPageSize, totalPages);

            // 保存第一页数据
            saveBatchZzjg(HbouDataConverter.INSTANCE.dataToHbouZzjg(firstPageResult.getItems()));

            // 创建并行任务处理剩余页
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            while (currentPage.get() < totalPages) {
                final int page = currentPage.incrementAndGet();
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        ZzjgResult result = cdspClient.getZzjg(page);
                        if (Objects.nonNull(result) && CollUtil.isNotEmpty(result.getItems())) {
                            saveBatchZzjg(HbouDataConverter.INSTANCE.dataToHbouZzjg(result.getItems()));
                            log.info("同步组织机构数据成功, 页码: {}, 数据量: {}", page, result.getItems().size());
                        }
                    } catch (Exception e) {
                        log.error("同步组织机构数据失败, 页码: {}", page, e);
                    }
                }, getSyncExecutor());
                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            log.info("组织机构数据同步完成，总页数: {}", totalPages);
        } catch (Exception e) {
            log.error("同步组织机构数据过程发生异常", e);
            throw e;
        }
    }

    @Override
    public void syncBzks() {
        log.info("开始同步本专科生数据");
        try {
            // 清空表
            hbouQywxBzksDao.truncate();

            // 获取第一页数据和总页数
            AtomicInteger currentPage = new AtomicInteger(0);
            BzksResult firstPageResult = cdspClient.getBzks(currentPage.incrementAndGet());

            if (Objects.isNull(firstPageResult) || CollUtil.isEmpty(firstPageResult.getItems())) {
                log.warn("未获取到同步本专科生数据，同步终止");
                return;
            }

            // 计算总页数
            int totalPages = (int) Math.ceil((double) firstPageResult.getTotal() / cdspPageSize);
            log.info("同步本专科生数据总记录数: {}, 页大小: {}, 总页数: {}",
                    firstPageResult.getTotal(), cdspPageSize, totalPages);

            // 保存第一页数据
            saveBatchBzks(HbouDataConverter.INSTANCE.dataToHbouBzks(firstPageResult.getItems()));

            // 创建并行任务处理剩余页
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            while (currentPage.get() < totalPages) {
                final int page = currentPage.incrementAndGet();
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        BzksResult result = cdspClient.getBzks(page);
                        if (Objects.nonNull(result) && CollUtil.isNotEmpty(result.getItems())) {
                            saveBatchBzks(HbouDataConverter.INSTANCE.dataToHbouBzks(result.getItems()));
                            log.info("同步同步本专科生数据成功, 页码: {}, 数据量: {}", page, result.getItems().size());
                        }
                    } catch (Exception e) {
                        log.error("同步同步本专科生数据失败, 页码: {}", page, e);
                    }
                }, getSyncExecutor());
                futures.add(future);
            }

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            log.info("同步本专科生数据同步完成，总页数: {}", totalPages);
        } catch (Exception e) {
            log.error("同步同步本专科生数据过程发生异常", e);
            throw e;
        }
    }

    @Override
    public void saveBatchXs(List<HbouQywxXs> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        // 分批处理以优化性能
        int size = list.size();
        for (int i = 0; i < size; i += batchSize) {
            int endIndex = Math.min(i + batchSize, size);
            List<HbouQywxXs> subList = list.subList(i, endIndex);
            try {
                hbouQywxXsDao.insertBatch(subList);
                log.debug("批量保存学生数据成功, 批次: {}/{}, 数据量: {}",
                        (i / batchSize) + 1, (int) Math.ceil((double) size / batchSize), subList.size());
            } catch (Exception e) {
                log.error("批量保存学生数据失败, 批次: {}/{}, 数据量: {}",
                        (i / batchSize) + 1, (int) Math.ceil((double) size / batchSize), subList.size(), e);
            }
        }
    }

    @Override
    public void saveBatchJzg(List<HbouQywxJzg> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        // 分批处理以优化性能
        int size = list.size();
        for (int i = 0; i < size; i += batchSize) {
            int endIndex = Math.min(i + batchSize, size);
            List<HbouQywxJzg> subList = list.subList(i, endIndex);
            try {
                hbouQywxJzgDao.insertBatch(subList);
                log.debug("批量保存教职工数据成功, 批次: {}/{}, 数据量: {}",
                        (i / batchSize) + 1, (int) Math.ceil((double) size / batchSize), subList.size());
            } catch (Exception e) {
                log.error("批量保存教职工数据失败, 批次: {}/{}, 数据量: {}",
                        (i / batchSize) + 1, (int) Math.ceil((double) size / batchSize), subList.size(), e);
            }
        }
    }

    @Override
    public void saveBatchZzjg(List<HbouQywxZzjg> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        // 分批处理以优化性能
        int size = list.size();
        for (int i = 0; i < size; i += batchSize) {
            int endIndex = Math.min(i + batchSize, size);
            List<HbouQywxZzjg> subList = list.subList(i, endIndex);
            try {
                hbouQywxZzjgDao.insertBatch(subList);
                log.debug("批量保存组织机构数据成功, 批次: {}/{}, 数据量: {}",
                        (i / batchSize) + 1, (int) Math.ceil((double) size / batchSize), subList.size());
            } catch (Exception e) {
                log.error("批量保存组织机构数据失败, 批次: {}/{}, 数据量: {}",
                        (i / batchSize) + 1, (int) Math.ceil((double) size / batchSize), subList.size(), e);
            }
        }
    }

    @Override
    public void saveBatchBzks(List<HbouQywxBzks> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        // 分批处理以优化性能
        int size = list.size();
        for (int i = 0; i < size; i += batchSize) {
            int endIndex = Math.min(i + batchSize, size);
            List<HbouQywxBzks> subList = list.subList(i, endIndex);
            try {
                hbouQywxBzksDao.insertBatch(subList);
                log.debug("批量保存本专科生数据成功, 批次: {}/{}, 数据量: {}",
                        (i / batchSize) + 1, (int) Math.ceil((double) size / batchSize), subList.size());
            } catch (Exception e) {
                log.error("批量保存本专科生数据失败, 批次: {}/{}, 数据量: {}",
                        (i / batchSize) + 1, (int) Math.ceil((double) size / batchSize), subList.size(), e);
            }
        }
    }
}
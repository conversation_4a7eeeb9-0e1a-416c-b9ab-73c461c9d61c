package com.lanshan.base.commonservice.addressbook.controller;

import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.addressbook.dto.UserIdCardDTO;
import com.lanshan.base.commonservice.addressbook.service.UserFaceIdentifyService;
import com.lanshan.base.commonservice.addressbook.vo.UserInfoBindVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("user-info/h5")
@Api(tags = "用户身份信息绑定-用户端", hidden = true)
public class UserInfoBindController {

    private final UserFaceIdentifyService userFaceIdentifyService;

    @PostMapping("getUserIdKey")
    @ApiOperation("获取人脸核身用的userIdKey")
    public Result<String> getUserIdKey(@RequestBody UserIdCardDTO dto) throws WxErrorException {
        return Result.build(userFaceIdentifyService.getUserIdKey(dto));

    }

    @GetMapping("getUserIdentifyInfo")
    @ApiOperation("获取用户的认证身份信息列表")
    public Result<List<UserInfoBindVO>> getUserIdentifyInfo(@RequestParam String verifyResult) throws WxErrorException {
        return Result.build(userFaceIdentifyService.getUserIdentifyInfo(verifyResult));
    }

    @GetMapping("getUserPhoneNumber")
    @ApiOperation("获取用户手机号")
    public Result<String> getUserPhoneNumber(@RequestParam String code) throws WxErrorException {
        return Result.build(userFaceIdentifyService.getUserPhoneNumber(code));
    }

    @GetMapping("getUserIdentifyInfoNoAuth")
    @ApiOperation("获取用户的认证身份信息列表（未人脸认证）")
    public Result<List<UserInfoBindVO>> getUserIdentifyInfoNoAuth(@RequestParam String openId, @RequestParam String name, @RequestParam String idCardNum) {
        return Result.build(userFaceIdentifyService.getUserIdentifyInfoNoAuth(openId, name, idCardNum));
    }
}

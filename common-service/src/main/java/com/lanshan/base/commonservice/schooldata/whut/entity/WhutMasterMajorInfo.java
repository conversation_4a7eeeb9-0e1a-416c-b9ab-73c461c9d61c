package com.lanshan.base.commonservice.schooldata.whut.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 研究生专业信息表(WhutMasterMajorInfo)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class WhutMasterMajorInfo extends Model<WhutMasterMajorInfo> {
    /**
     * 自增主键ID
     */
    private Long id;
    /**
     * 唯一标识
     */
    private String wybs;
    /**
     * 专业编码
     */
    private String zybm;
    /**
     * 专业名称
     */
    private String zymc;
    /**
     * 专业英文名称
     */
    private String zyywmc;
    /**
     * 二级学科码
     */
    private String ejxkm;
    /**
     * 研究学科码
     */
    private String yjxkm;
    /**
     * 二级学科名称
     */
    private String ejxkmc;
    /**
     * 学生类型码
     */
    private String xslxm;
    /**
     * 学生类型码代码名称
     */
    private String xslxmdmmc;
    /**
     * 是否在使用
     */
    private String sfzsy;
    /**
     * 时间戳
     */
    private Date tstamp;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}


package com.lanshan.base.commonservice.addressbook.controller;

import com.lanshan.base.api.constant.ServiceConstant;
import com.lanshan.base.api.qo.user.*;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.addressbook.service.CommonService;
import com.lanshan.base.commonservice.config.properties.AgentProperties;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Description: 通讯录通用接口
 * @Author: GaoJian
 * @Date: 2023/10/27
 */
@RestController
@RequestMapping(value = ServiceConstant.COMMON_ADDRESS_BOOK)
@Validated
@Api(tags = "通讯录通用接口", hidden = true)
@Slf4j
public class AddressBookCommonController {

    @Resource
    private CommonService commonService;

    @Resource
    private AgentProperties agentProperties;

    @ApiOperation("操作表处理")
    @GetMapping("addressbookOperate")
    public Result<Object> addressbookOperate() {
        commonService.addressbookOperate();
        return Result.build();
    }

    @ApiOperation("备份")
    @GetMapping("cpBak")
    public Result<Object> cpBak() {
        commonService.cpBak();
        return Result.build();
    }

    @ApiOperation("备份清除")
    @GetMapping("cpBakRemove")
    public Result<Object> cpBakRemove() {
        commonService.cpBakRemove();
        return Result.build();
    }

    @ApiOperation("应用新组织架构")
    @GetMapping("applyNewDept")
    public Result<Object> applyNewDept() {
        commonService.applyNewDept();
        return Result.build();
    }

    @ApiOperation("恢复组织架构")
    @GetMapping("cpRecover")
    public Result<Object> cpRecover() {
        commonService.cpRecover();
        return Result.build();
    }

    @ApiOperation("通讯录同步")
    @GetMapping("addressbookSync")
    public Result<Object> addressbookSync() {
        commonService.addressbookSync();
        return Result.build();
    }

    @ApiOperation("通用-单条新增用户")
    @PostMapping("saveUser")
    public Result<Object> saveUser(@RequestBody UserSaveSimpleQo qo) {
        commonService.saveUser(qo);
        return Result.build();
    }

    @ApiOperation("通用-批量新增用户（入操作表）")
    @PostMapping("batchSaveUser")
    public Result<Object> batchSaveUser(@RequestBody @Validated @NotEmpty(message = "参数列表不能为空") List<UserSaveSimpleQo> list) {
        commonService.batchSaveUser(list);
        return Result.build();
    }

    @ApiOperation("通用-单条新增部门")
    @PostMapping("saveDepartment")
    public Result<Object> saveDepartment(@RequestBody DepartmentSaveQo qo) {
        commonService.saveDepartment(qo);
        return Result.build();
    }

    @ApiOperation("通用-批量新增部门（入操作表）")
    @PostMapping("batchSaveDepartment")
    public Result<Object> batchSaveDepartment(@RequestBody @Validated @NotEmpty(message = "参数列表不能为空") List<DepartmentSaveQo> list) {
        commonService.batchSaveDepartment(list);
        return Result.build();
    }

    @ApiOperation("通用-单条新增标签")
    @PostMapping("saveTag")
    public Result<Object> saveTag(@RequestBody TagSaveQo qo) {
        commonService.saveTag(qo);
        return Result.build();
    }

    @ApiOperation("通用-批量新增标签（入操作表）")
    @PostMapping("batchSaveTag")
    public Result<Object> batchSaveTag(@RequestBody @Validated @NotEmpty(message = "参数列表不能为空") List<TagSaveQo> list) {
        commonService.batchSaveTag(list);
        return Result.build();
    }

    @ApiOperation("通用-批量新增用户部门（入操作表）")
    @PostMapping("batchSaveUserDept")
    public Result<Object> batchSaveUserDeptRelation(@RequestBody @Validated @NotEmpty(message = "参数列表不能为空") List<UserDeptRelationQo> list) {
        commonService.batchSaveUserDeptRelation(list);
        return Result.build();
    }

    @ApiOperation("通用-批量新增用户标签（入操作表）")
    @PostMapping("batchSaveUserTagRelation")
    public Result<Object> batchSaveUserTagRelation(@RequestBody @Validated @NotEmpty(message = "参数列表不能为空") List<UserTagRelationQo> list) {
        commonService.batchSaveUserTagRelation(list);
        return Result.build();
    }

    @ApiOperation("通用-批量新增部门标签（入操作表）")
    @PostMapping("batchSaveDeptTagRelation")
    public Result<Object> batchSaveDeptTagRelation(@RequestBody @Validated @NotEmpty(message = "参数列表不能为空") List<DeptTagRelationQo> list) {
        commonService.batchSaveDeptTagRelation(list);
        return Result.build();
    }

    @ApiOperation("是否能获取敏感信息")
    @GetMapping("isSensitiveInfoEnabled")
    public Result<Boolean> isSensitiveInfoEnabled() {
        return Result.build(agentProperties.getSensitiveInfoEnabled());
    }

    @ApiOperation("用户绑定手机号")
    @GetMapping(value = "useridBindMobile")
    Result<Object> useridBindMobile(@RequestParam(name = "userid") String userid,
                                    @RequestParam(name = "mobile") String mobile) {
        commonService.useridBindMobile(userid, mobile);
        return Result.build();
    }

    @ApiOperation("用户绑定手机号-测试企微")
    @GetMapping(value = "useridBindMobileTestCp")
    Result<Object> useridBindMobileTestCp(@RequestParam(name = "userid") String userid,
                                          @RequestParam(name = "mobile") String mobile) {
        commonService.useridBindMobileTestCp(userid, mobile);
        return Result.build();
    }

    @ApiOperation("新增通讯录信息 包含用户、部门")
    @PostMapping(value = "addAddressBookInfo")
    Result<Object> addAddressBookInfo(@RequestBody AddAddressBookQo qo) {
        commonService.addAddressBookInfo(qo);
        return Result.build();
    }

    @ApiOperation("外部人员加入")
    @PostMapping("externalJoin")
    public Result<Object> externalJoin(@RequestBody ExternalJoinQo qo) {
        commonService.externalJoin(qo);
        return Result.build();
    }

}

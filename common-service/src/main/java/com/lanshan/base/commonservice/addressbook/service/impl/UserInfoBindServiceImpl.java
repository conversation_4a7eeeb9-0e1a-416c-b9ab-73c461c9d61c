package com.lanshan.base.commonservice.addressbook.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.commonservice.addressbook.dao.UserInfoBindDao;
import com.lanshan.base.commonservice.addressbook.entity.UserInfoBind;
import com.lanshan.base.commonservice.addressbook.service.UserInfoBindService;
import com.lanshan.base.commonservice.addressbook.vo.UserIdentityInfoVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户认证表(UserInfoBind)表服务实现类
 *
 * <AUTHOR>
 */
@Service("userInfoBindService")
public class UserInfoBindServiceImpl extends ServiceImpl<UserInfoBindDao, UserInfoBind> implements UserInfoBindService {

    @Override
    public List<UserIdentityInfoVO> listIdentitiesByOpenId(String openId) {
        return baseMapper.listIdentitiesByOpenId(openId);
    }

    /**
     * 根据openId删除用户绑定身份信息
     *
     * @param openId 微信openId
     */
    @Override
    public void removeByOpenId(String openId) {
        super.remove(Wrappers.<UserInfoBind>lambdaQuery().eq(UserInfoBind::getOpenId, openId));
    }

    @Override
    public UserIdentityInfoVO getNewStuIdentityInfo(String currentUserId) {
        return baseMapper.getNewStuIdentityInfo(currentUserId);
    }
}


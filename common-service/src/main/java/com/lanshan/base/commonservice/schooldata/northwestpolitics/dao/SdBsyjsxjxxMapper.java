package com.lanshan.base.commonservice.schooldata.northwestpolitics.dao;

import com.lanshan.base.commonservice.schooldata.northwestpolitics.po.SdBsyjsxjxx;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 博士研究生学籍信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-26
 */
@Mapper
public interface SdBsyjsxjxxMapper extends BaseMapper<SdBsyjsxjxx> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<SdBsyjsxjxx> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<SdBsyjsxjxx> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<SdBsyjsxjxx> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<SdBsyjsxjxx> entities);

    @Update("TRUNCATE TABLE school_data.sd_bsyjsxjxx")
    void truncate();

}

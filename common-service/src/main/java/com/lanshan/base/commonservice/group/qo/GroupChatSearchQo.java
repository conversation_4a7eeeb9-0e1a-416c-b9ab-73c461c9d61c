package com.lanshan.base.commonservice.group.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 搜索群聊参数QO
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("搜索群聊参数QO")
public class GroupChatSearchQo extends PageQo implements Serializable {

    private static final long serialVersionUID = 4347055853981878610L;

    @ApiModelProperty(value = "群聊名称")
    private String name;

    @ApiModelProperty(value = "群主名称或群主学工号")
    private String owner;

    @ApiModelProperty(value = "群聊类型 1:普通群 2:班级群 3:课程群")
    private Integer type;

    @ApiModelProperty(value = "群聊状态 0:未激活 1:已激活")
    private Integer status;
}

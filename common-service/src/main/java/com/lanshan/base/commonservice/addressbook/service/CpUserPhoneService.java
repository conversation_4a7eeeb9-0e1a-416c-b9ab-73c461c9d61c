package com.lanshan.base.commonservice.addressbook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.base.commonservice.addressbook.dto.UserPhoneBindDTO;
import com.lanshan.base.commonservice.addressbook.entity.CpUserPhone;
import com.lanshan.base.commonservice.addressbook.qo.BindingUserQO;
import com.lanshan.base.commonservice.addressbook.qo.PerBindingVerifyQO;
import com.lanshan.base.commonservice.addressbook.qo.VerificationOfDocumentQO;
import com.lanshan.base.commonservice.addressbook.vo.CpUserPhoneVO;
import com.lanshan.base.commonservice.addressbook.vo.PreBindingResultVO;
import com.lanshan.base.commonservice.addressbook.vo.UserIdentityInfoVO;
import com.lanshan.base.commonservice.addressbook.vo.UserSimpleInfo;

import java.util.List;

/**
 * 用户手机号绑定表(CpUserPhone)表服务接口
 *
 * <AUTHOR>
 */
public interface CpUserPhoneService extends IService<CpUserPhone> {

    /**
     * 用户绑定手机号
     *
     * @param dto 绑定参数
     * @return 是否绑定成功
     */
    Boolean bind(UserPhoneBindDTO dto);

    /**
     * 获取手机验证码
     *
     * @param phoneNo 手机号
     * @return 是否发送成功
     */
    Object getPhoneCode(String phoneNo);

    /**
     * 校验手机验证码
     *
     * @param phoneNo 手机号
     * @param code    验证码
     * @return 是否校验成功
     */
    Boolean checkPhoneCode(String phoneNo, String code);

    /**
     * 获取用户绑定的手机号
     *
     * @param userId 用户id
     * @return 已绑定的手机号
     */
    CpUserPhoneVO getPhoneBind(String userId);

    /**
     * 更新同步状态
     *
     * @param userIdList 用户id列表
     * @param syncStatus 同步状态。0：未同步；1：已同步
     * @return Boolean
     */
    Boolean updateSyncStatus(List<String> userIdList, Integer syncStatus);

    /**
     * 确认绑定
     *
     * @return Boolean
     */
    Boolean bindConfirm();

    /**
     * 校验是否绑定
     * @param userId 用户唯一标识
     * @return Boolean
     */
    Boolean checkCanBind(String userId);

    /**
     * 获取当前用户的简要信息
     * @param userId 用户ID
     * @return UserSimpleInfo
     */
    UserSimpleInfo getUserSimpleInfo(String userId);


    /**
     * 证件核验
     *
     * @param qo 证件核验请求对象
     * @return 证件核验结果 true 成功 false 失败
     */
    Boolean verificationOfDocument(VerificationOfDocumentQO qo);

    /**
     * 获取身份列表
     *
     * @param openId 微信openId
     * @return 身份列表
     */
    List<UserIdentityInfoVO> listIdentities(String openId);

    /**
     * 更新默认身份
     *
     * @param userInfoBindId 待更新的绑定主键
     * @return true 成功 false 失败
     */
    Boolean updateDefaultIdentity(Long userInfoBindId);

    /**
     * 绑定前校验操作
     *
     * @param qo 校验对象
     * @return 校验结果
     */
    PreBindingResultVO perBindingVerify(PerBindingVerifyQO qo);

    /**
     * 绑定
     *
     * @param qo 绑定对象
     * @return 是否绑定成功
     */
    Boolean binding(BindingUserQO qo);

    /**
     * 获取客服二维码链接
     *
     * @param userType 用户类型 0：其他人员 1：教职工 2：本科生 3：研究生
     * @return 客服二维码链接
     */
    String getHelpUrl(String userType);

    /**
     * 更新身份信息
     *
     * @param openId 微信openId
     * @return 身份列表
     */
    List<UserIdentityInfoVO> updateIdentities(String openId);

    /**
     * 获取当前用户身份信息
     *
     * @return 身份信息
     */
    UserIdentityInfoVO getNewStuIdentityInfo();
}


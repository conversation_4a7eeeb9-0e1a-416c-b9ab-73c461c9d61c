package com.lanshan.base.commonservice.standardapp.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.standardapp.converter.StdSalarInfoConverter;
import com.lanshan.base.commonservice.standardapp.entity.StdSalarInfo;
import com.lanshan.base.commonservice.standardapp.service.StdSalarInfoService;
import com.lanshan.base.commonservice.standardapp.vo.StdSalarInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.Serializable;

/**
 * 工资信息表(SalarInfo)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("salarInfo")
@Api(tags = "工资信息表(SalarInfo)控制层", hidden = true)
public class StdSalarInfoController {
    /**
     * 服务对象
     */
    @Resource
    private StdSalarInfoService stdSalarInfoService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping
    public Result<IPage<StdSalarInfoVO>> selectAll(Page<StdSalarInfoVO> page, StdSalarInfoVO vo) {
        QueryWrapper<StdSalarInfo> queryWrapper = new QueryWrapper<>(StdSalarInfoConverter.INSTANCE.toEntity(vo));
        IPage<StdSalarInfo> pageData = this.stdSalarInfoService.page(page.convert(StdSalarInfoConverter.INSTANCE::toEntity), queryWrapper);
        return Result.build(pageData.convert(StdSalarInfoConverter.INSTANCE::toVO));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("{id}")
    public Result<StdSalarInfoVO> selectOne(@PathVariable Serializable id) {
        return Result.build(StdSalarInfoConverter.INSTANCE.toVO(this.stdSalarInfoService.getById(id)));
    }


    /**
     * @param month 月份
     *              通过月份查询单条数据
     * @return 单条数据
     */
    @ApiOperation("通过月份查询单条数据")
    @GetMapping("/getSalaryInfoByUserid")
    public Result<StdSalarInfoVO> getSalaryInfoByUserid(String month) {
        return Result.build(this.stdSalarInfoService.getSalaryInfoByUserid(month));
    }

    @ApiOperation("获取年终绩效")
    @GetMapping("/getNzjx")
    public void getNzjx(String year){

    }

}


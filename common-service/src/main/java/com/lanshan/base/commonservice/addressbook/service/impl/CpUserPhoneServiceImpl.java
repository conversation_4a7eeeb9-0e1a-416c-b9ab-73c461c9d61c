package com.lanshan.base.commonservice.addressbook.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.base.api.dto.common.UserIdDTO;
import com.lanshan.base.api.dto.system.SysConfigVo;
import com.lanshan.base.api.enums.ErrcodeEnum;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.enums.UserTypeEnum;
import com.lanshan.base.api.feign.user.UserFeign;
import com.lanshan.base.api.qo.user.UserGetuseridQo;
import com.lanshan.base.api.qo.user.UserSaveWithTagQo;
import com.lanshan.base.api.utils.AESUtil;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.converter.CpUserPhoneConverter;
import com.lanshan.base.commonservice.addressbook.dao.CpUserPhoneDao;
import com.lanshan.base.commonservice.addressbook.dto.UserPhoneBindDTO;
import com.lanshan.base.commonservice.addressbook.entity.*;
import com.lanshan.base.commonservice.addressbook.qo.BindingUserQO;
import com.lanshan.base.commonservice.addressbook.qo.PerBindingVerifyQO;
import com.lanshan.base.commonservice.addressbook.qo.VerificationOfDocumentQO;
import com.lanshan.base.commonservice.addressbook.service.*;
import com.lanshan.base.commonservice.addressbook.vo.CpUserPhoneVO;
import com.lanshan.base.commonservice.addressbook.vo.PreBindingResultVO;
import com.lanshan.base.commonservice.addressbook.vo.UserIdentityInfoVO;
import com.lanshan.base.commonservice.addressbook.vo.UserSimpleInfo;
import com.lanshan.base.commonservice.config.properties.AgentProperties;
import com.lanshan.base.commonservice.message.util.PhoneCodeSendFactory;
import com.lanshan.base.commonservice.system.service.ISysConfigService;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.util.set.Sets;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户手机号绑定表(CpUserPhone)表服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("cpUserPhoneService")
@RequiredArgsConstructor
@RefreshScope
public class CpUserPhoneServiceImpl extends ServiceImpl<CpUserPhoneDao, CpUserPhone> implements CpUserPhoneService {

    final private RedissonClient redissonClient;

    final private CommonService commonService;

    final private PhoneCodeSendFactory phoneCodeSendFactory;

    @Value("${user.phone.bind.error-throw}")
    private Boolean userPhoneBindErrorThrow;

    @Resource
    private UserInfoService userInfoService;

    @Resource
    private UserInfoBindService userInfoBindService;

    @Resource
    private UserInfoStatusService userInfoStatusService;

    @Resource
    private CpUserPhoneService cpUserPhoneService;

    @Resource
    private AgentProperties agentProperties;

    @Resource
    private UserFeign userFeign;

    @Resource
    private CpUserOperateBindMobileService cpUserOperateBindMobileService;

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private UserService userService;

    @Resource
    private UserInfoQrcodeService userInfoQrcodeService;

    @Resource
    private UserFaceIdentifyService userFaceIdentifyService;

    @Resource
    private NewStudentDataService newStudentDataService;

    /**
     * 用户绑定手机号
     *
     * @param dto 绑定参数
     * @return 是否绑定成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean bind(UserPhoneBindDTO dto) {

        // 校验验证码
        checkPhoneCode(dto.getPhoneNo(), dto.getCode());

        handleBind(dto.getPhoneNo(), true);
        return Boolean.TRUE;
    }

    private void handleBind(String phoneNo, Boolean checkPhoneExist) {
        // 从token中获取用户id和用户姓名
        String userId = SecurityContextHolder.getUserId();
        String userName = SecurityContextHolder.getUserName();

        // 查询用户是否已经绑定过手机号
        CpUserPhone userPhone = super.getOne(
                new LambdaQueryWrapper<>(CpUserPhone.class)
                        .eq(CpUserPhone::getUserId, userId)
                        .eq(CpUserPhone::getIsDeleted, false));
        Date now = new Date();
        if (userPhone == null || userPhone.getUserId() == null) {
            if (checkPhoneExist) {
                handlePhoneExist(phoneNo, userPhoneBindErrorThrow);
            }
            // 如果没有绑定过手机号，则新增
            userPhone = new CpUserPhone();
            userPhone.setUserId(userId);
            userPhone.setUserName(userName);
            userPhone.setPhoneNo(phoneNo);
            userPhone.setCreateTime(now);
            userPhone.setUpdateTime(now);
            super.saveOrUpdate(userPhone);
            this.useridBindMobile(userId, phoneNo);
        } else {
            // 如果已经绑定过手机号，则更新手机号，并记录之前的手机号
            if (!userPhone.getPhoneNo().equals(phoneNo)) {
                if (checkPhoneExist) {
                    handlePhoneExist(phoneNo, userPhoneBindErrorThrow);
                }
                userPhone.setUpdateTime(now);
                userPhone.setRemark(StringUtils.isBlank(userPhone.getRemark()) ? userPhone.getPhoneNo() : userPhone.getRemark().concat(",").concat(userPhone.getPhoneNo()));
                userPhone.setPhoneNo(phoneNo);
                userPhone.setSyncStatus(0);
                super.saveOrUpdate(userPhone);
                this.useridBindMobile(userId, phoneNo);
            }
        }
    }

    private void useridBindMobile(String userId, String phoneNo) {
        try {
            commonService.useridBindMobile(userId, phoneNo);
        } catch (RuntimeException e) {
            log.error("commonService.useridBindMobile接口调用异常！", e);
            throw ExceptionCodeEnum.USER_BIND_MOBILE_SERVER_ERROR.toServiceException("企业微信服务无法访问，请联系管理员进行处理！");
        }
    }

    /**
     * 获取手机验证码
     *
     * @param phoneNo 手机号
     * @return 验证码
     */
    @Override
    public Object getPhoneCode(String phoneNo) {
        // 校验发送验证码频率
        checkSendCodeLimit(phoneNo);

        // 生成验证码，并设置到redis，5分钟内有效
        String code = Integer.toString(RandomUtil.randomInt(100000, 999999));
        RMapCache<String, String> codeMap = redissonClient.getMapCache("user:phone:bind:code");
        codeMap.put(phoneNo, code, 10, TimeUnit.MINUTES);

        // 发送短信
        phoneCodeSendFactory.getSmsPhoneCodeSend().sendPhoneCode(phoneNo, code, SecurityContextHolder.getUserId());
        return true;
    }

    /**
     * 校验手机验证码
     *
     * @param phoneNo 手机号
     * @param code    验证码
     * @return 是否校验成功
     */
    @Override
    public Boolean checkPhoneCode(String phoneNo, String code) {
        RMapCache<String, String> codeMap = redissonClient.getMapCache("user:phone:bind:code");
        if (codeMap.get(phoneNo) == null || !codeMap.get(phoneNo).equals(code)) {
            throw ExceptionCodeEnum.SFT_PHONE_CODE_ERROR.toServiceException();
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean verificationOfDocument(VerificationOfDocumentQO qo) {
        //微信 openId
        String openId = SecurityContextHolder.getUserId();
        //查询用户信息，如果能查到则有身份，如果未找到则无任何身份
        List<UserInfo> userInfos = userInfoService.list(Wrappers.<UserInfo>lambdaQuery()
                        //20250610 证件核验的时候不再进行姓名校验   姓名可能存在繁体简体等问题，校验会有问题
//                .eq(UserInfo::getUserName, qo.getName())
                        .eq(UserInfo::getUserId, qo.getUserid())
                        .eq(UserInfo::getIdentityType, qo.getIdentityType())
                        .eq(UserInfo::getIdentityNumber, AESUtil.encrypt(qo.getIdentityNumber()))
        );
        if (CollUtil.isEmpty(userInfos)) {
            throw ExceptionCodeEnum.USER_BIND_MOBILE_NOT_EXIST.toServiceException();
        }
        List<UserInfoBind> userInfoBinds = new ArrayList<>();
        List<UserInfoStatus> userInfoStatuses = new ArrayList<>();
//        List<String> userIds = userInfos.stream().map(UserInfo::getUserId).collect(Collectors.toList());

        if (Objects.equals(qo.getIdentityType(), "0")) {
            //如果证件类型是0身份证  需要判断是否允许当前用户进行证件核验
            UserInfo userInfo = userInfoService.getOne(Wrappers.<UserInfo>lambdaQuery().eq(UserInfo::getUserId, qo.getUserid()));
            if (userInfo != null && !userInfo.getAllowIdentityCheck()) {
                throw ExceptionCodeEnum.SFT_NOT_ALLOW_IDENTIFIER_CHECK.toServiceException();
            }
        }

        userInfoBindService.update(Wrappers.<UserInfoBind>lambdaUpdate().set(UserInfoBind::getIsDefault, false).eq(UserInfoBind::getOpenId, openId));
        List<UserInfoBind> userInfoBindList = userInfoBindService.list(Wrappers.<UserInfoBind>lambdaQuery().eq(UserInfoBind::getOpenId, openId));
        //改 openid 下的已绑定用户身份
        List<String> boundUserIds = Optional.ofNullable(userInfoBindList).orElseGet(ArrayList::new).stream().map(UserInfoBind::getUserId).collect(Collectors.toList());
        List<UserInfoStatus> userInfoStatusList = new ArrayList<>();
        if (CollUtil.isNotEmpty(boundUserIds)) {
            userInfoStatusList = userInfoStatusService.list(Wrappers.<UserInfoStatus>lambdaQuery().in(UserInfoStatus::getUserId, boundUserIds));
        }
        Map<String, UserInfoBind> userInfoBindMap = Optional.ofNullable(userInfoBindList).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(UserInfoBind::getUserId, item -> item, (o, n) -> o));
        Map<String, UserInfoStatus> userInfoStatusMap = Optional.ofNullable(userInfoStatusList).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(UserInfoStatus::getUserId, item -> item, (o, n) -> o));

        //删除之前的绑定信息
        if (CollUtil.isNotEmpty(boundUserIds)) {
            userInfoStatusService.update(Wrappers.<UserInfoStatus>lambdaUpdate().set(UserInfoStatus::getAuthStatus, false).in(UserInfoStatus::getUserId, boundUserIds));
        }
        userInfoBindService.removeByOpenId(openId);

        for (UserInfo userInfo : userInfos) {
            String userId = userInfo.getUserId();
            UserInfoBind userInfoBind = userInfoBindMap.get(userId);
            if (Objects.isNull(userInfoBind)) {
                userInfoBind = new UserInfoBind();
            }
            userInfoBind.setOpenId(openId);
            userInfoBind.setUserId(userId);
            userInfoBind.setUserName(userInfo.getUserName());
            if (userId.equalsIgnoreCase(qo.getUserid())) {
                userInfoBind.setCertificateImg(qo.getCertificateImg());
                userInfoBind.setIsDefault(true);
                userInfoBind.setIdentityNumber(AESUtil.encrypt(qo.getIdentityNumber()));
                String desensitizedIdCard = DesensitizedUtil.idCardNum(qo.getIdentityNumber(), 5, 2);
                userInfoBind.setIdentityNumberWrapper(desensitizedIdCard);
            }
            userInfoBind.setUpdateDate(new Date());
            userInfoBinds.add(userInfoBind);
            UserInfoStatus userInfoStatus = userInfoStatusMap.get(userId);
            if (Objects.isNull(userInfoStatus)) {
                userInfoStatus = new UserInfoStatus();
                userInfoStatus.setAuthTime(new Date());
            }
            //用户状态信息
            userInfoStatus.setUserId(userId);
            userInfoStatus.setAuthStatus(true);
            userInfoStatuses.add(userInfoStatus);
        }
        //保存或更新绑定信息和状态信息
        return userInfoBindService.saveBatch(userInfoBinds) && userInfoStatusService.saveOrUpdateBatch(userInfoStatuses);
    }

    @Override
    public List<UserIdentityInfoVO> listIdentities(String openId) {
        return userInfoBindService.listIdentitiesByOpenId(openId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDefaultIdentity(Long userInfoBindId) {
        userInfoBindService.update(Wrappers.<UserInfoBind>lambdaUpdate().set(UserInfoBind::getIsDefault, false).eq(UserInfoBind::getIsDefault, true));
        return userInfoBindService.update(Wrappers.<UserInfoBind>lambdaUpdate().set(UserInfoBind::getIsDefault, true).eq(UserInfoBind::getId, userInfoBindId));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public PreBindingResultVO perBindingVerify(PerBindingVerifyQO qo) {
        //微信 openId
        String openId = SecurityContextHolder.getUserId();
        PreBindingResultVO preBindingResultVO = new PreBindingResultVO();
        String phoneNo = "";
        if (!StringUtils.isEmpty(qo.getCode())) {
            //微信手机号认证
            phoneNo = userFaceIdentifyService.getUserPhoneNumberByCodeFromRedis(qo.getCode());
            qo.setPhoneNo(phoneNo);
        } else if (!StringUtils.isEmpty(qo.getPhoneNo())) {
            //手机号短信验证码认证
            phoneNo = qo.getPhoneNo();
            checkPhoneCode(phoneNo, qo.getVerifyCode());
        } else {
            throw ExceptionCodeEnum.SFT_PARAMETER_ERROR.toServiceException();
        }
        if (StringUtils.isEmpty(phoneNo)) {
            throw ExceptionCodeEnum.SFT_GET_PHONE_ERROR.toServiceException();
        }
        //判断当前工号是否已绑定
        CpUserPhoneVO currentUserPhone = this.getPhoneBind(qo.getUserid());
        preBindingResultVO.setBindFlag(Objects.isNull(currentUserPhone.getId()));
        //校验手机号是否已绑定
        UserGetuseridQo userGetuseridQo = UserGetuseridQo.builder().mobile(phoneNo).build();
        Result<UserIdDTO> result = userFeign.userGetuserid(agentProperties.getCorpId(), agentProperties.getCommonAgentId(), userGetuseridQo);
        if (result.hasError() && ErrcodeEnum.ERROR_60111.getValue() != result.getCode() && ErrcodeEnum.ERROR_46004.getValue() != result.getCode()) {
            log.error("用户绑定手机号-查询企微用户异常, userid:{}, code:{}, msg:{}", qo.getUserid(), result.getCode(), result.getMsg());
            throw ExceptionCodeEnum.SFT_GET_USER_INFO_ERROR.toServiceException();
        }
        if (ErrcodeEnum.ERROR_60111.getValue() == result.getCode() || ErrcodeEnum.ERROR_46004.getValue() == result.getCode()) {
            preBindingResultVO.setPhoneFlag(false);
            return preBindingResultVO;
        }
        preBindingResultVO.setPhoneFlag(true);
        UserIdDTO userIdDTO = result.getResult();
        String userid = userIdDTO.getUserid();
        //如果当前工号和企微工号一致提示用户已经绑定过
        if (userid.equals(qo.getUserid())) {
            throw ExceptionCodeEnum.SFT_REPEAT_BINDING.toServiceException();
        }
        CpUserPhoneVO phoneBind = this.getPhoneBind(userid);
        Long id = phoneBind.getId();
        if (Objects.isNull(id)) {
            DateTime currentDate = DateUtil.date();
            CpUserPhone cpUserPhone = new CpUserPhone();
            cpUserPhone.setUserId(userid);
            cpUserPhone.setUserName(qo.getName());
            cpUserPhone.setPhoneNo(qo.getPhoneNo());
            cpUserPhone.setCreateTime(currentDate);
            cpUserPhone.setUpdateTime(currentDate);
            cpUserPhone.setSyncStatus(1);
            this.save(cpUserPhone);
        }
        List<UserIdentityInfoVO> userIdentityInfoVOS = listIdentities(openId);
        Map<String, UserIdentityInfoVO> userIdentityInfoVOMap = Optional.ofNullable(userIdentityInfoVOS).orElseGet(ArrayList::new).stream()
                .collect(Collectors.toMap(UserIdentityInfoVO::getUserId, item -> item, (o, n) -> o));
        boolean selfFlag = userIdentityInfoVOMap.containsKey(userid);
        preBindingResultVO.setSelfFlag(selfFlag);
        if (selfFlag) {
            preBindingResultVO.setUserid(userid);
        }
        return preBindingResultVO;
    }

    @Override
    public Boolean binding(BindingUserQO qo) {
        SysConfigVo byConfigKey = sysConfigService.getByConfigKey("user.bind.defaultDept");
        Long defaultDept = Long.valueOf(byConfigKey.getConfigValue());
        String boundUserid = qo.getBoundUserid();
        if (Objects.nonNull(boundUserid)) {
            commonService.unbindUser(boundUserid);
            CpUserPhoneVO phoneBind = getPhoneBind(boundUserid);
            if (Objects.nonNull(phoneBind.getId())) {
                this.update(Wrappers.<CpUserPhone>lambdaUpdate().set(CpUserPhone::getIsDeleted, true).eq(CpUserPhone::getId, phoneBind.getId()));
            }
            //如果账号已经在用户状态中，将用户企微开通状态修改为未开通
            userInfoStatusService.update(Wrappers.<UserInfoStatus>lambdaUpdate().set(UserInfoStatus::getOpenStatus, false)
                    .eq(UserInfoStatus::getUserId, boundUserid));
        }
        //通过userid查询绑定表（绑定表的数据都是数据中心没有手机号的数据）
        CpUserOperateBindMobile bindMobile = cpUserOperateBindMobileService.getById(qo.getUserid());
        if (Objects.isNull(bindMobile)) {
            UserSaveWithTagQo saveWithTagQo = new UserSaveWithTagQo();
            saveWithTagQo.setOperateType(1);
            saveWithTagQo.setUserid(qo.getUserid());
            saveWithTagQo.setName(qo.getName());
            saveWithTagQo.setMobile(qo.getPhoneNo());
            saveWithTagQo.setDepartment(Collections.singletonList(defaultDept));
            saveWithTagQo.setIsLeaderInDept(Collections.singletonList(0));
            saveWithTagQo.setMainDepartment(defaultDept);
            userService.saveUserWithTag(saveWithTagQo);
        } else {
            commonService.useridBindMobile(qo.getUserid(), qo.getPhoneNo());
        }
        userInfoStatusService.update(Wrappers.<UserInfoStatus>lambdaUpdate()
                .set(UserInfoStatus::getOpenStatus, true)
                .set(UserInfoStatus::getOpenTime, new Date())
                .set(UserInfoStatus::getPhoneNo, qo.getPhoneNo())
                .eq(UserInfoStatus::getUserId, qo.getUserid())
        );
        //如果当前开通用户有新生身份，且当前开通企微的不为新生身份，删除新生数据
        UserInfo userInfo = userInfoService.getById(qo.getUserid());
        if (!UserTypeEnum.NEW_STUDENT.getCodeStr().equals(userInfo.getUserType())) {
            String identityNumber = userInfo.getIdentityNumber();
            List<UserInfo> userInfoByIdCard = userInfoService.getUserInfoByIdCard(identityNumber);
            CpUserPhoneServiceImpl cpUserPhoneService = (CpUserPhoneServiceImpl) AopContext.currentProxy();
            userInfoByIdCard.stream().filter(item -> item.getUserType().equals(UserTypeEnum.NEW_STUDENT.getCodeStr())).findAny().ifPresent(item -> {
                cpUserPhoneService.removeUserInfoData(userInfo);
            });
        }
        //绑定成功修改 new_student_data 表中的开通状态
        newStudentDataService.update(Wrappers.<NewStudentData>lambdaUpdate()
                .set(NewStudentData::getOpenStatus, true)
                .eq(NewStudentData::getIdCardNum, qo.getUserid())
        );
        return true;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW, timeout = 1000)
    public void removeUserInfoData(UserInfo userInfo) {
        String userId = userInfo.getUserId();
        userInfoService.removeById(userId);
        userInfoStatusService.removeById(userId);
        userInfoBindService.update(Wrappers.<UserInfoBind>lambdaUpdate()
                .set(UserInfoBind::getIsDeleted, true)
                .eq(UserInfoBind::getUserId, userId)
        );
    }

    @Override
    public String getHelpUrl(String userType) {
        UserInfoQrcode userInfoQrcode = userInfoQrcodeService.getOne(Wrappers.<UserInfoQrcode>lambdaQuery().eq(UserInfoQrcode::getUserType, userType)
                .eq(UserInfoQrcode::getEnable, true).last("LIMIT 1"));
        if (Objects.nonNull(userInfoQrcode)) {
            return userInfoQrcode.getImg();
        }
        return "";
    }

    @Override
    public List<UserIdentityInfoVO> updateIdentities(String openId) {
        List<UserIdentityInfoVO> userIdentityInfoVOS = userInfoBindService.listIdentitiesByOpenId(openId);
        if (CollUtil.isEmpty(userIdentityInfoVOS)) {
            return Collections.emptyList();
        }
        Set<String> identityNumberSet = userIdentityInfoVOS.stream().map(UserIdentityInfoVO::getIdentityNumber).collect(Collectors.toSet());
        Map<String, UserIdentityInfoVO> userIdentityInfoVOMap = userIdentityInfoVOS.stream().collect(Collectors.toMap(UserIdentityInfoVO::getUserId, item -> item, (o, n) -> o));
        //查询用户证件下的所有用户信息
        List<UserInfo> userInfos = userInfoService.list(Wrappers.<UserInfo>lambdaQuery().in(UserInfo::getIdentityNumber, identityNumberSet));
        if (CollUtil.isEmpty(userInfos)) {
            return userIdentityInfoVOS;
        }
        Map<String, UserInfo> userInfoMap = Optional.of(userInfos).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(UserInfo::getUserId, item -> item, (o, n) -> o));
        List<String> removeUserIds = new ArrayList<>();
        Set<String> unionSet = Sets.union(userIdentityInfoVOMap.keySet(), userInfoMap.keySet());
        List<UserInfoBind> userInfoBinds = new ArrayList<>();
        List<UserInfoStatus> userInfoStatuses = new ArrayList<>();

        for (String userid : unionSet) {
            UserIdentityInfoVO userIdentityInfoVO = userIdentityInfoVOMap.get(userid);
            UserInfo userInfo = userInfoMap.get(userid);
            if (Objects.nonNull(userIdentityInfoVO) && Objects.isNull(userInfo)) {
                removeUserIds.add(userid);
                break;
            }
            //没有则表示需要新增
            if (Objects.isNull(userIdentityInfoVO) && Objects.nonNull(userInfo)) {
                UserInfoBind userInfoBind = new UserInfoBind();
                userInfoBind.setIdentityNumber(userInfo.getIdentityNumber());
                userInfoBind.setIsDefault(false);
                userInfoBind.setIsDeleted(false);
                userInfoBind.setIdentityNumberWrapper(userInfo.getIdentityNumberWrapper());
                userInfoBind.setOpenId(openId);
                userInfoBind.setUserId(userid);
                userInfoBind.setUserName(userInfo.getUserName());
                userInfoBinds.add(userInfoBind);
                UserInfoStatus userInfoStatus = new UserInfoStatus();
                //用户状态信息
                userInfoStatus.setUserId(userid);
                userInfoStatus.setAuthStatus(true);
                userInfoStatus.setAuthTime(new Date());
                userInfoStatuses.add(userInfoStatus);
            }
        }
        userInfoBindService.saveOrUpdateBatch(userInfoBinds);
        userInfoStatusService.saveOrUpdateBatch(userInfoStatuses);
        if (CollUtil.isNotEmpty(removeUserIds)) {
            userInfoBindService.remove(Wrappers.<UserInfoBind>lambdaQuery().in(UserInfoBind::getUserId, removeUserIds).eq(UserInfoBind::getOpenId, openId));
        }
        return listIdentities(openId);
    }

    /**
     * 获取用户绑定的手机号
     *
     * @param userId 用户id
     * @return 已绑定的手机号
     */
    @Override
    public CpUserPhoneVO getPhoneBind(String userId) {
        Optional<CpUserPhone> opt = super.getOneOpt(
                new LambdaQueryWrapper<>(CpUserPhone.class)
                        .eq(CpUserPhone::getUserId, userId)
                        .eq(CpUserPhone::getIsDeleted, false));
        return opt.map(CpUserPhoneConverter.INSTANCE::toVO).orElse(new CpUserPhoneVO());
    }

    /**
     * 更新同步状态
     *
     * @param userIdList 用户id列表
     * @param syncStatus 同步状态。0：未同步；1：已同步
     * @return Boolean
     */
    @Override
    public Boolean updateSyncStatus(List<String> userIdList, Integer syncStatus) {

        if (CollectionUtils.isEmpty(userIdList)) {
            return Boolean.TRUE;
        }

        LambdaUpdateWrapper<CpUserPhone> updateWrapper = new LambdaUpdateWrapper<CpUserPhone>()
                .eq(CpUserPhone::getIsDeleted, false)
                .in(CpUserPhone::getUserId, userIdList)
                .set(CpUserPhone::getSyncStatus, syncStatus);

        return super.update(updateWrapper);
    }

    /**
     * 确认绑定
     *
     * @return Boolean
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean bindConfirm() {
        RMapCache<String, String> confirmMap = redissonClient.getMapCache("user:phone:bind:confirm");
        String userId = SecurityContextHolder.getUserId();
        String phoneNo = confirmMap.remove(userId);

        if (StringUtils.isBlank(phoneNo)) {
            throw ExceptionCodeEnum.SFT_PHONE_NOT_VERIFY.toServiceException();
        }

        // 解绑这个手机号之前绑定的账号，并绑定当前用户
        handlePhoneExist(phoneNo, false);

        // 绑定新的手机号
        handleBind(phoneNo, false);
        return true;
    }

    /**
     * 校验用户账号是否可绑定
     *
     * @param userId 用户唯一标识
     * @return Boolean true 可绑定，false 不可绑定
     */
    @Override
    public Boolean checkCanBind(String userId) {
        if (Objects.isNull(commonService.userCanBind(userId))) {
            throw ExceptionCodeEnum.USER_BIND_MOBILE_SERVER_ERROR.toServiceException("该账号数据在数据中心异常，请联系管理员处理！");
        }
        return true;
    }

    /**
     * 获取当前用户的简要信息
     *
     * @param userId 用户ID
     * @return UserSimpleInfo
     */
    @Override
    public UserSimpleInfo getUserSimpleInfo(String userId) {
        CpUserOperateBindMobile cpUserOperateBindMobile = commonService.userCanBind(userId);
        if (Objects.isNull(cpUserOperateBindMobile)) {
            throw ExceptionCodeEnum.USER_BIND_MOBILE_SERVER_ERROR.toServiceException("该账号数据在数据中心异常，请联系管理员处理！");
        }
        return UserSimpleInfo.builder().userName(cpUserOperateBindMobile.getName()).userId(userId).gender(cpUserOperateBindMobile.getGender()).build();
    }

    /**
     * 校验发送验证码频率
     *
     * @param phoneNo 手机号
     */
    private void checkSendCodeLimit(String phoneNo) {
        RMapCache<String, String> limitMap = redissonClient.getMapCache("user:phone:bind:limit");
        if (limitMap.get(phoneNo) == null) {
            limitMap.put(phoneNo, "1", 1, TimeUnit.MINUTES);
        } else {
            throw ExceptionCodeEnum.SFT_GET_PHONE_CODE_ERROR.toServiceException();
        }
    }

    private void handlePhoneExist(String phoneNo, boolean userPhoneBindErrorThrow) {
        // 查询用户是否已经绑定过手机号
        CpUserPhone userPhone = super.getOne(
                new LambdaQueryWrapper<>(CpUserPhone.class)
                        .eq(CpUserPhone::getPhoneNo, phoneNo)
                        .eq(CpUserPhone::getIsDeleted, false));
        if (userPhone != null && StringUtils.isNotBlank(userPhone.getPhoneNo())) {
            if (userPhoneBindErrorThrow) {
                RMapCache<String, String> confirmMap = redissonClient.getMapCache("user:phone:bind:confirm");
                confirmMap.put(SecurityContextHolder.getUserId(), phoneNo, 10, TimeUnit.MINUTES);
                throw ExceptionCodeEnum.USER_BIND_MOBILE_EXIST.toServiceException(String.format("已绑定%s(%s)的账号！", userPhone.getUserName(), userPhone.getUserId()));
            } else {
                // 解绑这个手机号之前绑定的账号，并绑定当前用户
                userPhone.setIsDeleted(true);
                userPhone.setUpdateTime(new Date());
                super.saveOrUpdate(userPhone);
                try {
                    commonService.unbindUser(userPhone.getUserId());
                } catch (RuntimeException e) {
                    log.error("commonService.useridBindMobile接口调用异常！", e);
                    throw ExceptionCodeEnum.USER_BIND_MOBILE_SERVER_ERROR.toServiceException("企业微信服务无法访问，请联系管理员进行处理！");
                }
            }
        }
    }
}


package com.lanshan.base.commonservice.schooldata.whut.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.experimental.UtilityClass;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Http请求
 */
@UtilityClass
public final class HttpRequestUtil {
    private Logger logger = LoggerFactory.getLogger(HttpRequestUtil.class);

    public String getParamStr(Map<String, Object> paramsMap) {
        StringBuilder param = new StringBuilder();
        if (paramsMap.size() > 0) {
            for (Map.Entry<String, Object> entry : paramsMap.entrySet()) {
                param.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        return param.toString();
    }

    public <T> T get(String url, Map<String, String> headerMap, TypeReference<T> tTypeReference) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);

        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
        httpGet.setConfig(requestConfig);
        httpGet.addHeader("Content-Type", "application/json");
        if (ObjectUtil.isNotEmpty(headerMap)) {
            headerMap.forEach(httpGet::addHeader);
        }
        try {
            String execute = httpClient.execute(httpGet, new BasicResponseHandler());
            logger.info(execute);
            return JSONObject.parseObject(execute, tTypeReference);
        } catch (IOException e) {
            e.printStackTrace();
            throw new Exception();
        } finally {
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public <T> T get(String url, Map<String, String> headerMap, Map<String, Object> paramsMap, TypeReference<T> tTypeReference) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        if (url.contains("?")) {
            url += "&" + getParamStr(paramsMap);
        } else {
            url += "?" + getParamStr(paramsMap);
        }
        HttpGet httpGet = new HttpGet(url);

        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
        httpGet.setConfig(requestConfig);
        httpGet.addHeader("Content-Type", "application/json");
        if (ObjectUtil.isNotEmpty(headerMap)) {
            headerMap.forEach(httpGet::addHeader);
        }
        try {
            String execute = httpClient.execute(httpGet, new BasicResponseHandler());
            logger.info(execute);
            return JSON.parseObject(execute, tTypeReference);
        } catch (IOException e) {
            e.printStackTrace();
            throw new Exception();
        } finally {
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * POST-param
     */
    public <T> T post(String url, Map<String, String> headerMap, TypeReference<T> tTypeReference) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);

        RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(60000).setConnectTimeout(60000).build();
        httpPost.setConfig(requestConfig);
        httpPost.addHeader("Content-Type", "application/json");
        if (ObjectUtil.isNotEmpty(headerMap)) {
            headerMap.forEach(httpPost::addHeader);
        }
        try {
            String execute = httpClient.execute(httpPost, new BasicResponseHandler());
            logger.info(execute);
            return JSON.parseObject(execute, tTypeReference);
        } catch (IOException e) {
            e.printStackTrace();
            throw new Exception();
        } finally {
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * POST-param
     */
    public <T> T post(String url, Map<String, String> headerMap, Map<String, Object> paramsMap, TypeReference<T> tTypeReference) {
        try {
            URL httpUrl = new URL(url);
            HttpURLConnection con = (HttpURLConnection) httpUrl.openConnection();
            con.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            con.setRequestProperty("Charset", "utf-8");
            if (ObjectUtil.isNotEmpty(headerMap)) {
                headerMap.forEach(con::setRequestProperty);
            }
            con.setRequestMethod("POST");
            con.setDoOutput(true);
            con.setConnectTimeout(20 * 60 * 1000);

            byte[] bytes = getParamStr(paramsMap).getBytes();
            con.getOutputStream().write(bytes);
            InputStream is = con.getInputStream();
            String result = readInputStream(is);
            logger.info(result);
            return JSONObject.parseObject(result, tTypeReference);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * POST-data
     */
    public <T> T post(String url, Map<String, String> headerMap, String body, TypeReference<T> tTypeReference) {
        try {
            URL httpUrl = new URL(url);
            HttpURLConnection con = (HttpURLConnection) httpUrl.openConnection();
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("Charset", "utf-8");
            if (ObjectUtil.isNotEmpty(headerMap)) {
                headerMap.forEach(con::setRequestProperty);
            }
            con.setRequestMethod("POST");
            con.setDoOutput(true);
            con.setConnectTimeout(20 * 1000);

            DataOutputStream writer = new DataOutputStream(con.getOutputStream());
            writer.write(body.getBytes("utf-8"));
            writer.flush();
            writer.close();
            InputStream is = con.getInputStream();
            String result = readInputStream(is);
            logger.info(result);
            return JSONObject.parseObject(result, tTypeReference);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * POST-data
     */
    public <T> T post(String url, Map<String, String> headerMap, Map<String, Object> paramsMap, String body, TypeReference<T> tTypeReference) {
        try {
            if (url.contains("?")) {
                url += "&" + getParamStr(paramsMap);
            } else {
                url += "?" + getParamStr(paramsMap);
            }
            URL httpUrl = new URL(url);
            HttpURLConnection con = (HttpURLConnection) httpUrl.openConnection();
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("Charset", "utf-8");
            if (ObjectUtil.isNotEmpty(headerMap)) {
                headerMap.forEach(con::setRequestProperty);
            }
            con.setRequestMethod("POST");
            con.setDoOutput(true);
            con.setConnectTimeout(20 * 1000);

            DataOutputStream writer = new DataOutputStream(con.getOutputStream());
            writer.write(body.getBytes("utf-8"));
            writer.flush();
            writer.close();
            InputStream is = con.getInputStream();
            String result = readInputStream(is);
            logger.info(result);
            return JSONObject.parseObject(result, tTypeReference);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private String readInputStream(InputStream inStream) throws Exception {
        BufferedReader in = new BufferedReader(new InputStreamReader(inStream, "UTF-8"));
        StringBuffer buffer = new StringBuffer();
        String line = "";
        while ((line = in.readLine()) != null) {
            buffer.append(line);
        }
        return buffer.toString();
    }

    public Map<String, Object> convertJavaObjectToMap(Object obj) {
        Map<String, Object> map = new LinkedHashMap<String, Object>();
        Class<?> clazz = obj.getClass();
        //注意此处只能通过getClass().getSuperclass().getDeclaredField获取继承的父类私有属性
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object value = null;
            try {
                value = field.get(obj);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            if (value != null) {
                map.put(fieldName, value);
            }
        }
        return map;
    }
}

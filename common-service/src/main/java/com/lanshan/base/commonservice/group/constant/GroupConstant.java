package com.lanshan.base.commonservice.group.constant;

/**
 * 群聊常量
 *
 * <AUTHOR>
 */
public class GroupConstant {

    public static final String FORMATTER = "yyyy-MM-dd HH:mm:ss";

    /**
     * 建群最小成员数
     */
    public static final String GROUP_CHAT_IS_OWNER = "0";

    /**
     * 建群最小成员数
     */
    public static final Integer MIN_MEMBER_SIZE = 2;

    /**
     * 建群最大成员数
     */
    public static final Integer MAX_MEMBER_SIZE = 2000;

    /**
     * 群聊类型 1:普通群 2:班级群 3:课程群 4:部门群 5 新生群
     */
    public static final Integer GROUP_CHAT_NORMAL = 1;
    public static final Integer GROUP_CHAT_CLASS = 2;
    public static final Integer GROUP_CHAT_COURSE = 3;
    public static final Integer GROUP_CHAT_DEPT = 4;
    public static final Integer GROUP_CHAT_NEW_STU = 5;

    /**
     * 群聊操作类型 1:添加群聊成员 2:移除群聊成员 3:修改群名 4:修改群主
     */
    public static final Integer ADD_GROUP_CHAT_MEMBER = 1;

    public static final Integer REMOVE_GROUP_CHAT_MEMBER = 2;

    public static final Integer UPDATE_GROUP_CHAT_NAME = 3;

    public static final Integer UPDATE_GROUP_CHAT_OWNER = 4;

    /**
     * 群聊数据范围 0，部门；1，标签；2，个人；
     */
    public static final Integer SCOPE_DEPARTMENT = 0;
    public static final Integer SCOPE_TAG = 1;
    public static final Integer SCOPE_USER = 2;

    /**
     * 修改群聊名称操作内容模板
     */
    public static final String GROUP_CHAT_OPERATION_UPDATE_NAME = "将群名从[%s]修改为[%s]";
    /**
     * 修改群聊群主操作内容模板
     */
    public static final String GROUP_CHAT_OPERATION_UPDATE_OWNER = "将群主从[%s]修改为[%s]";

    /**
     * 添加群聊成员操作内容模板
     */
    public static final String GROUP_CHAT_OPERATION_ADD_MEMBER = "选择部门:%s,标签:%s,成员:%s";

    /**
     * 移除群聊成员操作内容模板
     */
    public static final String GROUP_CHAT_OPERATION_REMOVE_MEMBER = "将成员%s移除群聊";

    /**
     * 群聊群主类型
     */
    public static final Integer GROUP_OWNER_TYPE_DESIGNATION = 0;
    public static final Integer GROUP_OWNER_TYPE_UNSPECIFIED = 1;
}

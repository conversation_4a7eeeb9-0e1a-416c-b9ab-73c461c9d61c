package com.lanshan.base.commonservice.welcomenewstudent.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * 新生任务表(NewStudentTask)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString
public class NewStudentTask extends Model<NewStudentTask> {
    /**
     * 主键
     */
    private Long id;
    /**
     * 任务名称
     */
    private String name;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 任务描述
     */
    private String taskDesc;
    /**
     * 任务状态 true 开启，false 关闭
     */
    private Boolean taskStatus;
    /**
     * 任务内容
     */
    private String taskContent;
    /**
     * 任务链接
     */
    private String taskUrl;
    /**
     * 任务类型 0 内部应用 1 外部应用
     */
    private String taskType;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updateDate;

    /**
     * 获取主键值
     *
     * @return 主键值
     */
    @Override
    public Serializable pkVal() {
        return this.id;
    }
}


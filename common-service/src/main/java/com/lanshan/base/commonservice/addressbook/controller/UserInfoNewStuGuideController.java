package com.lanshan.base.commonservice.addressbook.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.addressbook.converter.UserInfoNewStuGuideConverter;
import com.lanshan.base.commonservice.addressbook.qo.UserInfoNewStuGuideQO;
import com.lanshan.base.commonservice.addressbook.service.UserInfoNewStuGuideService;
import com.lanshan.base.commonservice.addressbook.vo.UserInfoNewStuGuideVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 新生指南表(UserInfoNewStuGuide)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("userInfoNewStuGuide")
@Api(tags = "新生指南表(UserInfoNewStuGuide)控制层", hidden = true)
public class UserInfoNewStuGuideController {
    /**
     * 服务对象
     */
    @Resource
    private UserInfoNewStuGuideService userInfoNewStuGuideService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<UserInfoNewStuGuideVO>> page(UserInfoNewStuGuideQO qo) {
        return Result.build(userInfoNewStuGuideService.pageByParam(qo));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/selectOne/{id}")
    public Result<UserInfoNewStuGuideVO> selectOne(@PathVariable Serializable id) {
        return Result.build(UserInfoNewStuGuideConverter.INSTANCE.toVO(this.userInfoNewStuGuideService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("/insert")
    public Result<Boolean> insert(@RequestBody UserInfoNewStuGuideVO vo) {
        return Result.build(this.userInfoNewStuGuideService.save(UserInfoNewStuGuideConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody UserInfoNewStuGuideVO vo) {
        return Result.build(this.userInfoNewStuGuideService.updateById(UserInfoNewStuGuideConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @DeleteMapping("/delete")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.userInfoNewStuGuideService.removeByIds(idList));
    }
}


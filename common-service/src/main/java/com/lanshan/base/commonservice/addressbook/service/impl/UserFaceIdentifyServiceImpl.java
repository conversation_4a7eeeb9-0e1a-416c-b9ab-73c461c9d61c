package com.lanshan.base.commonservice.addressbook.service.impl;

import cn.hutool.core.util.DesensitizedUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.base.api.enums.ExceptionCodeEnum;
import com.lanshan.base.api.utils.AESUtil;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.commonservice.addressbook.converter.UserInfoBindConverter;
import com.lanshan.base.commonservice.addressbook.dto.UserIdCardDTO;
import com.lanshan.base.commonservice.addressbook.entity.UserInfo;
import com.lanshan.base.commonservice.addressbook.entity.UserInfoBind;
import com.lanshan.base.commonservice.addressbook.entity.UserInfoStatus;
import com.lanshan.base.commonservice.addressbook.service.UserFaceIdentifyService;
import com.lanshan.base.commonservice.addressbook.service.UserInfoBindService;
import com.lanshan.base.commonservice.addressbook.service.UserInfoService;
import com.lanshan.base.commonservice.addressbook.service.UserInfoStatusService;
import com.lanshan.base.commonservice.addressbook.util.WxFaceIdentifyUtil;
import com.lanshan.base.commonservice.addressbook.vo.UserInfoBindVO;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentDataService;
import lombok.RequiredArgsConstructor;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class UserFaceIdentifyServiceImpl implements UserFaceIdentifyService {

    private final WxFaceIdentifyUtil wxFaceIdentifyUtil;

    private final UserInfoBindService userInfoBindService;

    private final UserInfoService userInfoService;

    private final RedissonClient redissonClient;

    private static final String ID_CARD_INFO_BUCKET_KEY = "user:info:idcard:";

    private static final String USER_PHONE_BUCKET_KEY = "user:phone:code:";

    private final UserInfoStatusService userInfoStatusService;

    private final NewStudentDataService newStudentDataService;

    /**
     * 获取人脸核身用的userIdKey
     *
     * @return userIdKey
     */
    @Override
    public String getUserIdKey(UserIdCardDTO dto) throws WxErrorException {
        if (dto == null || StringUtils.isEmpty(dto.getName()) || StringUtils.isEmpty(dto.getIdCardNumber())) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("请先输入姓名和身份证号！");
        }
        String userIdKey = wxFaceIdentifyUtil.getUserIdKey(dto);
        if (StringUtils.isNotEmpty(userIdKey)) {
            RBucket<String> bucket = redissonClient.getBucket(getUserIdCardInfoBucketKey());
            bucket.set(dto.getIdCardNumber() + ";" + dto.getName(), 7200, TimeUnit.SECONDS);
        }
        return userIdKey;
    }

    /**
     * @param verifyResult 小程序认证结果凭据
     * @return 用户身份信息列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<UserInfoBindVO> getUserIdentifyInfo(String verifyResult) throws WxErrorException {

        // 查询人脸核身结果
        String openid = wxFaceIdentifyUtil.getVerifyResult(verifyResult);
        if (openid == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("人脸核身未通过！");
        }
        // 通过人脸核身后，写入身份认证表
        RBucket<String> bucket = redissonClient.getBucket(getUserIdCardInfoBucketKey());
        String userIdCardString = bucket.get();
        if (StringUtils.isEmpty(userIdCardString)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("获取身份信息异常！");
        }
        String[] userIdCardArray = userIdCardString.split(";");
        String idCardNum = userIdCardArray[0];
        String encryptIdCardNumber = AESUtil.encrypt(idCardNum);
        String userName = userIdCardArray[1];
        return doGetUserIdentifyInfo(encryptIdCardNumber, openid, userName, idCardNum);
    }

    /**
     * 获取用户身份信息列表
     *
     * @param encryptIdCardNumber 加密后的身份证号
     * @param openid              微信openid
     * @param userName            用户姓名
     * @param idCardNum           身份证号码
     * @return
     */
    private List<UserInfoBindVO> doGetUserIdentifyInfo(String encryptIdCardNumber, String openid, String userName, String idCardNum) {
        // 根据身份证号查询用户身份信息列表
        List<UserInfo> userInfoList = userInfoService.getUserInfoByIdCard(encryptIdCardNumber);
        List<UserInfoBind> userInfoBindList = new ArrayList<>();
        List<UserInfoStatus> userInfoStatusList = new ArrayList<>();
        if (userInfoList == null || userInfoList.isEmpty()) {
            UserInfoBind userInfoBind = new UserInfoBind();
            userInfoBind.setOpenId(openid);
            userInfoBind.setUserName(userName);
            userInfoBind.setIdentityNumber(encryptIdCardNumber);
            userInfoBind.setCreateDate(new Date());
            userInfoBind.setIsDefault(true);
            userInfoBind.setIdentityNumberWrapper(DesensitizedUtil.idCardNum(idCardNum, 5, 2));
            userInfoBindList.add(userInfoBind);
        } else {
            List<String> userids = userInfoList.stream().map(UserInfo::getUserId).collect(Collectors.toList());
            List<UserInfoStatus> userInfoStatuses = userInfoStatusService.list(Wrappers.lambdaQuery(UserInfoStatus.class).in(UserInfoStatus::getUserId, userids));
            Map<String, UserInfoStatus> userInfoStatusMap = userInfoStatuses.stream().collect(Collectors.toMap(UserInfoStatus::getUserId, item -> item, (o, n) -> o));
            for (UserInfo userInfo : userInfoList) {
                UserInfoBind userInfoBind = new UserInfoBind();
                userInfoBind.setOpenId(openid);
                userInfoBind.setUserName(userName);
                userInfoBind.setIdentityNumber(encryptIdCardNumber);
                userInfoBind.setUserId(userInfo.getUserId());
                userInfoBind.setCreateDate(new Date());
                userInfoBind.setIdentityNumberWrapper(userInfo.getIdentityNumberWrapper());
                userInfoBindList.add(userInfoBind);
                UserInfoStatus userInfoStatus = new UserInfoStatus();
                userInfoStatus.setUserId(userInfo.getUserId());
                userInfoStatus.setAuthStatus(true);
                //根据旧数据进行更新
                UserInfoStatus infoStatus = userInfoStatusMap.get(userInfo.getUserId());
                userInfoStatus.setOpenStatus(false);
                if (Objects.nonNull(infoStatus)) {
                    userInfoStatus.setOpenStatus(infoStatus.getOpenStatus());
                }
                userInfoStatus.setAuthTime(new Date());
                userInfoStatusList.add(userInfoStatus);
            }
            userInfoBindList.get(0).setIsDefault(true);
        }
        userInfoBindService.removeByOpenId(openid);
        userInfoBindService.saveBatch(userInfoBindList);
        userInfoStatusService.saveOrUpdateBatch(userInfoStatusList);
        //认证成功后，需要更新新生数据中的认证状态
        newStudentDataService.update(Wrappers.<NewStudentData>lambdaUpdate()
                .set(NewStudentData::getAuthStatus, true)
                .eq(NewStudentData::getIdCardNum, idCardNum)
        );
        return UserInfoBindConverter.INSTANCE.toVO(userInfoBindList);
    }

    /**
     * 获取用户手机号
     *
     * @param code 凭证
     * @return
     * @throws WxErrorException
     */
    @Override
    public String getUserPhoneNumber(String code) throws WxErrorException {
        String phone = wxFaceIdentifyUtil.getUserPhoneNumber(code);
        if (StringUtils.isEmpty(phone)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("获取手机号异常！");
        }
        RBucket<String> bucket = redissonClient.getBucket(USER_PHONE_BUCKET_KEY + code);
        bucket.set(phone, 7200, TimeUnit.SECONDS);
        return phone;
    }

    /**
     * 从缓存中获取用户手机号 （用户需要先调用getUserPhoneNumber获取手机号）
     * @param code
     * @return
     */
    @Override
    public String getUserPhoneNumberByCodeFromRedis(String code) {
        RBucket<String> bucket = redissonClient.getBucket(USER_PHONE_BUCKET_KEY + code);
        return bucket.get();
    }

    @Override
    public List<UserInfoBindVO> getUserIdentifyInfoNoAuth(String openId, String name, String idCardNum) {
        return doGetUserIdentifyInfo(AESUtil.encrypt(idCardNum), openId, name, idCardNum);
    }


    private String getUserIdCardInfoBucketKey() {
        String openId = SecurityContextHolder.getUserId();
        return ID_CARD_INFO_BUCKET_KEY + openId;
    }
}

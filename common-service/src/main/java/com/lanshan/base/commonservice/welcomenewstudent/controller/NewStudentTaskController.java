package com.lanshan.base.commonservice.welcomenewstudent.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.commonservice.welcomenewstudent.converter.NewStudentTaskConverter;
import com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTask;
import com.lanshan.base.commonservice.welcomenewstudent.qo.NewStudentTaskQO;
import com.lanshan.base.commonservice.welcomenewstudent.service.NewStudentTaskService;
import com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentTaskVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.List;

/**
 * 新生任务表(NewStudentTask)表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("newStudentTask")
@Api(tags = "新生任务表(NewStudentTask)控制层", hidden = true)
public class NewStudentTaskController {
    /**
     * 服务对象
     */
    @Resource
    private NewStudentTaskService newStudentTaskService;

    /**
     * 分页查询所有数据
     *
     * @param page 分页对象
     * @param vo   查询实体
     * @return 所有数据
     */
    @ApiOperation("分页查询所有数据")
    @GetMapping("/page")
    public Result<IPage<NewStudentTaskVO>> page(NewStudentTaskQO qo) {
        return Result.build(newStudentTaskService.pageByParam(qo));
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/selectOne/{id}")
    public Result<NewStudentTaskVO> selectOne(@PathVariable Serializable id) {
        return Result.build(NewStudentTaskConverter.INSTANCE.toVO(this.newStudentTaskService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param vo 实体对象
     * @return 新增结果
     */
    @ApiOperation("新增数据")
    @PostMapping("/insert")
    public Result<Boolean> insert(@RequestBody NewStudentTaskVO vo) {
        return Result.build(this.newStudentTaskService.save(NewStudentTaskConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 修改数据
     *
     * @param vo 实体对象
     * @return 修改结果
     */
    @ApiOperation("修改数据")
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody NewStudentTaskVO vo) {
        return Result.build(this.newStudentTaskService.updateById(NewStudentTaskConverter.INSTANCE.toEntity(vo)));
    }

    /**
     * 删除数据
     *
     * @param idList 主键结合
     * @return 删除结果
     */
    @ApiOperation("删除数据")
    @PostMapping("/delete")
    public Result<Boolean> delete(@RequestBody List<Long> idList) {
        return Result.build(this.newStudentTaskService.removeByIds(idList));
    }

    @ApiOperation("获取所有新生任务")
    @GetMapping("/getAllNewStudentTask")
    public Result<List<NewStudentTaskVO>> getAllNewStudentTask() {
        return Result.build(NewStudentTaskConverter.INSTANCE.toVO(this.newStudentTaskService.list(Wrappers.lambdaQuery(NewStudentTask.class)
                .orderByAsc(NewStudentTask::getSort)
                .orderByDesc(NewStudentTask::getUpdateDate)
        )));
    }

    @ApiOperation("获取已完成的新生任务")
    @GetMapping("/getAllCompletedNewStudentTask")
    public Result<List<Long>> getAllCompletedNewStudentTask() {
        return Result.build(newStudentTaskService.getAllCompletedNewStudentTask());
    }

    @ApiOperation("完成新生任务")
    @PostMapping("/completeNewStudentTask")
    public Result<Boolean> completeNewStudentTask(Long newStudentTaskId) {
        return Result.build(this.newStudentTaskService.completeNewStudentTask(newStudentTaskId));
    }

    @ApiOperation("获取最大排序")
    @GetMapping("/getMaxOrder")
    public Result<Integer> getMaxOrder() {
        return Result.build(this.newStudentTaskService.getMaxOrder());
    }
}


package com.lanshan.base.commonservice.addressbook.qo;

import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 新生指南管理查询对象
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "新生指南管理查询对象")
public class UserInfoNewStuGuideQO extends PageQo {

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "学工号")
    private String userid;

    @ApiModelProperty(value = "状态")
    private Boolean status;

}

package com.lanshan.base.commonservice.welcomenewstudent.excel.convert;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.lanshan.base.commonservice.enums.IdentityTypeEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description {}
 * @date 2025/6/7 11:45
 */
public class IdentityTypeConverter implements Converter<String> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return IdentityTypeEnum.getValueByRemark(cellData.getStringValue());
    }

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        String remarkByValue = IdentityTypeEnum.getRemarkByValue((value));
        return remarkByValue == null ? new WriteCellData<>("-") : new WriteCellData<>(remarkByValue);
    }
}

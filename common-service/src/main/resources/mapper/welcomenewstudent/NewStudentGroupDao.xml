<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentGroupDao">
    <resultMap type="com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentGroup" id="NewStudentGroupMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="chatId" column="chat_id" jdbcType="VARCHAR"/>
        <result property="groupCode" column="group_code" jdbcType="VARCHAR"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
        <result property="ownerUserid" column="owner_userid" jdbcType="VARCHAR"/>
        <result property="ownerName" column="owner_name" jdbcType="VARCHAR"/>
        <result property="joinInUser" column="join_in_user"
                typeHandler="com.lanshan.base.commonservice.typehandler.StringListTypeHandler"/>
        <result property="year" column="year" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_group(chat_id, group_name, owner_userid, owner_name, join_in_user,
                                                  group_code, group_name, year, remark, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.chatId}, #{entity.groupName}, #{entity.ownerUserid}, #{entity.ownerName}, #{entity.joinInUser},
             #{entity.groupCode}, #{entity.groupName}, #{entity.year}, #{entity.remark}, #{entity.createDate},
             #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_group(chat_id, group_name, owner_userid, owner_name, join_in_user,
                                                  group_code, group_name, year, remark, create_time, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.chatId}, #{entity.groupName}, #{entity.ownerUserid}, #{entity.ownerName}, #{entity.joinInUser},
             #{entity.groupCode}, #{entity.groupName}, #{entity.year}, #{entity.remark},
             #{entity.createDate}, #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set chat_id      = EXCLUDED.chat_id,
                                      group_name   = EXCLUDED.group_name,
                                      owner_userid = EXCLUDED.owner_userid,
                                      owner_name   = EXCLUDED.owner_name,
                                      join_in_user = EXCLUDED.join_in_user,
                                      group_code   = EXCLUDED.group_code,
                                      group_name   = EXCLUDED.group_name,
                                      year         = EXCLUDED.year,
                                      remark       = EXCLUDED.remark,
                                      create_time  = EXCLUDED.create_time,
                                      update_time  = EXCLUDED.update_time
    </insert>
</mapper>


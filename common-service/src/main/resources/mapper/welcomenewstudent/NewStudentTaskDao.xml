<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentTaskDao">
    <resultMap type="com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentTask" id="NewStudentTaskMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="INTEGER"/>
        <result property="taskDesc" column="task_desc" jdbcType="VARCHAR"/>
        <result property="taskStatus" column="task_status" jdbcType="BOOLEAN"/>
        <result property="taskContent" column="task_content" jdbcType="VARCHAR"/>
        <result property="taskUrl" column="task_url" jdbcType="VARCHAR"/>
        <result property="taskType" column="task_type" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_task(name, sort, task_desc, task_status, task_content, task_url, task_type,
                                                 create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.sort}, #{entity.taskDesc}, #{entity.taskStatus}, #{entity.taskContent},
             #{entity.taskUrl}, #{entity.taskType} #{entity.createDate}, #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.new_student_task(name, sort, task_desc, task_status, task_content, task_url, task_type,
                                                 create_date, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.sort}, #{entity.taskDesc}, #{entity.taskStatus}, #{entity.taskContent},
             #{entity.taskUrl}, #{entity.taskType} #{entity.createDate}, #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set name         = EXCLUDED.name,
                                      sort         = EXCLUDED.sort,
                                      task_desc    = EXCLUDED.task_desc,
                                      task_status  = EXCLUDED.task_status,
                                      task_content = EXCLUDED.task_content,
                                      task_url     = EXCLUDED.task_url,
                                      task_type    = EXCLUDED.task_type,
                                      create_date  = EXCLUDED.create_date,
                                      update_date  = EXCLUDED.update_date
    </insert>
</mapper>


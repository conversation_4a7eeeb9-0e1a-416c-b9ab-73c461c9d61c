<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.welcomenewstudent.dao.NewStudentDataDao">

    <resultMap type="com.lanshan.base.commonservice.welcomenewstudent.entity.NewStudentData" id="NewStudentDataMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="idCardNum" column="id_card_num" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="userid" column="userid" jdbcType="VARCHAR"/>
        <result property="phoneNo" column="phone_no" jdbcType="VARCHAR"/>
        <result property="year" column="year" jdbcType="VARCHAR"/>
        <result property="identityType" column="identity_type" jdbcType="VARCHAR"/>
        <result property="collegeCode" column="college_code" jdbcType="VARCHAR"/>
        <result property="collegeName" column="college_name" jdbcType="VARCHAR"/>
        <result property="majorCode" column="major_code" jdbcType="VARCHAR"/>
        <result property="majorName" column="major_name" jdbcType="VARCHAR"/>
        <result property="classCode" column="class_code" jdbcType="VARCHAR"/>
        <result property="className" column="class_name" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="VARCHAR"/>
        <result property="sourceOfOrigin" column="source_of_origin" jdbcType="VARCHAR"/>
        <result property="authStatus" column="auth_status" jdbcType="BOOLEAN"/>
        <result property="openStatus" column="open_status" jdbcType="BOOLEAN"/>
        <result property="checkInStatus" column="check_in_status" jdbcType="BOOLEAN"/>
        <result property="startSendStatus" column="start_send_status" jdbcType="BOOLEAN"/>
        <result property="endSendStatus" column="end_send_status" jdbcType="BOOLEAN"/>
        <result property="joinInGroupStatus" column="join_in_group_status" jdbcType="BOOLEAN"/>
        <result property="deleteFlag" column="delete_flag" jdbcType="BOOLEAN"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updaterName" column="updater_name" jdbcType="VARCHAR"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_data.new_student_data(id_card_num, name, userid, phone_no, year, identity_type,
                                                   college_code, college_name, major_code, major_name, class_code,
                                                   class_name, gender, source_of_origin, auth_status, open_status,
                                                   check_in_status, start_send_status, end_send_status,
                                                   join_in_group_status, creator, creator_name, create_date, updater,
                                                   updater_name, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.idCardNum}, #{entity.name}, #{entity.userid}, #{entity.phoneNo}, #{entity.year},
             #{entity.identityType}, #{entity.collegeCode}, #{entity.collegeName}, #{entity.majorCode},
             #{entity.majorName}, #{entity.classCode}, #{entity.className}, #{entity.gender}, #{entity.sourceOfOrigin},
             #{entity.authStatus}, #{entity.openStatus}, #{entity.checkInStatus}, #{entity.startSendStatus},
             #{entity.endSendStatus}, #{entity.joinInGroupStatus}, #{entity.creator}, #{entity.creatorName},
             #{entity.createDate}, #{entity.updater}, #{entity.updaterName}, #{entity.updateDate})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into standard_data.new_student_data(id_card_num, name, userid, phone_no, year, identity_type,
                                                   college_code, college_name, major_code, major_name, class_code,
                                                   class_name, gender, source_of_origin, auth_status, open_status,
                                                   check_in_status, start_send_status, end_send_status,
                                                   join_in_group_status, creator, creator_name, create_date, updater,
                                                   updater_name, update_date)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.idCardNum}, #{entity.name}, #{entity.userid}, #{entity.phoneNo}, #{entity.year},
             #{entity.identityType}, #{entity.collegeCode}, #{entity.collegeName}, #{entity.majorCode},
             #{entity.majorName}, #{entity.classCode}, #{entity.className}, #{entity.gender}, #{entity.sourceOfOrigin},
             #{entity.authStatus}, #{entity.openStatus}, #{entity.checkInStatus}, #{entity.startSendStatus},
             #{entity.endSendStatus}, #{entity.joinInGroupStatus}, #{entity.creator}, #{entity.creatorName},
             #{entity.createDate}, #{entity.updater}, #{entity.updaterName}, #{entity.updateDate})
        </foreach>
        ON CONFLICT(id) DO update set id_card_num          = EXCLUDED.id_card_num,
                                      name                 = EXCLUDED.name,
                                      userid               = EXCLUDED.userid,
                                      phone_no             = EXCLUDED.phone_no,
                                      year                 = EXCLUDED.year,
                                      identity_type        = EXCLUDED.identity_type,
                                      college_code         = EXCLUDED.college_code,
                                      college_name         = EXCLUDED.college_name,
                                      major_code           = EXCLUDED.major_code,
                                      major_name           = EXCLUDED.major_name,
                                      class_code           = EXCLUDED.class_code,
                                      class_name           = EXCLUDED.class_name,
                                      gender               = EXCLUDED.gender,
                                      source_of_origin     = EXCLUDED.source_of_origin,
                                      auth_status          = EXCLUDED.auth_status,
                                      open_status          = EXCLUDED.open_status,
                                      check_in_status      = EXCLUDED.check_in_status,
                                      start_send_status    = EXCLUDED.start_send_status,
                                      end_send_status      = EXCLUDED.end_send_status,
                                      join_in_group_status = EXCLUDED.join_in_group_status,
                                      creator              = EXCLUDED.creator,
                                      creator_name         = EXCLUDED.creator_name,
                                      create_date          = EXCLUDED.create_date,
                                      updater              = EXCLUDED.updater,
                                      updater_name         = EXCLUDED.updater_name,
                                      update_date          = EXCLUDED.update_date
    </insert>

    <select id="getNewStudentStat" resultType="com.lanshan.base.commonservice.welcomenewstudent.vo.NewStudentStatVO">
        select college_code                                            AS collegeCode,
               college_name                                            AS collegeName,
               count(id_card_num)                                      as total,
               sum(case when auth_status = true then 1 else 0 end)     as authTotal,
               sum(case when open_status = true then 1 else 0 end)     as openTotal,
               sum(case when check_in_status = true then 1 else 0 end) as checkInTotal
        from standard_data.new_student_data
        where year = #{year}
        group by college_code, college_name
    </select>
</mapper>


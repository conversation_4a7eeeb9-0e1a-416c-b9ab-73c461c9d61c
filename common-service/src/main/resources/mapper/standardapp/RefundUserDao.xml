<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standardapp.dao.RefundUserDao">
    <resultMap id="RefundUserVOMap" type="com.lanshan.base.commonservice.standardapp.vo.RefundUserVO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="year" column="year" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
        <result property="bankNumber" column="bank_number" jdbcType="VARCHAR"/>
        <result property="bankUserName" column="bank_user_name" jdbcType="VARCHAR"/>
        <result property="donate" column="donate" jdbcType="BOOLEAN"/>
        <result property="balance" column="balance" jdbcType="VARCHAR"/>
        <result property="balanceCard" column="balance_card" jdbcType="VARCHAR"/>
        <result property="userName" column="name" jdbcType="VARCHAR"/>
        <result property="college" column="yxmc" jdbcType="VARCHAR"/>
        <result property="grade" column="sznj" jdbcType="VARCHAR"/>
        <result property="className" column="bjmc" jdbcType="VARCHAR"/>
        <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
    </resultMap>
    
    <select id="getRefundUserList" resultMap="RefundUserVOMap" parameterType="com.lanshan.base.commonservice.standardapp.qo.RefundUserQO">
        SELECT
        ru.id,
        ru.year,
        ru.user_id,
        ru.bank_name,
        ru.bank_number,
        ru.bank_user_name,
        ru.donate,
        ru.balance,
        ru.balance_card,
        ui.name,
        ui.college_name as yxmc,
        ui.grade as sznj,
        ui.class_name as bjmc,
        cp.avatar,
        cp.user_type
        FROM
        refund_user ru
        LEFT JOIN
        standard_app.std_student_info ui ON ru.user_id = ui.user_id
        LEFT JOIN
        addressbook.cp_user cp ON ru.user_id = cp.userid
        <where>
            <if test="qo.id != null">
                AND ru.id = #{qo.id}
            </if>
            <if test="qo.year != null">
                AND ru.year = #{qo.year}
            </if>
            <if test="qo.userId != null and qo.userId != ''">
                AND ru.user_id LIKE CONCAT('%', #{qo.userId}::VARCHAR, '%')
            </if>
            <if test="qo.donate != null">
                AND ru.donate = #{qo.donate}
            </if>
            <if test="qo.userName != null and qo.userName != ''">
                AND ui.name LIKE CONCAT('%', #{qo.userName}::VARCHAR, '%')
            </if>
            <if test="qo.college != null and qo.college != ''">
                AND ui.college_name LIKE CONCAT('%', #{qo.college}::VARCHAR, '%')
            </if>
            <if test="qo.userType != null and qo.userType != ''">
                AND cp.user_type = #{qo.userType}
            </if>
            <if test="qo.bankBind != null">
                <if test="qo.bankBind == true">
                    AND ru.bank_number IS NOT NULL AND ru.bank_number != ''
                </if>
                <if test="qo.bankBind == false">
                    AND (ru.bank_number IS NULL OR ru.bank_number = '')
                </if>
            </if>
        </where>
        ORDER BY ui.college_name, ui.class_name, ru.user_id
    </select>

    <!-- 获取学院列表 -->
    <select id="getCollegeList" resultType="java.lang.String">
        SELECT DISTINCT yxmc
        FROM standard_data.std_user
        WHERE yxmc IS NOT NULL AND yxmc != ''
        ORDER BY yxmc
    </select>
    
</mapper>
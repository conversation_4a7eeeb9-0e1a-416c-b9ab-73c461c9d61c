<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.lanshan.base.commonservice.schooldata.hbou.dao.HbouQywxBzksDao">

    <!-- 批量插入 -->
    <insert id="insertBatch">
        INSERT INTO school_data.hbou_qywx_bzks (
        xm, xbmc, xh, xsdqzt, rxny,
        nj, zymc, zybm, bjmc, bjbm,
        xymc, xybh, xqmc, xqbm, xslb
        ) VALUES
        <foreach collection="entities" item="item" separator=",">
            (
            #{item.xm}, #{item.xbmc}, #{item.xh}, #{item.xsdqzt}, #{item.rxny},
            #{item.nj}, #{item.zymc}, #{item.zybm}, #{item.bjmc}, #{item.bjbm},
            #{item.xymc}, #{item.xybh}, #{item.xqmc}, #{item.xqbm}, #{item.xslb}
            )
        </foreach>
    </insert>

</mapper>

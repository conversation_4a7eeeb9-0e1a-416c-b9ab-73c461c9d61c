<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.schooldata.northwestpolitics.dao.SdBsyjsxjxxMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.base.commonservice.schooldata.northwestpolitics.po.SdBsyjsxjxx">
                <result column="xh" property="xh" />
                <result column="zxlx" property="zxlx" />
                <result column="sfzx" property="sfzx" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                xh,
                zxlx,
                sfzx
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" >
        INSERT INTO school_data.sd_bsyjsxjxx (
            xh,
            zxlx,
            sfzx
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.xh},
                #{entity.zxlx},
                #{entity.sfzx}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" >
        INSERT INTO school_data.sd_bsyjsxjxx (
            xh,
            zxlx,
            sfzx
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.xh},
                #{entity.zxlx},
                #{entity.sfzx}
            )
        </foreach>
        ON CONFLICT (xh) DO UPDATE SET
                xh = EXCLUDED.xh,
                zxlx = EXCLUDED.zxlx,
                sfzx = EXCLUDED.sfzx
    </insert>


</mapper>

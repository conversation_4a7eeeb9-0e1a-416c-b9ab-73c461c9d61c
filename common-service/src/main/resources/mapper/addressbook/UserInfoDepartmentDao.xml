<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.addressbook.dao.UserInfoDepartmentDao">
    <resultMap type="com.lanshan.base.commonservice.addressbook.entity.UserInfoDepartment" id="UserInfoDepartmentMap">
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="parentCode" column="parent_code" jdbcType="VARCHAR"/>
        <result property="departmentId" column="department_id" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="code" useGeneratedKeys="true">
        insert into addressbook.user_info_department(name, parent_code, department_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name} , #{entity.parentCode} , #{entity.departmentId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="code" useGeneratedKeys="true">
        insert into addressbook.user_info_department(name, parent_code, department_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.name}, #{entity.parentCode}, #{entity.departmentId})
        </foreach>
        ON CONFLICT(id) DO update set
        name = EXCLUDED.name , parent_code = EXCLUDED.parent_code , department_id = EXCLUDED.department_id
    </insert>
    <select id="selectByName" resultMap="UserInfoDepartmentMap">
        select * from addressbook.user_info_department where name = #{deptName}
            limit 1
    </select>
</mapper>


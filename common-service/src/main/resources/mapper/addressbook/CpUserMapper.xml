<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.addressbook.mapper.CpUserMapper">
    <resultMap type="com.lanshan.base.commonservice.addressbook.entity.CpUser" id="UserMap">
        <result property="userid" column="userid"/>
        <result property="name" column="name"/>
        <result property="department" column="department"
                typeHandler="com.lanshan.base.starter.mybatis.ListIntegerToListLongTypeHandler"/>
        <result property="order" column="order"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="position" column="position"/>
        <result property="mobile" column="mobile"/>
        <result property="gender" column="gender"/>
        <result property="email" column="email"/>
        <result property="bizMail" column="biz_mail"/>
        <result property="isLeaderInDept" column="is_leader_in_dept"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="directLeader" column="direct_leader"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="avatar" column="avatar"/>
        <result property="thumbAvatar" column="thumb_avatar"/>
        <result property="telephone" column="telephone"/>
        <result property="alias" column="alias"/>
        <result property="address" column="address"/>
        <result property="openUserid" column="open_userid"/>
        <result property="mainDepartment" column="main_department"/>
        <result property="status" column="status"/>
        <result property="qrCode" column="qr_code"/>
        <result property="extattr" column="extattr"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="externalPosition" column="external_position"/>
        <result property="externalProfile" column="external_profile"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="userType" column="user_type"/>
    </resultMap>

    <sql id="User_Column_List">
        cp_user.userid
             , cp_user.name
             , cp_user.department
             , cp_user."order"
             , cp_user.position
             , cp_user.mobile
             , cp_user.gender
             , cp_user.email
             , cp_user.biz_mail
             , cp_user.is_leader_in_dept
             , cp_user.direct_leader
             , cp_user.avatar
             , cp_user.thumb_avatar
             , cp_user.telephone
             , cp_user.alias
             , cp_user.address
             , cp_user.open_userid
             , cp_user.main_department
             , cp_user.status
             , cp_user.qr_code
             , cp_user.extattr
             , cp_user.external_position
             , cp_user.external_profile
             , cp_user.user_type
    </sql>

    <select id="listUsersByDepartmentid" resultMap="UserMap">
        SELECT
        <include refid="User_Column_List"/>
        FROM cp_user
        INNER JOIN cp_user_department_relation ON cp_user.userid = cp_user_department_relation.userid
        WHERE cp_user_department_relation.departmentId = #{department_id}
    </select>

    <select id="listAllUsersByDepartmentid" resultMap="UserMap">
        SELECT
        <include refid="User_Column_List"/>
        FROM cp_user
        INNER JOIN cp_user_department_relation ON cp_user.userid = cp_user_department_relation.userid
        INNER JOIN cp_department ON cp_department.id = cp_user_department_relation.departmentId
        WHERE cp_department.id_path LIKE CONCAT(#{idPath}::text,'%')
    </select>

    <select id="pageUserInfoVoCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        cp_user
        <where>
            <if test="param.departmentId != null">
                <if test="param.fetchChild == 0">
                    AND EXISTS(SELECT 1 FROM cp_user_department_relation udr WHERE udr.userid = cp_user.userid AND
                    udr.departmentId = #{param.departmentId})
                </if>
                <if test="param.fetchChild == 1">
                    AND EXISTS(SELECT 1
                    FROM cp_user_department_relation udr,
                    cp_department d
                    WHERE udr.userid = cp_user.userid
                    AND udr.departmentid = d.id
                    AND d.id_path LIKE CONCAT(#{param.departmentIdPath}::text,'%'))
                </if>
            </if>
            <if test="param.tagid != null">
                AND EXISTS(SELECT 1 FROM cp_user_tag_relation utr WHERE utr.userid = cp_user.userid AND utr.tagid =
                #{param.tagid})
            </if>
            <if test="param.useridForExact != null and param.useridForExact != ''">
                and cp_user.userid = #{param.useridForExact}
            </if>
            <if test="param.userid != null and param.userid != ''">
                and cp_user.userid like concat('%',#{param.userid}::text,'%')
            </if>
            <if test="param.name != null and param.name != ''">
                and cp_user.name like concat('%',#{param.name}::text,'%')
            </if>
            <if test="param.mobile != null and param.mobile != ''">
                and cp_user.mobile like concat('%',#{param.mobile}::text,'%')
            </if>
            <if test="param.position != null and param.position != ''">
                and cp_user.position like concat('%',#{param.position}::text,'%')
            </if>
            <if test="param.status != null and param.status != ''">
                and cp_user.status = #{param.status}
            </if>
        </where>
    </select>

    <select id="pageUserInfoVo" resultMap="UserMap">
        SELECT
        <include refid="User_Column_List"/>
        FROM addressbook.cp_user
        <where>
            <if test="param.departmentId != null">
                <if test="param.fetchChild == 0">
                    AND EXISTS(SELECT 1
                               FROM addressbook.cp_user_department_relation udr
                               WHERE udr.userid = cp_user.userid
                                 AND udr.departmentId = #{param.departmentId})
                </if>
                <if test="param.fetchChild == 1">
                    AND EXISTS(SELECT 1
                               FROM addressbook.cp_user_department_relation udr,
                                    addressbook.cp_department d
                               WHERE udr.userid = cp_user.userid
                                 AND udr.departmentid = d.id
                                 AND d.id_path LIKE CONCAT(#{param.departmentIdPath}::text, '%'))
                </if>
            </if>
            <if test="param.tagid != null">
                AND EXISTS(SELECT 1
                           FROM addressbook.cp_user_tag_relation utr
                           WHERE utr.userid = cp_user.userid
                             AND utr.tagid = #{param.tagid})
            </if>
            <if test="param.useridForExact != null and param.useridForExact != ''">
                and cp_user.userid = #{param.useridForExact}
            </if>
            <if test="param.userid != null and param.userid != ''">
                and cp_user.userid like concat('%', #{param.userid}::text, '%')
            </if>
            <if test="param.name != null and param.name != ''">
                and cp_user.name like concat('%', #{param.name}::text, '%')
            </if>
            <if test="param.mobile != null and param.mobile != ''">
                and cp_user.mobile like concat('%', #{param.mobile}::text, '%')
            </if>
            <if test="param.position != null and param.position != ''">
                and cp_user.position like concat('%', #{param.position}::text, '%')
            </if>
            <if test="param.status != null and param.status != ''">
                and cp_user.status = #{param.status}
            </if>
            <if test="param.useridList != null and param.useridList.size > 0">
                and cp_user.userid in
                <foreach collection="param.useridList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.status == null and param.excludeStatusList != null and param.excludeStatusList.size > 0">
                and cp_user.status not in
                <foreach collection="param.excludeStatusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.deptIdList != null and param.deptIdList.size > 0">
                and EXISTS(SELECT 1
                           FROM addressbook.cp_user_department_relation udr WHERE udr.userid = cp_user.userid
                                                                              AND udr.departmentId in
                <foreach collection="param.deptIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="param.hasMobile != null and param.hasMobile != ''">
                <choose>
                    <when test="param.hasMobile == 1">
                        AND cp_user.mobile IS NOT NULL
                    </when>
                    <when test="param.hasMobile == 0">
                        AND cp_user.mobile IS NULL
                    </when>
                </choose>
            </if>
        </where>
        <choose>
            <when test="param.userid != null and param.userid != ''">
                ORDER BY cp_user.userid
            </when>
            <when test="param.name != null and param.name != ''">
                ORDER BY convert_to(cp_user.name, 'GB18030')
            </when>
            <when test="param.mobile != null and param.mobile != ''">
                ORDER BY cp_user.mobile
            </when>
            <otherwise>
                ORDER BY cp_user."order" -> 0 DESC, cp_user.main_department, convert_to(cp_user.name, 'GB18030')
            </otherwise>
        </choose>
    </select>

    <select id="listAllUsersByDepartmentids" resultType="com.lanshan.base.commonservice.addressbook.entity.CpUser">
        SELECT
        <include refid="User_Column_List"/>
        FROM cp_user
        INNER JOIN cp_user_department_relation ON cp_user.userid = cp_user_department_relation.userid
        INNER JOIN cp_department ON cp_department.id = cp_user_department_relation.departmentId
        WHERE 1=1
        AND cp_user.status in (1,2,4)
        <if test="pathList != null">
            and
            <foreach collection="pathList" item="idPath" open="(" close=")" separator=",">
                cp_department.id_path LIKE #{idPath} || '%'
            </foreach>
        </if>
    </select>

    <select id="selectByUserId" resultMap="UserMap">
        select * from addressbook.cp_user where userid=#{staffId}
    </select>
    <select id="selectByAccount" resultType="com.lanshan.base.commonservice.addressbook.entity.CpUser">
        select * from addressbook.cp_user where 1=1
        <if test="staffNoList != null and staffNoList.size() > 0">
            and userid in
            <foreach collection="staffNoList" item="staffNo" open="(" close=")" separator=",">
                #{staffNo}
            </foreach>
        </if>
    </select>


    <select id="getActiveUsers" resultType="com.lanshan.base.api.vo.user.UserInfoPartVO">
        select
            u.userid as userId,u.name,u.main_department as deptId,u.gender,d.name as deptName
        from addressbook.cp_user u
        inner join addressbook.cp_department d on d.id = u.main_department
        where u.status = 1 and u.user_type > 1
    </select>
    <select id="getActiveNewStudent" resultMap="UserMap">
        select * from cp_user where status =1 and user_type = 4
    </select>


    <select id="listUsersByDeptIds" resultType="com.lanshan.base.api.vo.user.UserInfoPartVO">
        SELECT
            c.userid as userId,
            c.name,
            c.gender,
            r.departmentid as deptId,
        cd.name as deptName
        FROM addressbook.cp_user_department_relation r
                 INNER JOIN  addressbook.cp_user c ON c.userid = r.userid
        inner join  addressbook.cp_department cd on cd.id = r.departmentid
        WHERE c.status = 1 and r.departmentId in
        <foreach collection="deptIds" item="deptId" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
    </select>
</mapper>


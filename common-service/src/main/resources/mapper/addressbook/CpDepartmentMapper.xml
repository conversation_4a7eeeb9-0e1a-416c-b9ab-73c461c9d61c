<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.addressbook.mapper.CpDepartmentMapper">
    <resultMap type="com.lanshan.base.commonservice.addressbook.entity.CpDepartment" id="DepartmentMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="nameEn" column="name_en"/>
        <result property="parentid" column="parentid"/>
        <result property="order" column="order"/>
        <result property="path" column="path"/>
        <result property="idPath" column="id_path"/>
        <result property="departmentLeader" column="department_leader"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="userCount" column="user_count"/>
        <result property="userActiveCount" column="user_active_count"/>
    </resultMap>

    <sql id="Department_Column_List">
        dept.id,
        dept.name,
        dept.name_en,
        dept.parentid,
        dept.order,
        dept.path,
        dept.id_path,
        dept.department_leader,
        dept.user_count,
        dept.user_active_count
    </sql>



    <select id="queryNewDept" resultMap="DepartmentMap">
        SELECT id, name
        FROM cp_department
        WHERE name = CONCAT((SELECT name FROM cp_department WHERE id = 1)::text, '新组织架构');
    </select>

    <select id="pageDeptByTag" resultMap="DepartmentMap">
        SELECT
        <include refid="Department_Column_List"/>
        FROM cp_department dept, cp_department_tag_relation dtr
        WHERE dept.id = dtr.departmentid
        AND dtr.tagid = #{param.tagid}
        <if test="param.name != null and param.name != ''">
            AND dept.name LIKE CONCAT('%', #{param.name}::text, '%')
        </if>
        <if test="param.parentid != null">
            AND dept.parentid = #{param.parentid}
        </if>
    </select>

    <select id="listDeptAndChildrenByIds" resultType="com.lanshan.base.api.vo.user.DepartmentVo">
        with dept_list AS (select *
        from addressbook.cp_department
        where id in <foreach item="item" collection="ids" open="(" separator="," close=")">
        #{item}
    </foreach>)

        select acd.*
        from addressbook.cp_department acd,
        dept_list dl
        where acd.id = dl.id
        or acd.id_path like dl.id_path || '%';
    </select>

    <select id="selectByName" resultMap="DepartmentMap">
        select * from addressbook.cp_department where name = #{departName}
    </select>
    <select id="selectByPath" resultMap="DepartmentMap">
        select * from addressbook.cp_department where path like concat('%',#{path}::text,'%')
    </select>

    <select id="getDeptByIds" resultType="com.lanshan.base.api.vo.user.DepartmentPartVO">
        SELECT
            d.id  ,
            d.name  AS name,
            d."path" ,
            COUNT(DISTINCT cu.userid)  AS user_count,
            COUNT(DISTINCT CASE WHEN cu.gender  = '1' THEN cu.userid  END) AS man_count,
            COUNT(DISTINCT CASE WHEN cu.gender  = '2' THEN cu.userid  END) AS woman_count
        FROM
            addressbook.cp_department  d
                LEFT JOIN addressbook.cp_user_department_relation  cr ON d.id  = cr.departmentid
        LEFT JOIN addressbook.cp_user  cu ON cr.userid  = cu.userid  and cu.status= 1
        WHERE
            d.id  IN
            <foreach item="item" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        GROUP BY
            d.id,  d.name;
    </select>
    <select id="getDeptUserCount" resultType="java.lang.Integer">
        SELECT count(DISTINCT t1.userid) as user_count
        from addressbook.cp_user_department_relation t1
            left join addressbook.cp_user t2 on t1.userid = t2.userid
            left join addressbook.cp_department t3  on t1.departmentid = t3."id"
        where t3.id_path like concat(#{idPath}::text, '%')
    </select>

    <select id="getDeptActiveUserCount" resultType="java.lang.Integer">
        SELECT count(DISTINCT t1.userid) as user_count
        from addressbook.cp_user_department_relation t1
            left join addressbook.cp_user t2 on t1.userid = t2.userid
            left join addressbook.cp_department t3  on t1.departmentid = t3."id"
        where t2.status = 1 and t3.id_path like concat(#{idPath}::text, '%')
    </select>

    <update id="updateBatchDeptUserAndActiveCount">
        INSERT INTO addressbook.cp_department (id, user_count, user_active_count) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
            #{entity.id},
            #{entity.userCount},
            #{entity.userActiveCount}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
        user_count = EXCLUDED.user_count,
        user_active_count = EXCLUDED.user_active_count
    </update>

</mapper>


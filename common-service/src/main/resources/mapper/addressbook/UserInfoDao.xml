<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.addressbook.dao.UserInfoDao">
    <resultMap type="com.lanshan.base.commonservice.addressbook.entity.UserInfo" id="UserInfoMap">
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="identityNumber" column="identity_number" jdbcType="VARCHAR"/>
        <result property="identityType" column="identity_type" jdbcType="VARCHAR"/>
        <result property="phoneNo" column="phone_no" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
        <result property="deptCode" column="dept_code" jdbcType="VARCHAR"/>
        <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="identityNumberWrapper" column="identity_number_wrapper" jdbcType="VARCHAR"/>
        <result property="encrypted" column="encrypted" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into addressbook.user_info(user_name, identity_number, identity_type, phone_no, user_type, dept_code,
        dept_name, create_date, update_date, identity_number_wrapper, encrypted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userName} , #{entity.identityNumber} , #{entity.identityType} , #{entity.phoneNo} ,
            #{entity.userType} , #{entity.deptCode} , #{entity.deptName} , #{entity.createDate} , #{entity.updateDate} ,
            #{entity.identityNumberWrapper} , #{entity.encrypted})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="userId" useGeneratedKeys="true">
        insert into addressbook.user_info(user_name, identity_number, identity_type, phone_no, user_type, dept_code,
        dept_name, create_date, update_date, identity_number_wrapper, encrypted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.userName}, #{entity.identityNumber}, #{entity.identityType}, #{entity.phoneNo},
            #{entity.userType}, #{entity.deptCode}, #{entity.deptName}, #{entity.createDate}, #{entity.updateDate},
            #{entity.identityNumberWrapper}, #{entity.encrypted})
        </foreach>
        ON CONFLICT(id) DO update set
        user_name = EXCLUDED.user_name , identity_number = EXCLUDED.identity_number , identity_type =
        EXCLUDED.identity_type , phone_no = EXCLUDED.phone_no , user_type = EXCLUDED.user_type , dept_code =
        EXCLUDED.dept_code , dept_name = EXCLUDED.dept_name , create_date = EXCLUDED.create_date , update_date =
        EXCLUDED.update_date , identity_number_wrapper = EXCLUDED.identity_number_wrapper , encrypted =
        EXCLUDED.encrypted
    </insert>
    <select id="selectByUserId" resultMap="UserInfoMap">
        select * from addressbook.user_info where user_id = #{userId}  limit 1
    </select>
</mapper>


<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.addressbook.dao.UserInfoBindDao">
    <resultMap type="com.lanshan.base.commonservice.addressbook.entity.UserInfoBind" id="UserInfoBindMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="openId" column="open_id" jdbcType="VARCHAR"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="identityNumber" column="identity_number" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="certificateImg" column="certificate_img" jdbcType="VARCHAR"/>
        <result property="isDefault" column="is_default" jdbcType="BOOLEAN"/>
        <result property="isDeleted" column="is_deleted" jdbcType="BOOLEAN"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="identityNumberWrapper" column="identity_number_wrapper" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.user_info_bind(open_id, user_name, identity_number, user_id, certificate_img,
        is_default, is_deleted, create_date, update_date, identity_number_wrapper)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.openId} , #{entity.userName} , #{entity.identityNumber} , #{entity.userId} ,
            #{entity.certificateImg} , #{entity.isDefault} , #{entity.isDeleted} , #{entity.createDate} ,
            #{entity.updateDate} , #{entity.identityNumberWrapper})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into addressbook.user_info_bind(open_id, user_name, identity_number, user_id, certificate_img,
        is_default, is_deleted, create_date, update_date, identity_number_wrapper)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.openId}, #{entity.userName}, #{entity.identityNumber}, #{entity.userId}, #{entity.certificateImg},
            #{entity.isDefault}, #{entity.isDeleted}, #{entity.createDate}, #{entity.updateDate},
            #{entity.identityNumberWrapper})
        </foreach>
        ON CONFLICT(id) DO update set
        open_id = EXCLUDED.open_id , user_name = EXCLUDED.user_name , identity_number = EXCLUDED.identity_number ,
        user_id = EXCLUDED.user_id , certificate_img = EXCLUDED.certificate_img , is_default = EXCLUDED.is_default ,
        is_deleted = EXCLUDED.is_deleted , create_date = EXCLUDED.create_date , update_date = EXCLUDED.update_date ,
        identity_number_wrapper = EXCLUDED.identity_number_wrapper
    </insert>

    <select id="listIdentitiesByOpenId" resultType="com.lanshan.base.commonservice.addressbook.vo.UserIdentityInfoVO">
        SELECT user_info_bind.id                      AS userInfoBindId,
               user_info_bind.open_id                 AS openId,
               user_info_bind.user_name               AS userName,
               user_info_bind.identity_number         AS identityNumber,
               user_info_bind.user_id                 AS userId,
               user_info_bind.certificate_img         AS certificateImg,
               user_info_bind.is_default              AS isDefault,
               user_info.dept_code                    AS deptCode,
               user_info.dept_name                    AS deptName,
               user_info.user_type                    AS userType,
               user_info_status.auth_status           AS authStatus,
               user_info_status.open_status           AS openStatus,
               user_info_status.phone_no              AS phoneNo,
               user_info_bind.identity_number_wrapper AS identityNumberWrapper
        FROM addressbook.user_info_bind
                 LEFT JOIN addressbook.user_info user_info ON user_info_bind.user_id = user_info.user_id
                 LEFT JOIN addressbook.user_info_status ON user_info_bind.user_id = user_info_status.user_id
        WHERE open_id = #{openId}
          AND is_deleted = false
    </select>

    <select id="getNewStuIdentityInfo" resultType="com.lanshan.base.commonservice.addressbook.vo.UserIdentityInfoVO">
        SELECT user_info_bind.id                      AS userInfoBindId,
               user_info_bind.open_id                 AS openId,
               user_info_bind.user_name               AS userName,
               user_info_bind.identity_number         AS identityNumber,
               user_info_bind.user_id                 AS userId,
               user_info_bind.certificate_img         AS certificateImg,
               user_info_bind.is_default              AS isDefault,
               user_info.dept_code                    AS deptCode,
               user_info.dept_name                    AS deptName,
               user_info.user_type                    AS userType,
               user_info_status.auth_status           AS authStatus,
               user_info_status.open_status           AS openStatus,
               user_info_status.phone_no              AS phoneNo,
               user_info_bind.identity_number_wrapper AS identityNumberWrapper,
               new_student_data.college_name          AS collegeName,
               new_student_data.major_name            AS majorName
        FROM addressbook.user_info_bind
                 LEFT JOIN addressbook.user_info user_info ON user_info_bind.user_id = user_info.user_id
                 LEFT JOIN addressbook.user_info_status ON user_info_bind.user_id = user_info_status.user_id
                 LEFT JOIN standard_data.new_student_data ON user_info_bind.user_id = CASE
                                                                                          WHEN new_student_data.userid is null
                                                                                              THEN new_student_data.userid
                                                                                          ELSE new_student_data.id_card_num END
        WHERE user_info_bind.user_id = #{currentUserId}
          AND is_default = true
          AND is_deleted = false
    </select>
</mapper>


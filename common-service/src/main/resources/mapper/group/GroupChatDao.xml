<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.group.dao.GroupChatDao">

    <resultMap type="com.lanshan.base.commonservice.group.entity.GroupChat" id="GroupChatMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="chatid" column="chatid" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="owner" column="owner" jdbcType="VARCHAR"/>
        <result property="ownerName" column="owner_name" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="creatorName" column="creator_name" jdbcType="VARCHAR"/>
        <result property="updater" column="updater" jdbcType="VARCHAR"/>
        <result property="updaterName" column="updater_name" jdbcType="VARCHAR"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
        <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="memberIdEncrypt" column="member_id_encrypt" jdbcType="VARCHAR"/>
        <result property="message" column="message" jdbcType="VARCHAR"/>
        <result property="ownerType" column="owner_type" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into public.group_chat(chatid, name, owner, owner_name, remark, creator, creator_name, updater,
        updater_name, create_date, update_date, type, status, member_id_encrypt, message, owner_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.chatid} , #{entity.name} , #{entity.owner} , #{entity.ownerName} , #{entity.remark} ,
            #{entity.creator} , #{entity.creatorName} , #{entity.updater} , #{entity.updaterName} , #{entity.createDate}
            , #{entity.updateDate} , #{entity.type} , #{entity.status} , #{entity.member_id_encrypt} , #{entity.message} ,
            #{entity.ownerType})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into public.group_chat(chatid, name, owner, owner_name, remark, creator, creator_name, updater,
        updater_name, create_date, update_date, type, status, member_id_encrypt, message, owner_type)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.chatid}, #{entity.name}, #{entity.owner}, #{entity.ownerName}, #{entity.remark},
            #{entity.creator}, #{entity.creatorName}, #{entity.updater}, #{entity.updaterName}, #{entity.createDate},
            #{entity.updateDate}, #{entity.type}, #{entity.status}, #{entity.member_id_encrypt}, #{entity.message},
            #{entity.ownerType})
        </foreach>
        ON CONFLICT(id) DO update set
        chatid = EXCLUDED.chatid , name = EXCLUDED.name , owner = EXCLUDED.owner , owner_name = EXCLUDED.owner_name ,
        remark = EXCLUDED.remark , creator = EXCLUDED.creator , creator_name = EXCLUDED.creator_name , updater =
        EXCLUDED.updater , updater_name = EXCLUDED.updater_name , create_date = EXCLUDED.create_date , update_date =
        EXCLUDED.update_date , type = EXCLUDED.type , status = EXCLUDED.status , merber_id_encrypt =
        EXCLUDED.member_id_encrypt , message = EXCLUDED.message , owner_type = EXCLUDED.owner_type
    </insert>

    <insert id="insertByMe"  keyProperty="id" useGeneratedKeys="true">
<!--        <selectKey resultType="java.lang.Long"  order="AFTER" keyProperty="id" >-->
<!--            select nextval('public.chat_id_seq'::regclass) as id-->
<!--        </selectKey>-->
        insert into public.group_chat(chatid, name, owner, owner_name, remark, creator, creator_name, updater,
        updater_name, create_date, update_date, type, status, member_id_encrypt, message, owner_type)
        values
        (#{entity.chatid} , #{entity.name} , #{entity.owner} , #{entity.ownerName} , #{entity.remark} ,
        #{entity.creator} , #{entity.creatorName} , #{entity.updater} , #{entity.updaterName} , #{entity.createDate}
        , #{entity.updateDate} , #{entity.type} , #{entity.status} , #{entity.memberIdEncrypt} , #{entity.message} ,
        #{entity.ownerType})
    </insert>
</mapper>


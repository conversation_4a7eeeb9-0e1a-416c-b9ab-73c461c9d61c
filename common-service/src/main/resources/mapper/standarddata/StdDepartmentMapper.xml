<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.base.commonservice.standarddata.dao.StdDepartmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.base.commonservice.standarddata.po.StdDepartment">
                <id column="id" property="id" />
                <result column="name" property="name" />
                <result column="parentid" property="parentid" />
                <result column="order" property="order" />
                <result column="department_leader" property="departmentLeader" />
        <result column="path" property="path" />
        <result column="id_path" property="idPath" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                "name",
                parentid,
                "order",
                department_leader,
                "path",
                id_path
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" >
        INSERT INTO standard_data.std_department (
            id,
            name,
            parentid,
            "order",
            department_leader,
            path,
            id_path
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.name},
                #{entity.parentid},
                #{entity.order},
                #{entity.departmentLeader},
                #{entity.path},
                #{entity.idPath}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" >
        INSERT INTO standard_data.std_department (
            id,
            name,
            parentid,
            "order",
            department_leader,
            path,
            id_path
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.name},
                #{entity.parentid},
                #{entity.order},
                #{entity.departmentLeader},
                #{entity.path},
                #{entity.idPath}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                parentid = EXCLUDED.parentid,
                "order" = EXCLUDED.order,
                department_leader = EXCLUDED.department_leader,
                path = EXCLUDED.path,
                id_path = EXCLUDED.id_path
    </insert>

    <select id="getDeptTree" resultType="com.lanshan.base.commonservice.standarddata.vo.StdDepartmentTreeVO">
        select
               id,
               name,
               parentid,
               "order",
               department_leader
        from standard_data.std_department
        order by "order", id
    </select>



</mapper>

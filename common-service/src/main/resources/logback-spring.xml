<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <springProperty name="APP_NAME" scope="context" source="spring.application.name"/>

    <!--设置存储路径变量-->
    <property name="LOG_HOME" value="./logs"/>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{25}:%line %msg%n</pattern>
            <!--            <pattern>%yellow(%date{yyyy-MM-dd HH:mm:ss}) |%highlight(%-5level)  |%green(%logger:%line) |%black(%msg%n)</pattern>-->
            <pattern>%yellow(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) |%highlight(%-5level) |%boldMagenta(%logger{36}) |:%blue(%msg%n)</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--日志级别,此处配置级别会被配置文件中覆盖，如果配置文件不配置默认走此处级别-->
    <springProperty name="log.level" scope="context" source="log.level"/>
    <!-- 日志最大的历史（单位：天 ）不配置视为永久保存-->
    <property name="log.maxHistory" value="7"/>
    <!--日志切割最大大小-->
    <property name="log.maxSize" value="100MB"/>
    <!--格式化输出-->
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"/>
    <!--控制台输出appender-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <!--设置输出格式-->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
<!--            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>-->
            <!--            <pattern>%yellow(%date{yyyy-MM-dd HH:mm:ss}) |%highlight(%-5level)  |%green(%logger:%line) |%black(%msg%n)</pattern>-->
            <pattern>%yellow(%d{yyyy-MM-dd HH:mm:ss}) %green([%thread]) |%highlight(%-5level) |%boldMagenta(%logger{36}) |:%blue(%msg%n)</pattern>
            <!--设置编码-->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--输出到logstash的appender-->
    <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <!--可以访问的logstash日志收集端口-->
        <destination>192.168.3.37:4560</destination>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder"/>
    </appender>

    <!--info日志-->
    <appender name="infoFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 指定日志文件的名称 -->
        <file>${LOG_HOME}/info/${APP_NAME}-info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名,可设置文件类型为gz,开启文件压缩-->
            <fileNamePattern>${LOG_HOME}/info/${APP_NAME}-%d{yyyy-MM-dd}-%i.log.gz</fileNamePattern>
            <!--日志文件保留天数-->
            <maxHistory>${log.maxHistory}</maxHistory>
            <!--默认值10M，当文件大小超过log.maxSize时，通知RollingPolicy轮转-->
            <maxFileSize>${log.maxSize}</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <!-- 过滤器，过滤掉不是指定日志水平的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 设置日志级别 -->
            <level>info</level>
            <!-- 如果跟该日志水平相匹配，则接受 -->
            <onMatch>ACCEPT</onMatch>
            <!-- 如果跟该日志水平不匹配，则过滤掉 -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!--debug日志-->
    <appender name="debugFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 指定日志文件的名称 -->
        <file>${LOG_HOME}/debug/${APP_NAME}-debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名,可设置文件类型为gz,开启文件压缩-->
            <fileNamePattern>${LOG_HOME}/debug/${APP_NAME}-%d{yyyy-MM-dd}-%i.log.gz</fileNamePattern>
            <!--日志文件保留天数-->
            <!--            <maxHistory>${log.maxHistory}</maxHistory>-->
            <!--默认值10M，当文件大小超过log.maxSize时，通知RollingPolicy轮转-->
            <maxFileSize>${log.maxSize}</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <!-- 过滤器，过滤掉不是指定日志水平的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 设置日志级别 -->
            <level>debug</level>
            <!-- 如果跟该日志水平相匹配，则接受 -->
            <onMatch>ACCEPT</onMatch>
            <!-- 如果跟该日志水平不匹配，则过滤掉 -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <!--error日志-->
    <appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 指定日志文件的名称 -->
        <file>${LOG_HOME}/error/${APP_NAME}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名,可设置文件类型为gz,开启文件压缩-->
            <fileNamePattern>${LOG_HOME}/error/${APP_NAME}-%d{yyyy-MM-dd}-%i.log.gz</fileNamePattern>
            <!--日志文件保留天数-->
            <!--            <maxHistory>${log.maxHistory}</maxHistory>-->
            <!--默认值10M，当文件大小超过log.maxSize时，通知RollingPolicy轮转-->
            <maxFileSize>${log.maxSize}</maxFileSize>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <!-- 过滤器，过滤掉不是指定日志水平的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 设置日志级别 -->
            <level>error</level>
            <!-- 如果跟该日志水平相匹配，则接受 -->
            <onMatch>ACCEPT</onMatch>
            <!-- 如果跟该日志水平不匹配，则过滤掉 -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <logger name ="com.lanshan" level="debug"/>
    <!--        <appender-ref ref="console"/>-->

    <!--指定基础的日志输出级别-->
    <root>
        <level value="${log.level}"/>
        <!--appender将会添加到这个logger-->
        <appender-ref ref="console"/>
        <appender-ref ref="errorFile"/>
        <appender-ref ref="infoFile"/>
        <appender-ref ref="debugFile"/>
    </root>

</configuration>
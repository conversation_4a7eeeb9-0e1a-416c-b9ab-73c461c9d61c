package com.lanshan.base.starter.mybatis;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.lanshan.base.api.constant.CommonConstant;
import com.lanshan.base.api.utils.system.SecurityContextHolder;
import com.lanshan.base.starter.mybatis.constant.AutoFillFieldConstant;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Description: 公共字段，自动填充值
 * @Author: lanshan
 */
@Component
public class FieldMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        String userId = SecurityContextHolder.getUserId();
        //是否登录
        if (StringUtils.isNotBlank(userId)) {
            //创建者
            strictInsertFill(metaObject, AutoFillFieldConstant.CREATOR, String.class, userId);
            //更新者
            strictInsertFill(metaObject, AutoFillFieldConstant.UPDATER, String.class, userId);
            //创建者名称
            strictInsertFill(metaObject, AutoFillFieldConstant.CREATOR_NAME, String.class, SecurityContextHolder.getUserName());
            //更新者名称
            strictInsertFill(metaObject, AutoFillFieldConstant.UPDATER_NAME, String.class, SecurityContextHolder.getUserName());
        } else {
            //创建者
            strictInsertFill(metaObject, AutoFillFieldConstant.CREATOR, String.class, CommonConstant.SYS_USER);
            //更新者
            strictInsertFill(metaObject, AutoFillFieldConstant.UPDATER, String.class, CommonConstant.SYS_USER);
            //创建者名称
            strictInsertFill(metaObject, AutoFillFieldConstant.CREATOR_NAME, String.class, CommonConstant.SYS_USER_NAME);
            //更新者名称
            strictInsertFill(metaObject, AutoFillFieldConstant.UPDATER_NAME, String.class, CommonConstant.SYS_USER_NAME);
        }
        Date date = new Date();
        //创建时间
        this.setFieldValByName(AutoFillFieldConstant.CREATE_DATE, new Date(), metaObject);
        //更新时间
        this.setFieldValByName(AutoFillFieldConstant.UPDATE_DATE, new Date(), metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        String userId = SecurityContextHolder.getUserId();
        //是否登录
        if (StringUtils.isNotBlank(userId)) {
            //更新者
            strictInsertFill(metaObject, AutoFillFieldConstant.UPDATER, String.class, userId);
            //更新者名称
            strictInsertFill(metaObject, AutoFillFieldConstant.UPDATER_NAME, String.class, SecurityContextHolder.getUserName());
        } else {
            //更新者
            strictInsertFill(metaObject, AutoFillFieldConstant.UPDATER, String.class, CommonConstant.SYS_USER);
            //更新者名称
            strictInsertFill(metaObject, AutoFillFieldConstant.UPDATER_NAME, String.class, CommonConstant.SYS_USER_NAME);
        }

        //更新时间
        this.setFieldValByName(AutoFillFieldConstant.UPDATE_DATE, new Date(), metaObject);
    }
}
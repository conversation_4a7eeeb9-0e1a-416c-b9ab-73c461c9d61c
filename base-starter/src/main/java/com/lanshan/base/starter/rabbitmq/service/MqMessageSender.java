package com.lanshan.base.starter.rabbitmq.service;


import com.lanshan.base.starter.rabbitmq.component.RabbitmqOptions;
import com.lanshan.base.starter.rabbitmq.constant.RabbitConstant;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * rabbitmq发送消息服务
 *
 * <AUTHOR>
 */
@Component
public class MqMessageSender {
    @Resource
    private AmqpTemplate amqpTemplate;

    /**
     * 发送队列消息
     *
     * @param routingKey 消息队列路由
     * @param data       消息体
     */
    public void sendMessage(String routingKey, Object data) {
        amqpTemplate.convertAndSend(routingKey, data);
    }

    /**
     * 发送队列消息
     *
     * @param exchange   消息队列交换机
     * @param routingKey 消息队列路由
     * @param data       消息体
     */
    public void sendMessage(String exchange, String routingKey, Object data) {
        amqpTemplate.convertAndSend(exchange, routingKey, data);
    }

    /**
     * 发送死信队列消息
     *
     * @param routingKey 消息队列路由
     * @param data       消息体
     * @param timeUnit   时间单位
     * @param timeout    超时时间
     */
    public void sendDelayedMessage(String routingKey, Object data, TimeUnit timeUnit, long timeout) {
        amqpTemplate.convertAndSend(
                RabbitConstant.DELAYED_EXCHANGE,
                routingKey,
                data,
                message -> RabbitmqOptions.delay(message, timeUnit, timeout)
        );
    }
}

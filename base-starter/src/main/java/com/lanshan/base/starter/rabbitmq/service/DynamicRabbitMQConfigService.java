package com.lanshan.base.starter.rabbitmq.service;

import com.lanshan.base.starter.rabbitmq.factory.ExchangeFactory;
import com.lanshan.base.starter.rabbitmq.properties.RabbitMQProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 动态RabbitMQ配置管理服务
 * 支持不重启服务动态创建队列、交换机和绑定关系
 */
@Slf4j
@Service
public class DynamicRabbitMQConfigService {

    @Resource
    private RabbitMQProperties rabbitMQProperties;

    @Resource
    private RabbitAdmin rabbitAdmin;

    @Resource
    private ExchangeFactory exchangeFactory;

    // 缓存已创建的资源，避免重复创建
    private final Set<String> createdQueues = ConcurrentHashMap.newKeySet();
    private final Set<String> createdExchanges = ConcurrentHashMap.newKeySet();
    private final Set<String> createdBindings = ConcurrentHashMap.newKeySet();

    /**
     * 应用启动完成后初始化RabbitMQ配置
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        if (rabbitMQProperties.isEnabled()) {
            initializeRabbitMQResources();
        }
    }

    /**
     * 监听配置变更事件，动态更新RabbitMQ配置
     */
    @EventListener(EnvironmentChangeEvent.class)
    public void onEnvironmentChange(EnvironmentChangeEvent event) {
        if (rabbitMQProperties.isEnabled() && containsRabbitMQConfig(event.getKeys())) {
            log.info("检测到RabbitMQ配置变更，开始动态更新...");
            initializeRabbitMQResources();
        }
    }

    /**
     * 手动刷新配置（提供给外部调用）
     */
    public void refreshConfiguration() {
        if (rabbitMQProperties.isEnabled()) {
            log.info("手动刷新RabbitMQ配置...");
            initializeRabbitMQResources();
        }
    }

    /**
     * 初始化RabbitMQ资源
     */
    private void initializeRabbitMQResources() {
        try {
            // 创建交换机
            if (rabbitMQProperties.getExchanges() != null) {
                rabbitMQProperties.getExchanges().forEach(this::createExchange);
            }

            // 创建队列
            if (rabbitMQProperties.getQueues() != null) {
                rabbitMQProperties.getQueues().forEach(this::createQueue);
            }

            // 创建绑定关系
            if (rabbitMQProperties.getBindings() != null) {
                rabbitMQProperties.getBindings().forEach(this::createBinding);
            }

            log.info("RabbitMQ动态配置更新完成");
        } catch (Exception e) {
            log.error("RabbitMQ动态配置更新失败", e);
        }
    }

    /**
     * 创建队列
     */
    public void createQueue(RabbitMQProperties.QueueConfig queueConfig) {
        if (createdQueues.contains(queueConfig.getName())) {
            log.debug("队列已存在，跳过创建: {}", queueConfig.getName());
            return;
        }

        try {
            Map<String, Object> arguments = new HashMap<>();

            // 设置队列参数
            if (queueConfig.getArguments() != null) {
                arguments.putAll(queueConfig.getArguments());
            }

            // 设置死信队列
            if (queueConfig.getDeadLetterExchange() != null) {
                arguments.put("x-dead-letter-exchange", queueConfig.getDeadLetterExchange());
            }
            if (queueConfig.getDeadLetterRoutingKey() != null) {
                arguments.put("x-dead-letter-routing-key", queueConfig.getDeadLetterRoutingKey());
            }

            // 设置TTL
            if (queueConfig.getTtl() != null) {
                arguments.put("x-message-ttl", queueConfig.getTtl());
            }

            // 设置队列最大长度
            if (queueConfig.getMaxLength() != null) {
                arguments.put("x-max-length", queueConfig.getMaxLength());
            }

            // 设置队列最大优先级
            if (queueConfig.getMaxPriority() != null) {
                arguments.put("x-max-priority", queueConfig.getMaxPriority());
            }

            Queue queue = new Queue(
                    queueConfig.getName(),
                    queueConfig.isDurable(),
                    queueConfig.isExclusive(),
                    queueConfig.isAutoDelete(),
                    arguments
            );

            rabbitAdmin.declareQueue(queue);
            createdQueues.add(queueConfig.getName());
            log.info("成功创建队列: {}", queueConfig.getName());

        } catch (Exception e) {
            log.error("创建队列失败: {}", queueConfig.getName(), e);
        }
    }

    /**
     * 创建交换机
     */
    public void createExchange(RabbitMQProperties.ExchangeConfig exchangeConfig) {
        if (createdExchanges.contains(exchangeConfig.getName())) {
            log.debug("交换机已存在，跳过创建: {}", exchangeConfig.getName());
            return;
        }

        try {
            AbstractExchange exchange = exchangeFactory.createExchange(exchangeConfig);
            rabbitAdmin.declareExchange(exchange);
            createdExchanges.add(exchangeConfig.getName());
            log.info("成功创建交换机: {} (类型: {})", exchangeConfig.getName(), exchangeConfig.getType());

        } catch (Exception e) {
            log.error("创建交换机失败: {} (类型: {})", exchangeConfig.getName(), exchangeConfig.getType(), e);
        }
    }

    /**
     * 创建绑定关系
     */
    public void createBinding(RabbitMQProperties.BindingConfig bindingConfig) {
        String bindingKey = bindingConfig.getQueue() + ":" + bindingConfig.getExchange() + ":" + bindingConfig.getRoutingKey();

        if (createdBindings.contains(bindingKey)) {
            log.debug("绑定关系已存在，跳过创建: {}", bindingKey);
            return;
        }

        try {
            Binding binding;

            if (bindingConfig.getArguments() != null && !bindingConfig.getArguments().isEmpty()) {
                // Headers交换机绑定
                binding = BindingBuilder
                        .bind(new Queue(bindingConfig.getQueue()))
                        .to(new HeadersExchange(bindingConfig.getExchange()))
                        .whereAll(bindingConfig.getArguments())
                        .match();
            } else {
                // 普通绑定
                binding = BindingBuilder
                        .bind(new Queue(bindingConfig.getQueue()))
                        .to(new DirectExchange(bindingConfig.getExchange()))
                        .with(bindingConfig.getRoutingKey());
            }

            rabbitAdmin.declareBinding(binding);
            createdBindings.add(bindingKey);
            log.info("成功创建绑定: {} -> {} (路由键: {})",
                    bindingConfig.getQueue(), bindingConfig.getExchange(), bindingConfig.getRoutingKey());

        } catch (Exception e) {
            log.error("创建绑定失败: {} -> {} (路由键: {})",
                    bindingConfig.getQueue(), bindingConfig.getExchange(), bindingConfig.getRoutingKey(), e);
        }
    }

    /**
     * 检查配置变更是否包含RabbitMQ相关配置
     */
    private boolean containsRabbitMQConfig(Set<String> keys) {
        return keys.stream()
                .anyMatch(key -> key.startsWith("spring.rabbitmq.custom"));
    }

    /**
     * 清理缓存（用于测试或特殊场景）
     */
    public void clearCache() {
        createdQueues.clear();
        createdExchanges.clear();
        createdBindings.clear();
    }
} 
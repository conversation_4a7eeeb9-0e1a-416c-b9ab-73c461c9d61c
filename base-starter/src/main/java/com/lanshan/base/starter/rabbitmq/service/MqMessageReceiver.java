package com.lanshan.base.starter.rabbitmq.service;

import com.alibaba.fastjson2.JSONObject;
import com.lanshan.base.starter.rabbitmq.constant.QueueConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 接收消息中间件消息示例
 *
 */
@Component
@Slf4j
public class MqMessageReceiver {

    @RabbitListener(queues = {QueueConstant.TEST_QUEUE}, containerFactory="customContainerFactory")
    public void receive(@Payload JSONObject message) {
        log.info("接收到消息：" + message);
    }
}

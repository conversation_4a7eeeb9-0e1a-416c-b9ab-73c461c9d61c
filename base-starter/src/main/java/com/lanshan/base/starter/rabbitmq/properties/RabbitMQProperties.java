package com.lanshan.base.starter.rabbitmq.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;
import java.util.Map;

/**
 * RabbitMQ统一动态配置属性
 * 支持配置刷新，不重启服务动态加载
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "spring.rabbitmq.custom")
public class RabbitMQProperties {

    /**
     * 是否启用动态配置
     */
    private boolean enabled = true;

    /**
     * 队列配置列表
     */
    private List<QueueConfig> queues;

    /**
     * 交换机配置列表
     */
    private List<ExchangeConfig> exchanges;

    /**
     * 绑定关系配置列表
     */
    private List<BindingConfig> bindings;

    @Data
    public static class QueueConfig {
        /**
         * 队列名称
         */
        private String name;

        /**
         * 是否持久化
         */
        private boolean durable = true;

        /**
         * 是否排他
         */
        private boolean exclusive = false;

        /**
         * 是否自动删除
         */
        private boolean autoDelete = false;

        /**
         * 死信交换机
         */
        private String deadLetterExchange;

        /**
         * 死信路由键
         */
        private String deadLetterRoutingKey;

        /**
         * 消息TTL（毫秒）
         */
        private Long ttl;

        /**
         * 队列最大长度
         */
        private Integer maxLength;

        /**
         * 队列最大优先级
         */
        private Integer maxPriority;

        /**
         * 延迟队列类型
         */
        private String delayedType;

        /**
         * 其他参数
         */
        private Map<String, Object> arguments;
    }

    @Data
    public static class ExchangeConfig {
        /**
         * 交换机名称
         */
        private String name;

        /**
         * 交换机类型：direct, topic, fanout, headers, x-delayed-message, x-consistent-hash
         */
        private String type = "direct";

        /**
         * 是否持久化
         */
        private boolean durable = true;

        /**
         * 是否自动删除
         */
        private boolean autoDelete = false;

        /**
         * 是否内部交换机
         */
        private boolean internal = false;

        /**
         * 延迟消息类型（用于x-delayed-message）
         */
        private String delayedType;

        /**
         * 其他参数
         */
        private Map<String, Object> arguments;
    }

    @Data
    public static class BindingConfig {
        /**
         * 队列名称
         */
        private String queue;

        /**
         * 交换机名称
         */
        private String exchange;

        /**
         * 路由键
         */
        private String routingKey;

        /**
         * 绑定参数（用于headers交换机）
         */
        private Map<String, Object> arguments;
    }
} 
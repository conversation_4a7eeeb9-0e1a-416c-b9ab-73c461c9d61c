package com.lanshan.base.starter.rabbitmq.factory;

import com.lanshan.base.starter.rabbitmq.component.DelayedExchange;
import com.lanshan.base.starter.rabbitmq.properties.RabbitMQProperties;
import org.springframework.amqp.core.*;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 交换机工厂类
 * 支持所有RabbitMQ交换机类型
 */
@Component
public class ExchangeFactory {

    /**
     * 创建交换机
     */
    public AbstractExchange createExchange(RabbitMQProperties.ExchangeConfig config) {
        AbstractExchange exchange;
        Map<String, Object> arguments = config.getArguments() != null ?
                new HashMap<>(config.getArguments()) : new HashMap<>();

        switch (config.getType().toLowerCase()) {
            case "direct":
                exchange = new DirectExchange(
                        config.getName(),
                        config.isDurable(),
                        config.isAutoDelete(),
                        arguments
                );
                break;

            case "topic":
                exchange = new TopicExchange(
                        config.getName(),
                        config.isDurable(),
                        config.isAutoDelete(),
                        arguments
                );
                break;

            case "fanout":
                exchange = new FanoutExchange(
                        config.getName(),
                        config.isDurable(),
                        config.isAutoDelete(),
                        arguments
                );
                break;

            case "headers":
                exchange = new HeadersExchange(
                        config.getName(),
                        config.isDurable(),
                        config.isAutoDelete(),
                        arguments
                );
                break;

            case "x-delayed-message":
                // 延时消息交换机
                if (config.getDelayedType() != null) {
                    arguments.put("x-delayed-type", config.getDelayedType());
                } else {
                    arguments.put("x-delayed-type", "direct");
                }
                exchange = new DelayedExchange(
                        config.getName(),
                        config.isDurable(),
                        config.isAutoDelete(),
                        arguments
                );
                break;

            case "x-consistent-hash":
                // 一致性哈希交换机
                exchange = new CustomExchange(
                        config.getName(),
                        "x-consistent-hash",
                        config.isDurable(),
                        config.isAutoDelete(),
                        arguments
                );
                break;

            case "x-random":
                // 随机交换机
                exchange = new CustomExchange(
                        config.getName(),
                        "x-random",
                        config.isDurable(),
                        config.isAutoDelete(),
                        arguments
                );
                break;

            case "x-recent-history":
                // 最近历史交换机
                exchange = new CustomExchange(
                        config.getName(),
                        "x-recent-history",
                        config.isDurable(),
                        config.isAutoDelete(),
                        arguments
                );
                break;

            default:
                // 自定义交换机类型
                exchange = new CustomExchange(
                        config.getName(),
                        config.getType(),
                        config.isDurable(),
                        config.isAutoDelete(),
                        arguments
                );
        }

        // 设置内部交换机属性
        if (config.isInternal() && exchange instanceof AbstractExchange) {
            // 注意：Spring AMQP可能不直接支持internal属性，需要通过arguments设置
            arguments.put("internal", config.isInternal());
        }

        return exchange;
    }
} 
package com.lanshan.base.starter.rabbitmq.config;

import com.lanshan.base.starter.rabbitmq.properties.RabbitMQProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * RabbitMQ自动配置类
 * 提供统一的队列、交换机、绑定关系自动创建
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(RabbitMQProperties.class)
@ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true", matchIfMissing = true)
public class RabbitMQAutoConfiguration {

    private final RabbitMQProperties properties;
    private final RabbitAdmin rabbitAdmin;

    public RabbitMQAutoConfiguration(RabbitMQProperties properties, RabbitAdmin rabbitAdmin) {
        this.properties = properties;
        this.rabbitAdmin = rabbitAdmin;
    }

    @PostConstruct
    public void initRabbitMQ() {
        log.info("开始初始化RabbitMQ队列和交换机...");

        // 自动创建配置的队列和交换机
        if (properties.getQueues() != null) {
            properties.getQueues().forEach(this::createQueue);
        }

        if (properties.getExchanges() != null) {
            properties.getExchanges().forEach(this::createExchange);
        }

        if (properties.getBindings() != null) {
            properties.getBindings().forEach(this::createBinding);
        }

        log.info("RabbitMQ初始化完成");
    }

    private void createQueue(RabbitMQProperties.QueueConfig queueConfig) {
        Map<String, Object> arguments = new HashMap<>();

        // 设置队列参数
        if (queueConfig.getArguments() != null) {
            arguments.putAll(queueConfig.getArguments());
        }

        // 设置死信队列
        if (queueConfig.getDeadLetterExchange() != null) {
            arguments.put("x-dead-letter-exchange", queueConfig.getDeadLetterExchange());
        }
        if (queueConfig.getDeadLetterRoutingKey() != null) {
            arguments.put("x-dead-letter-routing-key", queueConfig.getDeadLetterRoutingKey());
        }

        // 设置TTL
        if (queueConfig.getTtl() != null) {
            arguments.put("x-message-ttl", queueConfig.getTtl());
        }

        Queue queue = new Queue(
                queueConfig.getName(),
                queueConfig.isDurable(),
                queueConfig.isExclusive(),
                queueConfig.isAutoDelete(),
                arguments
        );

        rabbitAdmin.declareQueue(queue);
        log.info("创建队列: {}", queueConfig.getName());
    }

    private void createExchange(RabbitMQProperties.ExchangeConfig exchangeConfig) {
        AbstractExchange exchange;

        switch (exchangeConfig.getType().toLowerCase()) {
            case "direct":
                exchange = new DirectExchange(
                        exchangeConfig.getName(),
                        exchangeConfig.isDurable(),
                        exchangeConfig.isAutoDelete()
                );
                break;
            case "topic":
                exchange = new TopicExchange(
                        exchangeConfig.getName(),
                        exchangeConfig.isDurable(),
                        exchangeConfig.isAutoDelete()
                );
                break;
            case "fanout":
                exchange = new FanoutExchange(
                        exchangeConfig.getName(),
                        exchangeConfig.isDurable(),
                        exchangeConfig.isAutoDelete()
                );
                break;
            default:
                throw new IllegalArgumentException("不支持的交换机类型: " + exchangeConfig.getType());
        }

        rabbitAdmin.declareExchange(exchange);
        log.info("创建交换机: {} (类型: {})", exchangeConfig.getName(), exchangeConfig.getType());
    }

    private void createBinding(RabbitMQProperties.BindingConfig bindingConfig) {
        Binding binding = BindingBuilder
                .bind(new Queue(bindingConfig.getQueue()))
                .to(new DirectExchange(bindingConfig.getExchange()))
                .with(bindingConfig.getRoutingKey());

        rabbitAdmin.declareBinding(binding);
        log.info("创建绑定: {} -> {} (路由键: {})",
                bindingConfig.getQueue(), bindingConfig.getExchange(), bindingConfig.getRoutingKey());
    }
} 
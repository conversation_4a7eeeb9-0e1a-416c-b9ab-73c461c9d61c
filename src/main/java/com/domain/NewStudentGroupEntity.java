package com.domain;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

/**
 * (NewStudentGroup)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-20 16:13:42
 */
@Data
public class NewStudentGroupEntity  {
@TableId(value = "id", type = IdType.AUTO)
    private Long id;
@TableField(value = "group_name")
    private String groupName;
@TableField(value = "group_owner")
    private String groupOwner;
@TableField(value = "group_owner_no")
    private String groupOwnerNo;
@TableField(value = "department")
    private String department;
@TableField(value = "year")
    private String year;
@TableField(value = "person_num")
    private Long personNum;
@TableField(value = "remart")
    private String remart;
@TableField(value = "create_time")
    private Date createTime;
@TableField(value = "last_update_time")
    private Date lastUpdateTime;




}


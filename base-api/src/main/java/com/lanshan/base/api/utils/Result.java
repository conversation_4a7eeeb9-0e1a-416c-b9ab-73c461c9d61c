package com.lanshan.base.api.utils;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lanshan.base.api.constant.ErrorCode;
import com.lanshan.base.api.utils.system.MessageUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * API返回结果包装类
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel(value = "响应结果")
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private static final String DEFAULT_MSG = "成功";

    /**
     * 编码：0表示成功，其他值表示失败
     */
    @ApiModelProperty(value = "编码：0表示成功，其他值表示失败")
    private int code = 0;
    /**
     * 消息内容
     */
    @ApiModelProperty(value = "消息内容")
    private String msg = "success";
    /**
     * 响应数据
     */
    @ApiModelProperty(value = "响应数据")
    private T data;

    private Result(String msg, T result) {
        this.msg = msg;
        this.data = result;
    }

    public static <T> Result<T> build() {
        return new Result<>(DEFAULT_MSG, null);
    }

//    public static <T> Result<T> build(String msg) {
//        return new Result<>(msg, null);
//    }

    public static <T> Result<T> build(T result) {
        return new Result<>(DEFAULT_MSG, result);
    }

    public static <T> Result<T> build(String msg, T result) {
        return new Result<>(msg, result);
    }

    public Result<T> ok(T result) {
        this.setResult(result);
        return this;
    }

    public boolean success() {
        return code == 0;
    }

    public boolean hasError() {
        return !success();
    }

    public Result<T> error() {
        this.code = ErrorCode.ERROR;
        this.msg = MessageUtils.getMessage(this.code);

        return this;
    }

    public Result<T> error(int code) {
        this.code = code;
        this.msg = MessageUtils.getMessage(this.code);
        return this;
    }

    public Result<T> error(int code, T result) {
        this.code = code;
        this.msg = MessageUtils.getMessage(this.code);
        this.data = result;
        return this;
    }

    public Result<T> error(String msg) {
        this.code = ErrorCode.CUSTOMIZE_ERROR;
        this.msg = msg;
        return this;
    }

    public Result<T> error(String msg, T result) {
        this.code = ErrorCode.CUSTOMIZE_ERROR;
        this.msg = msg;
        this.data = result;
        return this;
    }

    public Result<T> error(int code, String msg, T result) {
        this.code = code;
        this.msg = msg;
        this.data = result;
        return this;
    }

    public void setResult(T data) {
        this.data = data;
    }

    @JsonIgnore
    public T getResult() {
        return this.data;
    }
}

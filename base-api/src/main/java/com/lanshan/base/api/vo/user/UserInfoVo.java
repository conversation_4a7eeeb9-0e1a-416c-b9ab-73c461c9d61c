package com.lanshan.base.api.vo.user;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
@EqualsAndHashCode
public class UserInfoVo implements Serializable {

    private static final long serialVersionUID = -7635323390460574115L;

    @ApiModelProperty(value = "成员UserID")
    private String userid;

    @ApiModelProperty(value = "成员名称")
    private String name;

    @ApiModelProperty(value = "成员所属部门id列表")
    private List<Long> department;

    @ApiModelProperty(value = "部门内的排序值")
    private List<Integer> order;

    @ApiModelProperty(value = "职务信息")
    private String position;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "性别")
    private String genderDesc;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "企业邮箱")
    @JsonProperty("biz_mail")
    private String bizMail;

    @ApiModelProperty(value = "表示在所在的部门内是否为部门负责人")
    @JsonProperty("is_leader_in_dept")
    private List<Integer> isLeaderInDept;

    @ApiModelProperty(value = "直属上级UserID")
    @JsonProperty("direct_leader")
    private List<String> directLeader;

    @ApiModelProperty(value = "头像url")
    private String avatar;

    @ApiModelProperty(value = "头像缩略图url")
    @JsonProperty("thumb_avatar")
    private String thumbAvatar;

    @ApiModelProperty(value = "座机")
    private String telephone;

    @ApiModelProperty(value = "别名")
    private String alias;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "open_userid")
    @JsonProperty("open_userid")
    private String openUserid;

    @ApiModelProperty(value = "主部门")
    @JsonProperty("main_department")
    private Long mainDepartment;

    @ApiModelProperty(value = "激活状态")
    private Integer status;

    @ApiModelProperty(value = "激活状态")
    private String statusDesc;

    @ApiModelProperty(value = "员工个人二维码")
    @JsonProperty("qr_code")
    private String qrCode;

    @ApiModelProperty(value = "扩展属性")
    private Extattr extattr;

    @ApiModelProperty(value = "对外职务")
    @JsonProperty("external_position")
    private String externalPosition;

    @ApiModelProperty(value = "成员对外属性")
    @JsonProperty("external_profile")
    private ExternalProfile externalProfile;

    @ApiModelProperty("用户类型 0：其他人员 1：教职工 2：本科生 3：研究生")
    private Integer userType;


    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class Extattr implements Serializable {
        private static final long serialVersionUID = 1L;

        /**[
            {
                "type": 0,
                "name": "文本名称",
                "text": {
                    "value": "文本"
                }
            },
            {
                "type": 1,
                "name": "网页名称",
                "web": {
                    "url": "http://www.test.com",
                    "title": "标题"
                }
            }
		]*/
        @ApiModelProperty(value = "属性列表")
        private List<ObjectNode> attrs;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class ExternalProfile implements Serializable {
        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "企业简称")
        @JsonProperty("external_corp_name")
        private String externalCorpName;
        @ApiModelProperty(value = "视频号信息")
        @JsonProperty("wechat_channels")
        private WechatChannels wechatChannels;

        /**[
            {
                "type": 0,
                "name": "文本名称",
                "text": {
                    "value": "文本"
                }
            },
            {
                "type": 1,
                "name": "网页名称",
                "web": {
                    "url": "http://www.test.com",
                    "title": "标题"
                }
            },
            {
                "type": 2,
                "name": "测试app",
                "miniprogram": {
                    "appid": "wx8bd80126147dFAKE",
                    "pagepath": "/index",
                    "title": "my miniprogram"
                }
            }
		]*/
        @ApiModelProperty(value = "属性列表")
        @JsonProperty("external_attr")
        private List<ObjectNode> externalAttr;

        @Data
        public static class WechatChannels implements Serializable {
            private static final long serialVersionUID = 1L;

            @ApiModelProperty(value = "视频号名称")
            private String nickname;
            @ApiModelProperty(value = "状态")
            private Integer status;

        }
    }

    @ApiModelProperty(value = "成员所属主部门名称")
    private String mainDepartmentName;

    @ApiModelProperty(value = "成员所属主部门path")
    private String mainPath;

    @ApiModelProperty(value = "部门列表")
    private List<DepartmentVo> departmentVoList;

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class DepartmentVo implements Serializable {
        private static final long serialVersionUID = 1;

        @ApiModelProperty(value = "部门id")
        private Long departmentid;

        @ApiModelProperty(value = "部门名称")
        private String departmentName;

        @ApiModelProperty(value = "部门路径")
        private String departmentPath;
    }

    @ApiModelProperty(value = "标签列表")
    private List<TagVo> tagVoList;

    @ApiModelProperty(value = "部门标签列表")
    private List<TagVo> deptTagVoList;

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class TagVo implements Serializable {
        private static final long serialVersionUID = 1;

        @ApiModelProperty(value = "标签id")
        private Long tagid;

        @ApiModelProperty(value = "标签名称")
        private String tagname;
    }

    @ApiModelProperty(value = "部门 用[,]分割")
    private String deptStr;

    @ApiModelProperty(value = "标签 用[,]分割")
    private String tagStr;
}

package com.lanshan.base.api.feign.addressbook.fallback;

import com.lanshan.base.api.feign.addressbook.CpUserFeign;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.vo.user.UserInfoPartVO;
import com.lanshan.base.api.vo.user.UserInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description 通讯录用户服务异常处理
 */
@Slf4j
@Component
public class CpUserFeignFallbackFactory implements FallbackFactory<CpUserFeign> {
    @Override
    public CpUserFeign create(Throwable cause) {
        log.error("通讯录用户服务异常：", cause);
        return new CpUserFeign() {
            @Override
            public Result<List<UserInfoVo>> listUsersByUseridList(List<String> useridList) {
                return null;
            }

            @Override
            public Result<UserInfoVo> getUserByUserid(String userid) {
                return null;
            }

            @Override
            public Result<List<UserInfoVo>> listCacheUserByUserids(Collection<String> userid) {
                return null;
            }

            @Override
            public Result<Collection<UserInfoPartVO>> getActiveUsers() {
                return null;
            }

            @Override
            public Result<List<UserInfoVo>> listUsersByDepartmentid(Long departmentid) {
                return null;
            }

            @Override
            public Result<List<UserInfoPartVO>> listUsersByDeptIds(List<Long> deptIds) {
                return null;
            }
        };
    }
}

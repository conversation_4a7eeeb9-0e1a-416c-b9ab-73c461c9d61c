package com.lanshan.base.api.enums;


import com.lanshan.base.api.exception.ServiceException;
import lombok.Getter;

/**
 * 异常编码
 *
 * <AUTHOR>
 */
@Getter
public enum ExceptionCodeEnum {

    //系统相关 start
    SUCCESS(0, "成功"),
    SYSTEM_BUSY(-1, "系统繁忙~请稍后再试~"),
    SYSTEM_TIMEOUT(-2, "系统维护中~请稍后再试~"),
    PARAM_EX(-3, "参数类型解析异常"),
    SQL_EX(-4, "运行SQL出现异常"),
    NULL_POINT_EX(-5, "空指针异常"),
    ILLEGAL_ARGUMENT_EX(-6, "无效参数异常"),
    MEDIA_TYPE_EX(-7, "请求类型异常"),
    LOAD_RESOURCES_ERROR(-8, "加载资源出错"),
    BASE_VALID_PARAM(-9, "统一验证参数异常"),
    OPERATION_EX(-10, "操作异常"),
    SERVICE_MAPPER_ERROR(-11, "Mapper类转换异常"),
    CAPTCHA_ERROR(-12, "验证码校验失败"),
    JSON_PARSE_ERROR(-13, "JSON解析异常"),
    PARAM_NOT_EMPTY(-14, "参数[%s]不能为空！"),
    SYS_CONFIG_NOT_EXIST(-15, "配置不存在！"),
    NOT_OPEN_IN_CP(-16, "请在企微中打开！"),
    EXECPTION(-17, "系统异常，请联系厂家运维人员！"),


    OK(200, "OK"),
    BAD_REQUEST(400, "错误的请求"),
    UNAUTHORIZED(401, "未认证"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "没有找到资源"),
    METHOD_NOT_ALLOWED(405, "不支持当前请求类型"),
    TOO_MANY_REQUESTS(429, "请求超过次数限制"),
    INTERNAL_SERVER_ERROR(500, "内部服务错误"),
    BAD_GATEWAY(502, "网关错误"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    REQUIRED_FILE_PARAM_EX(1001, "请求中必须至少包含一个有效文件"),
    DATA_SAVE_ERROR(2000, "新增数据失败"),
    DATA_UPDATE_ERROR(2001, "修改数据失败"),
    TOO_MUCH_DATA_ERROR(2002, "批量新增数据过多"),

    JWT_BASIC_INVALID(40000, "无效的基本身份验证令牌"),
    JWT_TOKEN_EXPIRED(40001, "会话超时，请重新登录"),
    JWT_SIGNATURE(40002, "不合法的token，请认真比对 token 的签名"),
    JWT_ILLEGAL_ARGUMENT(40003, "缺少token参数"),
    JWT_GEN_TOKEN_FAIL(40004, "生成token失败"),
    JWT_PARSER_TOKEN_FAIL(40005, "解析用户身份错误，请重新登录！"),
    JWT_USER_INVALID(40006, "用户名或密码错误"),
    JWT_USER_ENABLED(40007, "用户已经被禁用！"),
    JWT_OFFLINE(40008, "您已在另一个设备登录！"),
    JWT_NOT_LOGIN(40009, "登录超时，请重新登录！"),
    JWT_USER_INVALID_OVER_TIMES(40010, "密码输入错误%s次，帐户锁定%s小时"),

    JWT_USER_INVALID_TIMES(40011, "用户名或密码错误。再尝试错误%s次，账户将被锁定%s小时！"),

    FILE_UPLOAD_ERROR(50000, "文件上传服务异常！"),
    FILE_NOT_FOUND(50001, "文件不存在！"),
    FILE_SIZE_EXCEED(50002, "文件过大，单个文件大小不能超过50M！"),
    FILE_TYPE_ERROR(50003, "文件格式不正确，请查看操作手册！"),
    FILE_DOWNLOAD_ERROR(50004, "文件下载异常！"),
    FILE_DELETE_ERROR(50005, "文件删除失败！"),
    EXPORT_EXCEL_ERROR(50006, "导出Excel异常！"),
    CONFIG_DELETED_DISABLED(60000, "内置参数【%s】不能删除"),

    AGENT_NOT_FOUND(70000, "企业微信访问异常，检查应用ID, Secret是否正确！"),
    APP_NOT_FOUND(70001, "应用不存在！"),
    AGENT_CONFIG_NOT_FOUND(70002, "企业微信配置不存在！"),
    AGENT_EXIST(70003, "企业微信应用已存在，请勿重复添加！"),
    APP_NAME_EXIST(70004, "应用名称已存在，请勿重复添加！"),
    THEME_EXIST(70005, "主题已存在，请勿重复添加！"),
    THEME_NOT_EXIST(70006, "主题不存在！"),
    CAN_NOT_DEL_DEFAULT_THEME(70007, "默认主题不能删除！"),
    AGENT_ZONE_EXIST(70008, "应用专区已存在，请勿重复添加！"),
    LOCK_ERROR(70009, "正在执行相同操作，请勿重复提交"),
    MONITOR_URL_NOT_EXIST(70010, "地址不存在"),
    APP_OPERATE_ERROR(70011, "应用操作失败"),
    THEME_IMPORT_ERROR(70012, "主题导入失败，请检查文件格式！"),
    MONITOR_HEALTH_CHECK_ERROR(70013, "健康检测地址请求超时或无法访问，请检查！"),
    APP_DESC_LENG_LE4(70014, "小程序应用描述字段长度不能小于4个字符！"),
    WX_CP_APP_THEME_EXIST(70015, "无企微应用，无法添加主题！"),
    TAG_NAME_EXIST(80000, "标签名称已存在，请勿重复添加！"),
    CORP_ID_EXIST(80001, "企业微信微corpId配置，请联系运维人员进行配置"),
    AGENTT_ID_EXIST(80002, "企业微信agentId微配置，请联系运维人员进行配置"),

    //群聊相关
    GROUP_CHAT_NOT_EXIST(80100, "群聊不存在！"),
    GROUP_CHAT_NOT_CHOICE_SCOPE(80101, "必须选择群聊范围"),
    GROUP_CHAT_NOT_FIND_MEMBER(80102, "企微通讯录暂未找到成员"),
    GROUP_CHAT_MEMBER_SIZE_ERROR(80103, "实际有效的群成员数量至少2人，至多2000人"),
    GROUP_CHAT_OWNER_NOT_MEMBER(80104, "群主必须是群成员之一"),
    GROUP_CHAT_CREAT_FAIL(80105, "群聊创建失败"),
    GROUP_CHAT_EXIST(80106, "所选成员的群聊已存在"),
    GROUP_CHAT_MEMBER_EXIST(80107, "所选成员已存在于当前群聊中"),
    GROUP_CHAT_ADD_MEMBER_ERROR(80108, "群聊添加成员失败"),
    GROUP_CHAT_REMOVE_MEMBER_ERROR(80109, "群聊移除成员失败"),
    GROUP_CHAT_SEND_MESSAGE_ERROR(80110, "发送群聊消息失败"),
    GROUP_CHAT_UPLOAD_MATERIAL_ERROR(80111, "群聊上传临时素材失败"),
    GROUP_CHAT_STUDENT_TYPE_NOT_EXIST(80112, "学生类型不存在"),
    GROUP_CHAT_CLASS_STUDENT_IS_EMPTY(80113, "班级下无学生"),
    GROUP_CHAT_CLASS_CREATE_ERROR(80114, "建群失败，请稍后再试"),
    GROUP_CHAT_CLASS_EXISTS(80115, "班级群已存在"),
    GROUP_CHAT_COURSE_EXISTS(80116, "班级群已存在"),
    GROUP_CHAT_TYPE_NOT_SUPPORT(80117, "不支持的群聊类型"),
    GROUP_CHAT_TCH_NOT_IN_CP(80118, "教师不在企微中"),
    GROUP_CHAT_TCH_NOT_ACTIVE(80119, "教师未激活，请 24 小时后再试"),

    //通讯录相关
    USER_NOT_EXIST(90000, "用户不存在！"),
    USER_EXIST(90001, "用户已存在！"),
    DEPT_NOT_EXIST(90002, "部门不存在！"),
    DEPT_EXIST(90003, "部门已存在！"),
    TAG_NOT_EXIST(90004, "标签不存在！"),
    TAG_EXIST(90005, "标签已存在！"),
    USER_BIND_MOBILE_NOT_EXIST(90006, "用户数据不存在！"),
    USER_BIND_MOBILE_EXIST(90007, "该手机号已绑定其他账号！"),
    INVITE_OUTSIDE_SUBMITTED(90008, "该手机号已经提交过审批！"),
    INVITE_OUTSIDE_SETTING_NOT_EXIST(90009, "未找到邀请配置！"),
    INVITE_OUTSIDE_USER_EXIST(90010, "当前手机号的用户已存在！"),
    INVITE_OUTSIDE_SUBMIT_NOTICE_SEND_FAIL(90011, "提交通知发送失败！"),
    INVITE_OUTSIDE_DEPT_NOT_EXIST(90012, "当前部门不存在！"),

    USER_BIND_MOBILE_SERVER_ERROR(90010, "用户绑定手机号服务异常！"),
    USER_BIND_ADD_USER_FAIL(90011, "用户绑定手机号-新增用户失败！"),
    USER_BIND_USER_ADD_TAG_FAIL(90012, "用户绑定手机号-用户添加标签失败！"),

    EXTERNAL_USER_ADD_FAIL(90013, "外部人员加入失败！"),
    EXTERNAL_USER_ADD_MOBILE_EXIST(90014, "手机号码已被绑定！"),
    BATCH_USER_EXEC(90015, "批量操作用户已经在处理中,请重新选择！"),

    //待办相关
    TODO_REPEAT_TYPE_NOT_EXIST(100001, "待办重复类型不存在！"),
    TODO_ALREADY_COMPLETE(100002, "待办已经完成！"),
    TODO_OP_MODE_NOT_EXIST(100003, "待办操作模式不存在！"),
    TODO_REPEAT_UNTIL_IS_NULL(100004, "待办重复结束时间不能为空！"),
    TODO_START_AFTER_END(100005, "待办开始时间不能晚于结束时间！"),
    TODO_END_AFTER_REPEAT_UTIL(100006, "待办结束时间不能晚于重复结束时间！"),
    TODO_REPEAT_INTERVAL_MUST_GE_1(100007, "待办重复间隔必须大于等于1！"),
    TODO_REPEAT_UTIL_IS_NULL(100008, "待办为重复时，重复结束时间不能为空！"),
    TODO_OP_START_TIME_NOT_MATCH(100009, "操作开始时间必须匹配重复待办明细的开始时间！"),
    TODO_REMIND_TIME_DIFFS_IS_NULL(100010, "日程开始前多少秒提醒不能为空！"),
    TODO_REPEAT_TYPE_IS_NULL(100011, "待办为重复时，重复类型不能为空！"),
    TODO_REPEAT_DAY_OF_WEEK(100012, "重复类型为每周时，每周周几重复不能为空！"),
    TODO_REPEAT_DAY_OF_MONTH(100013, "重复类型为每月时，每月哪几天重复不能为空！"),
    TODO_USER_LIST_IS_NULL(100014, "用户id列表不能为空！"),
    TODO_NAME_ADD_INVALID(100015, "名称不能为空且长度不能大于500！"),
    TODO_NAME_UPDATE_INVALID(100016, "名称长度不能大于50！"),
    TODO_DESCRIPTION_INVALID(100017, "描述长度不能大于500！"),
    TODO_START_TIME_BEFORE_OP_START_TIME(100018, "开始时间不得早于操作开始时间！"),
    TODO_REPEAT_INTERVAL_IS_NULL(100020, "待办为重复时，重复间隔不能为空！"),
    TODO_NOT_EXIST(100021, "待办不存在！"),
    TODO_REPEAT_TIME_PARAM_ERROR(100022, "重复类型待办对应的相关时间参数有误，生成待办失败！"),
    TODO_FLOW_NODE_NOT_EXIST(100023, "待办流程节点不存在"),
    TODO_FLOW_SERIAL_NO_EXIST(100024, "流水号已存在"),
    TODO_FLOW_NOT_EXIST(100025, "待办流程不存在"),
    TODO_OPERATE_TYPE_NOT_EXIST(100026, "操作类型不存在"),
    TODO_TYPE_INVALID(100027, "待办类型长度不能大于30"),
    TODO_CREATE_TIME_INVALID(100028, "待办事项创建时间不能为空！"),
    TODO_START_TIME_INVALID(100029, "待办任务开始时间不能为空！"),
    TODO_USER_TODO_NOT_EXIST(100030, "用户待办事项不存在！"),
    TODO_USER_TODO_RETRY(100031, "存入和查询的数据不一致 触发重试！"),

    // 消息中心
    MSG_NOT_EXIST(110000, "消息不存在！"),
    MSG_EXIST(110001, "消息已存在！"),
    MSG_SEND_SCOPE_EMPTY(110002, "消息接收范围不能为空！"),
    MSG_SEND_QW_FAIL(110003, "发送消息到企业微信失败！"),
    MSG_SEND_USER_MAX(110004, "消息发送用户最多不能超过10000个！"),
    MSG_SEND_FAIL(110005, "消息发送失败：%s！"),

    //监控相关
    OPERATE_FAIL(120000, "操作失败"),
    START_FAIL(120001, "启动失败"),
    STOP_FAIL(120002, "停止失败"),
    RESTART_FAIL(120003, "重启失败"),
    NOT_SUPPORT_OPERATION(120004, "不支持的操作类型"),
    UNKNOWN_NOTIFY_TYPE(120005, "未知的通知类型"),

    //API接入
    API_EXIST(130000, "接口已存在！"),
    API_NOT_EXIST(130001, "接口不存在！"),
    COMPANY_EXIST(130002, "服务商已存在！"),
    COMPANY_NOT_EXIST(130003, "服务商不存在！"),
    APP_EXIST(130004, "应用已存在！"),
    APP_NOT_EXIST(130005, "应用不存在！"),
    APP_SECRET_ERROR(130006, "keyId或appSecret错误！"),
    APP_DISABLE(130007, "应用已禁用！"),
    API_PATH_EXIST(130008, "URL路径已存在！"),
    APP_EXPIRED(130009, "应用已过期！"),
    COMPANY_RELATE_APP(130010, "当前服务商有相关应用，无法删除！"),
    COMPANY_NAME_EXIST(130011, "服务商名称已存在！"),

    ACCESS_TOKEN_NOT_EXIST(130011, "请先获取access_token！"),
    API_NOT_AUTH(1300012, "接口未授权！"),

    //访客
    VISITOR_RECORD_NOT_EXIST(140000, "记录不存在！"),
    VISITOR_RECORD_ONLY_CANCEL_PENDING(140001, "只能取消待审核的记录"),
    VISITOR_RECORD_ONLY_APPROVE_PENDING_REJECTED(140002, "只能审核通过待审核、已拒绝的记录"),
    VISITOR_RECORD_ONLY_REJECT_APPROVED(140003, "只能审核拒绝已通过的记录"),
    VISITOR_RECORD_ONLY_CANCEL_APPROVED(140004, "只能取消已通过的记录"),

    // 二次认证相关
    USERID_ALREADY_EXISTS(150000, "该身份证号已创建账号！"),
    PHONE_ALREADY_EXISTS(150001, "当前手机号已存在账号，请直接登录企业微信！"),
    NEW_ENROLLMENT_STU_NOT_EXIST(150002, "暂无相关数据，请确认输入信息后再试！"),

    // 身份通
    SFT_PHONE_CODE_ERROR(160000, "手机号绑定验证码错误或已过期！"),
    SFT_PARAMETER_ERROR(160001, "参数不能为空！"),
    SFT_GET_PHONE_ERROR(160002, "手机号获取失败，请重试！"),
    SFT_GET_USER_INFO_ERROR(160003, "查询企微用户失败！"),
    SFT_REPEAT_BINDING(160004, "当前工号已绑定此手机号，请勿重复绑定！"),
    SFT_PHONE_NOT_VERIFY(160005, "手机号未经过验证！"),
    SFT_GET_PHONE_CODE_ERROR(160006, "1分钟只能请求一次验证码！"),
    SFT_NOT_ALLOW_IDENTIFIER_CHECK(160007, "不允许使用身份证核验！"),

    SYSTEM_COMMON_ERROR(999, "%s"),
    ;


    private final int code;
    private String msg;

    ExceptionCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ServiceException build(String msg, Object... param) {
        return new ServiceException(String.format(msg, param), this.code);
    }

    public ServiceException param(Object... param) {
        return new ServiceException(String.format(msg, param), this.code);
    }

    public ServiceException toServiceException() {
        return new ServiceException(this);
    }

    public ServiceException toServiceException(String msg) {
        return new ServiceException(msg, this.code);
    }
}

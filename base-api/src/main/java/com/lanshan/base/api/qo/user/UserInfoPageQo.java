package com.lanshan.base.api.qo.user;

import com.lanshan.base.api.enums.YnEnum;
import com.lanshan.base.api.qo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "用户信息分页查询条件（根据实际情况选择传参）")
public class UserInfoPageQo extends PageQo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "部门id")
    private Long departmentId;

    @ApiModelProperty(value = "是否包含递归查询全部子部门人员：0，否；1，是")
    private Integer fetchChild = YnEnum.YES.value();

    @ApiModelProperty(value = "部门路径匹配，由departmentId转换而来", hidden = true)
    private String departmentIdPath;

    @ApiModelProperty(value = "部门路径匹配，由departmentName转换而来", hidden = true)
    private String departmentPath;

    @ApiModelProperty(value = "标签id")
    private Long tagid;

    @ApiModelProperty(value = "学工号(模糊查询)")
    private String userid;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "学工号(精确查询)", required = false)
    private String useridForExact;

    @ApiModelProperty(value = "手机号码")
    private String mobile;

    @ApiModelProperty(value = "职务信息")
    private String position;

    @ApiModelProperty(value = "激活状态")
    private Integer status;

    @ApiModelProperty(value = "学工号列表", hidden = true)
    private List<String> useridList;

    @ApiModelProperty(value = "排除状态，用于通讯录选择时排除指定的状态，不能同时与 status 使用")
    private List<Integer> excludeStatusList;

    @ApiModelProperty(value = "部门id列表,用于通讯录选择指定部门下的用户")
    private List<Long> deptIdList;

    @ApiModelProperty(value = "是否有手机号 0 无 1 有 不传默认全部")
    private String hasMobile;

    @ApiModelProperty(value = "需要排除的用户id列表")
    private Set<String> excludeUserids;

    @ApiModelProperty(value = "脱敏", hidden = true)
    private Boolean desensitization = true;
}

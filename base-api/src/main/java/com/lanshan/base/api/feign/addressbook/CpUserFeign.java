package com.lanshan.base.api.feign.addressbook;

import com.lanshan.base.api.constant.ServiceConstant;
import com.lanshan.base.api.feign.addressbook.fallback.CpUserFeignFallbackFactory;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.vo.user.UserInfoPartVO;
import com.lanshan.base.api.vo.user.UserInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotEmpty;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 调用企业微信通讯录用户的feign client
 */
@FeignClient(contextId = "CpUserFeign", name = ServiceConstant.COMMON_SERVICE_NAME,
        path = ServiceConstant.COMMON_ADDRESS_BOOK,
        fallbackFactory = CpUserFeignFallbackFactory.class)
public interface CpUserFeign {

    /**
     * 根据用户id列表获取成员用户列表
     *
     * @param useridList userId 集合
     * @return Result<List < UserInfoVo>> 用户信息
     */
    @PostMapping(value = "listUsersByUseridList")
    Result<List<UserInfoVo>> listUsersByUseridList(@RequestBody @Validated @NotEmpty List<String> useridList);

    /**
     * 根据userid获取用户信息
     *
     * @param userid 用户id
     * @return 用户信息
     */
    @GetMapping("getUserByUserid")
    Result<UserInfoVo> getUserByUserid(@RequestParam String userid);

    /**
     * 根据用户id列表获取成员用户列表
     *
     * @param userid userid 集合
     */
    @GetMapping("listCacheUserByUserids")
    Result<List<UserInfoVo>> listCacheUserByUserids(@RequestParam Collection<String> userid);

    /**
     * 获取所有已激活的身份为学生的用户id
     * <AUTHOR> yang.
     * @since 2025/4/28 11:34
     */
    @PostMapping(value = "getActiveUsers",produces = "application/json;charset=UTF-8")
    Result<Collection<UserInfoPartVO>> getActiveUsers();

    /**
     * 获取部门成员(仅当前部门)
     * <AUTHOR> yang.
     * @since 2025/4/29 17:12
     */
    @PostMapping(value = "listUsersByDepartmentid")
    Result<List<UserInfoVo>> listUsersByDepartmentid(@RequestParam Long departmentid);

    /**
     * 批量获取部门成员(仅当前部门)
     * <AUTHOR> yang.
     * @since 2025/4/29 17:12
     */
    @PostMapping(value = "listUsersByDeptIds")
    Result<List<UserInfoPartVO>> listUsersByDeptIds(@RequestBody List<Long> deptIds);
}

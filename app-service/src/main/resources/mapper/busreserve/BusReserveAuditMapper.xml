<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.busreserve.dao.BusReserveAuditMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.busreserve.po.BusReserveAudit">
                <id column="id" property="id" />
                <result column="user_id" property="userId" />
                <result column="user_name" property="userName" />
                <result column="phone" property="phone" />
                <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                user_id,
                user_name,
                phone,
                create_time
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_audit (
            id,
            user_id,
            user_name,
            phone,
            create_time
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userId},
                #{entity.userName},
                #{entity.phone},
                #{entity.createTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_audit (
            id,
            user_id,
            user_name,
            phone,
            create_time
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userId},
                #{entity.userName},
                #{entity.phone},
                #{entity.createTime}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                user_id = EXCLUDED.user_id,
                user_name = EXCLUDED.user_name,
                phone = EXCLUDED.phone,
                create_time = EXCLUDED.create_time
    </insert>


</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.busreserve.dao.BusReserveOperationLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.busreserve.po.BusReserveOperationLog">
                <id column="id" property="id" />
                <result column="business_type" property="businessType" />
                <result column="user_id" property="userId" />
                <result column="user_name" property="userName" />
                <result column="operation_before" property="operationBefore" />
                <result column="operation_after" property="operationAfter" />
                <result column="create_time" property="createTime" />
                <result column="info_id" property="infoId" />
                <result column="info_name" property="infoName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                business_type,
                user_id,
                user_name,
                operation_before,
                operation_after,
                create_time,
                info_id,
                info_name
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_operation_log (
            id,
            business_type,
            user_id,
            user_name,
            operation_before,
            operation_after,
            create_time,
            info_id,
            info_name
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.businessType},
                #{entity.userId},
                #{entity.userName},
                #{entity.operationBefore},
                #{entity.operationAfter},
                #{entity.createTime},
                #{entity.infoId},
                #{entity.infoName}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_operation_log (
            id,
            business_type,
            user_id,
            user_name,
            operation_before,
            operation_after,
            create_time,
            info_id,
            info_name
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.businessType},
                #{entity.userId},
                #{entity.userName},
                #{entity.operationBefore},
                #{entity.operationAfter},
                #{entity.createTime},
                #{entity.infoId},
                #{entity.infoName}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                business_type = EXCLUDED.business_type,
                user_id = EXCLUDED.user_id,
                user_name = EXCLUDED.user_name,
                operation_before = EXCLUDED.operation_before,
                operation_after = EXCLUDED.operation_after,
                create_time = EXCLUDED.create_time,
                info_id = EXCLUDED.info_id,
                info_name = EXCLUDED.info_name
    </insert>


</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.busreserve.dao.BusReserveOutsideUserRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord">
                <id column="id" property="id" />
                <result column="user_name" property="userName" />
                <result column="user_gender" property="userGender" />
                <result column="dept_id" property="deptId" />
                <result column="dept_name" property="deptName" />
                <result column="position" property="position" />
                <result column="audit_user_id" property="auditUserId" />
                <result column="audit_name" property="auditName" />
                <result column="reason" property="reason" />
                <result column="info_id" property="infoId" />
                <result column="info_name" property="infoName" />
                <result column="driver_user_id" property="driverUserId" />
                <result column="driver_name" property="driverName" />
                <result column="driver_phone" property="driverPhone" />
                <result column="reserve_status" property="reserveStatus" />
                <result column="start_time" property="startTime" />
                <result column="check_time" property="checkTime" />
                <result column="create_time" property="createTime" />
                <result column="halfway_location" property="halfwayLocation" />
                <result column="start_day" property="startDay" />
        <result column="phone" property="phone" />
        <result column="audit_phone" property="auditPhone" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                user_name,
                user_gender,
                dept_id,
                dept_name,
                position,
                audit_user_id,
                audit_name,
                reason,
                info_id,
                info_name,
                driver_user_id,
                driver_name,
                driver_phone,
                reserve_status,
                start_time,
                check_time,
                create_time,
                halfway_location,
                start_day,
                phone,
                audit_phone
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_outside_user_record (
            id,
            user_name,
            user_gender,
            dept_id,
            dept_name,
            position,
            audit_user_id,
            audit_name,
            reason,
            info_id,
            info_name,
            driver_user_id,
            driver_name,
            driver_phone,
            reserve_status,
            start_time,
            check_time,
            create_time,
            halfway_location,
            start_day,
            phone,
            audit_phone
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userName},
                #{entity.userGender},
                #{entity.deptId},
                #{entity.deptName},
                #{entity.position},
                #{entity.auditUserId},
                #{entity.auditName},
                #{entity.reason},
                #{entity.infoId},
                #{entity.infoName},
                #{entity.driverUserId},
                #{entity.driverName},
                #{entity.driverPhone},
                #{entity.reserveStatus},
                #{entity.startTime},
                #{entity.checkTime},
                #{entity.createTime},
                #{entity.halfwayLocation},
                #{entity.startDay},
                #{entity.phone},
                #{entity.auditPhone}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_outside_user_record (
            id,
            user_name,
            user_gender,
            dept_id,
            dept_name,
            position,
            audit_user_id,
            audit_name,
            reason,
            info_id,
            info_name,
            driver_user_id,
            driver_name,
            driver_phone,
            reserve_status,
            start_time,
            check_time,
            create_time,
            halfway_location,
            start_day,
            phone,
            audit_phone
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userName},
                #{entity.userGender},
                #{entity.deptId},
                #{entity.deptName},
                #{entity.position},
                #{entity.auditUserId},
                #{entity.auditName},
                #{entity.reason},
                #{entity.infoId},
                #{entity.infoName},
                #{entity.driverUserId},
                #{entity.driverName},
                #{entity.driverPhone},
                #{entity.reserveStatus},
                #{entity.startTime},
                #{entity.checkTime},
                #{entity.createTime},
                #{entity.halfwayLocation},
                #{entity.startDay},
                #{entity.phone},
                #{entity.auditPhone}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                user_name = EXCLUDED.user_name,
                user_gender = EXCLUDED.user_gender,
                dept_id = EXCLUDED.dept_id,
                dept_name = EXCLUDED.dept_name,
                position = EXCLUDED.position,
                audit_user_id = EXCLUDED.audit_user_id,
                audit_name = EXCLUDED.audit_name,
                reason = EXCLUDED.reason,
                info_id = EXCLUDED.info_id,
                info_name = EXCLUDED.info_name,
                driver_user_id = EXCLUDED.driver_user_id,
                driver_name = EXCLUDED.driver_name,
                driver_phone = EXCLUDED.driver_phone,
                reserve_status = EXCLUDED.reserve_status,
                start_time = EXCLUDED.start_time,
                check_time = EXCLUDED.check_time,
                create_time = EXCLUDED.create_time,
                halfway_location = EXCLUDED.halfway_location,
                start_day = EXCLUDED.start_day,
                phone = EXCLUDED.phone,
                audit_phone = EXCLUDED.audit_phone
    </insert>

    <update id="updateBusReserveOutsideUserRecord">
        UPDATE standard_app.bus_reserve_outside_user_record
        SET reserve_status = #{reserveStatus}
        WHERE id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getBusReserveOutsideUserRecordById"
            resultType="com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordVO">
        select br.id,br.user_name, br.user_gender, br.dept_id, br.dept_name, br.position, br.audit_user_id, br.audit_name,
               br.reason, br.info_id, br.info_name, br.driver_user_id, br.driver_name, br.driver_phone, br.reserve_status,
               br.start_time, br.check_time, br.create_time, br.halfway_location, br.start_day, br.phone,br.audit_phone,
               bn.start_location,bn.end_location
        from standard_app.bus_reserve_outside_user_record br
        LEFT JOIN standard_app.bus_reserve_info bn on bn.id = br.info_id
        where br.id = #{id}
    </select>

    <select id="getOutsideUserRecordByInfoAndDayAndUser"
            resultType="com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordVO">
        select br.id,br.user_name, br.user_gender, br.dept_id, br.dept_name, br.position, br.audit_user_id, br.audit_name,
               br.reason, br.info_id, br.info_name, br.driver_user_id, br.driver_name, br.driver_phone, br.reserve_status,
               br.start_time, br.check_time, br.create_time, br.halfway_location, br.start_day, br.phone,br.audit_phone,
               bn.start_location,bn.end_location
        from standard_app.bus_reserve_outside_user_record br
                 LEFT JOIN standard_app.bus_reserve_info bn on bn.id = br.info_id
        <where>
            <if test="infoId != null">
                and br.info_id = #{infoId}
            </if>
            <if test="startDay != null and startDay != ''">
                and br.start_day = #{startDay}
            </if>
            <if test="userIds != null and userIds.size() > 0">
                and br.phone in
                <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="reserveStatusNot != null">
                and br.reserve_status != #{reserveStatusNot}
            </if>
        </where>
    </select>

    <select id="getOutSideUserCountByInfoIdsAndReserveStatus" resultType="java.lang.Integer">
        select count(1)
        from standard_app.bus_reserve_outside_user_record
        where info_id = #{infoId} and start_day = #{startDay} and reserve_status = #{reserveStatus}
    </select>

    <select id="getBusReserveRecordByLocation"
            resultType="com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord">
        select br.id,br.user_name, br.user_gender, br.dept_id, br.dept_name, br.position, br.audit_user_id, br.audit_name,
               br.reason, br.info_id, br.info_name, br.driver_user_id, br.driver_name, br.driver_phone, br.reserve_status,
               br.start_time, br.check_time, br.create_time, br.halfway_location, br.start_day, br.phone,br.audit_phone,
               bn.start_location,bn.end_location
        from standard_app.bus_reserve_outside_user_record br
                 inner JOIN standard_app.bus_reserve_info bn on bn.id = br.info_id
        where
            br.info_id != #{infoId} and bn.start_location = #{startLocation} and bn.end_location = #{endLocation}
          and br.start_day = #{startDay} and  br.phone = #{userId} and br.reserve_status = #{reserveStatus}
    </select>

    <select id="getWaitRecordByDate" resultType="com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordVO">
        select br.id,br.user_name, br.user_gender, br.dept_id, br.dept_name, br.position, br.audit_user_id, br.audit_name,
               br.reason, br.info_id, br.info_name,br.start_day,br.start_time, bn.start_location,bn.end_location
        from standard_app.bus_reserve_outside_user_record br
                 inner JOIN standard_app.bus_reserve_info bn on bn.id = br.info_id
        where br.reserve_status = #{reserveStatus} and br.start_time &gt;= #{startTime} and br.start_time &lt;= #{endTime}
    </select>

    <select id="getTomorrowWaitRecord"
            resultType="com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordVO">
        select br.id,br.user_name, br.user_gender, br.dept_id, br.dept_name, br.position, br.audit_user_id, br.audit_name,
               br.reason, br.info_id, br.info_name,br.start_day,br.start_time, bn.start_location,bn.end_location
        from standard_app.bus_reserve_outside_user_record br
                 inner JOIN standard_app.bus_reserve_info bn on bn.id = br.info_id
        where br.reserve_status = #{reserveStatus} and br.start_day = #{tomorrow}
    </select>


</mapper>

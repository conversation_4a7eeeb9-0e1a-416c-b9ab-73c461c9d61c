<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.busreserve.dao.BusReserveRemindMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.busreserve.po.BusReserveRemind">
                <id column="id" property="id" />
                <result column="user_id" property="userId" />
                <result column="user_type" property="userType" />
                <result column="info_id" property="infoId" />
                <result column="create_time" property="createTime" />
                <result column="user_name" property="userName" />
                <result column="start_day" property="startDay" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                user_id,
                user_type,
                info_id,
                create_time,
                user_name,
                start_day
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_remind (
            id,
            user_id,
            user_type,
            info_id,
            create_time,
            user_name,
            start_day
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userId},
                #{entity.userType},
                #{entity.infoId},
                #{entity.createTime},
                #{entity.userName},
                #{entity.startDay}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_remind (
            id,
            user_id,
            user_type,
            info_id,
            create_time,
            user_name,
            start_day
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userId},
                #{entity.userType},
                #{entity.infoId},
                #{entity.createTime},
                #{entity.userName},
                #{entity.startDay}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                user_id = EXCLUDED.user_id,
                user_type = EXCLUDED.user_type,
                info_id = EXCLUDED.info_id,
                create_time = EXCLUDED.create_time,
                user_name = EXCLUDED.user_name,
                start_day = EXCLUDED.start_day
    </insert>


</mapper>

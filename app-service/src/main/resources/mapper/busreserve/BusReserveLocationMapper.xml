<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.busreserve.dao.BusReserveLocationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.busreserve.po.BusReserveLocation">
                <id column="id" property="id" />
                <result column="location" property="location" />
                <result column="create_time" property="createTime" />
                <result column="create_user_id" property="createUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                location,
                create_time,
                create_user_id
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_location (
            id,
            location,
            create_time,
            create_user_id
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.location},
                #{entity.createTime},
                #{entity.createUserId}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_location (
            id,
            location,
            create_time,
            create_user_id
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.location},
                #{entity.createTime},
                #{entity.createUserId}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                location = EXCLUDED.location,
                create_time = EXCLUDED.create_time,
                create_user_id = EXCLUDED.create_user_id
    </insert>


</mapper>

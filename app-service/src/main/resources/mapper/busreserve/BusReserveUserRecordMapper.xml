<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.busreserve.dao.BusReserveUserRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.busreserve.po.BusReserveUserRecord">
                <id column="id" property="id" />
                <result column="user_id" property="userId" />
                <result column="user_name" property="userName" />
                <result column="dept_id" property="deptId" />
                <result column="dept_name" property="deptName" />
                <result column="info_id" property="infoId" />
                <result column="info_name" property="infoName" />
                <result column="driver_user_id" property="driverUserId" />
                <result column="driver_name" property="driverName" />
                <result column="driver_phone" property="driverPhone" />
                <result column="replace_status" property="replaceStatus" />
                <result column="replace_user_id" property="replaceUserId" />
                <result column="replace_user_name" property="replaceUserName" />
                <result column="reserve_status" property="reserveStatus" />
                <result column="start_time" property="startTime" />
                <result column="check_time" property="checkTime" />
                <result column="create_time" property="createTime" />
                <result column="user_gender" property="userGender" />
                <result column="halfway_location" property="halfwayLocation" />
                <result column="start_day" property="startDay" />
        <result column="user_count" property="userCount" />
        <result column="change_count" property="changeCount" />
        <result column="replace_phone" property="replacePhone" />
        <result column="record_ids" property="recordIds" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>

    <resultMap id="BusReserveUserRecordVOMap" type="com.lanshan.app.busreserve.vo.BusReserveUserRecordVO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="dept_id" property="deptId" />
        <result column="dept_name" property="deptName" />
        <result column="info_id" property="infoId" />
        <result column="info_name" property="infoName" />
        <result column="driver_user_id" property="driverUserId" />
        <result column="driver_name" property="driverName" />
        <result column="driver_phone" property="driverPhone" />
        <result column="replace_status" property="replaceStatus" />
        <result column="replace_user_id" property="replaceUserId" />
        <result column="replace_user_name" property="replaceUserName" />
        <result column="reserve_status" property="reserveStatus" />
        <result column="start_time" property="startTime" />
        <result column="check_time" property="checkTime" />
        <result column="create_time" property="createTime" />
        <result column="user_gender" property="userGender" />
        <result column="halfway_location" property="halfwayLocation" />
        <result column="start_day" property="startDay" />
        <result column="user_count" property="userCount" />
        <result column="change_count" property="changeCount" />
        <result column="replace_phone" property="replacePhone" />
        <result column="record_ids" property="recordIds" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="start_location" property="startLocation" />
        <result column="end_location" property="endLocation" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                user_id,
                user_name,
                dept_id,
                dept_name,
                info_id,
                info_name,
                driver_user_id,
                driver_name,
                driver_phone,
                replace_status,
                replace_user_id,
                replace_user_name,
                reserve_status,
                start_time,
                check_time,
                create_time,
                user_gender,
                halfway_location,
                start_day,
                user_count,
                change_count,
                replace_phone,
                record_ids
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_user_record (
            id,
            user_id,
            user_name,
            dept_id,
            dept_name,
            info_id,
            info_name,
            driver_user_id,
            driver_name,
            driver_phone,
            replace_status,
            replace_user_id,
            replace_user_name,
            reserve_status,
            start_time,
            check_time,
            create_time,
            user_gender,
            halfway_location,
            start_day,
            user_count,
            change_count,
        replace_phone,
        record_ids
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userId},
                #{entity.userName},
                #{entity.deptId},
                #{entity.deptName},
                #{entity.infoId},
                #{entity.infoName},
                #{entity.driverUserId},
                #{entity.driverName},
                #{entity.driverPhone},
                #{entity.replaceStatus},
                #{entity.replaceUserId},
                #{entity.replaceUserName},
                #{entity.reserveStatus},
                #{entity.startTime},
                #{entity.checkTime},
                #{entity.createTime},
                #{entity.userGender},
                #{entity.halfwayLocation},
                #{entity.startDay},
                #{entity.userCount},
                #{entity.changeCount},
                #{entity.replacePhone},
                #{entity.recordIds,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_user_record (
            id,
            user_id,
            user_name,
            dept_id,
            dept_name,
            info_id,
            info_name,
            driver_user_id,
            driver_name,
            driver_phone,
            replace_status,
            replace_user_id,
            replace_user_name,
            reserve_status,
            start_time,
            check_time,
            create_time,
            user_gender,
            halfway_location,
            start_day,
            user_count,
            change_count,
            replace_phone,
            record_ids
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.userId},
                #{entity.userName},
                #{entity.deptId},
                #{entity.deptName},
                #{entity.infoId},
                #{entity.infoName},
                #{entity.driverUserId},
                #{entity.driverName},
                #{entity.driverPhone},
                #{entity.replaceStatus},
                #{entity.replaceUserId},
                #{entity.replaceUserName},
                #{entity.reserveStatus},
                #{entity.startTime},
                #{entity.checkTime},
                #{entity.createTime},
                #{entity.userGender},
                #{entity.halfwayLocation},
                #{entity.startDay},
                #{entity.userCount},
                #{entity.changeCount},
                #{entity.replacePhone},
                #{entity.recordIds}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                user_id = EXCLUDED.user_id,
                user_name = EXCLUDED.user_name,
                dept_id = EXCLUDED.dept_id,
                dept_name = EXCLUDED.dept_name,
                info_id = EXCLUDED.info_id,
                info_name = EXCLUDED.info_name,
                driver_user_id = EXCLUDED.driver_user_id,
                driver_name = EXCLUDED.driver_name,
                driver_phone = EXCLUDED.driver_phone,
                replace_status = EXCLUDED.replace_status,
                replace_user_id = EXCLUDED.replace_user_id,
                replace_user_name = EXCLUDED.replace_user_name,
                reserve_status = EXCLUDED.reserve_status,
                start_time = EXCLUDED.start_time,
                check_time = EXCLUDED.check_time,
                create_time = EXCLUDED.create_time,
                user_gender = EXCLUDED.user_gender,
                halfway_location = EXCLUDED.halfway_location,
                start_day = EXCLUDED.start_day,
                user_count = EXCLUDED.user_count,
                change_count = EXCLUDED.change_count,
                replace_phone = EXCLUDED.replace_phone,
                record_ids = EXCLUDED.record_ids
    </insert>

    <insert id="changeReserveBatch">
        INSERT INTO standard_app.bus_reserve_user_record (
        id,
        info_id,
        info_name,
        driver_user_id,
        driver_name,
        driver_phone,
        start_time,
        change_count,
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
            #{entity.id},
            #{entity.infoId},
            #{entity.infoName},
            #{entity.driverUserId},
            #{entity.driverName},
            #{entity.driverPhone},
            #{entity.startTime},
            #{entity.changeCount}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
        info_id = EXCLUDED.info_id,
        info_name = EXCLUDED.info_name,
        driver_user_id = EXCLUDED.driver_user_id,
        driver_name = EXCLUDED.driver_name,
        driver_phone = EXCLUDED.driver_phone,
        start_time = EXCLUDED.start_time,
        change_count = EXCLUDED.change_count
    </insert>

    <update id="updateBusReserveUserRecord">
        UPDATE standard_app.bus_reserve_user_record
        SET reserve_status = #{reserveStatus}
        WHERE id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="getBusReserveUserRecordById"
            resultMap="BusReserveUserRecordVOMap">
        select br.id, br.user_id, br.user_name, br.dept_id, br.dept_name, br.info_id, br.info_name, br.driver_user_id,
               br.driver_name, br.driver_phone, br.replace_status, br.replace_user_id, br.replace_user_name, br.reserve_status,
               br.start_time, br.check_time, br.create_time, br.user_gender, br.halfway_location, br.start_day, br.user_count,br.change_count,br.replace_phone,br.record_ids,
               bn.start_location,bn.end_location
        from  standard_app.bus_reserve_user_record br
        LEFT JOIN standard_app.bus_reserve_info bn on bn.id = br.info_id
        where br.id = #{id}


    </select>

    <select id="getBusReserveUserRecordByInfoIdAndStartTime"
            resultMap="BusReserveUserRecordVOMap">
        select br.id, br.user_id, br.user_name, br.dept_id, br.dept_name, br.info_id, br.info_name, br.driver_user_id,
               br.driver_name, br.driver_phone, br.replace_status, br.replace_user_id, br.replace_user_name, br.reserve_status,
               br.start_time, br.check_time, br.create_time, br.user_gender, br.halfway_location, br.start_day, br.user_count,br.change_count,br.replace_phone,br.record_ids,
               bn.start_location,bn.end_location
        from  standard_app.bus_reserve_user_record br
        LEFT JOIN standard_app.bus_reserve_info bn on bn.id = br.info_id
        <where>
            <if test="infoId != null">
                and br.info_id = #{infoId}
            </if>
            <if test="startTimeStr != null and startTimeStr != ''">
                and to_char(br.start_time, 'YYYY-MM-DD HH24:MI:SS') = #{startTimeStr}
            </if>
            <if test="replaceUserId != null and replaceUserId != '' ">
                and br.replace_user_id = #{replaceUserId}
            </if>
        </where>

    </select>

    <select id="getUserRecordByInfoAndDayAndUser"
            resultMap="BusReserveUserRecordVOMap">
        select br.id, br.user_id, br.user_name, br.dept_id, br.dept_name, br.info_id, br.info_name, br.driver_user_id,
               br.driver_name, br.driver_phone, br.replace_status, br.replace_user_id, br.replace_user_name, br.reserve_status,
               br.start_time, br.check_time, br.create_time, br.user_gender, br.halfway_location, br.start_day, br.user_count,br.change_count,br.replace_phone,br.record_ids,
               bn.start_location,bn.end_location
        from  standard_app.bus_reserve_user_record br
                  LEFT JOIN standard_app.bus_reserve_info bn on bn.id = br.info_id
        <where>
            <if test="infoId != null">
                and br.info_id = #{infoId}
            </if>
            <if test="startDay != null and startDay != ''">
                and br.start_day = #{startDay}
            </if>
            <if test="userIds != null and userIds.size() > 0">
                and br.user_id in
                <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="reserveStatusNot != null">
                and br.reserve_status != #{reserveStatusNot}
            </if>
        </where>
    </select>

    <select id="getUserCountByInfoIdsAndReserveStatus" resultType="java.lang.Integer">
        select count(1)
        from standard_app.bus_reserve_user_record
        where info_id = #{infoId} and start_day = #{startDay} and reserve_status = #{reserveStatus}
    </select>

    <select id="getBusReserveRecordByLocation" resultMap="BusReserveUserRecordVOMap">
        select
            br.id, br.user_id, br.user_name, br.dept_id, br.dept_name, br.info_id, br.info_name, br.driver_user_id,
            br.driver_name, br.driver_phone, br.replace_status, br.replace_user_id, br.replace_user_name, br.reserve_status,
            br.start_time, br.check_time, br.create_time, br.user_gender, br.halfway_location, br.start_day, br.user_count,br.change_count,br.replace_phone,br.record_ids
        from standard_app.bus_reserve_user_record br
        inner JOIN standard_app.bus_reserve_info bn on bn.id = br.info_id
        where
            br.info_id != #{infoId} and bn.start_location = #{startLocation} and bn.end_location = #{endLocation}
          and br.start_day = #{startDay} and  br.user_id = #{userId} and br.reserve_status = #{reserveStatus}
    </select>

    <select id="getWaitRecordByDate" resultMap="BusReserveUserRecordVOMap">
        select
            br.id, br.user_id, br.user_name, br.dept_id, br.dept_name, br.info_id, br.info_name,br.start_day,br.start_time,br.halfway_location,bn.start_location,bn.end_location
        from standard_app.bus_reserve_user_record br
            inner JOIN standard_app.bus_reserve_info bn on bn.id = br.info_id
        where br.reserve_status = #{reserveStatus} and br.start_time &gt;= #{startTime} and br.start_time &lt;= #{endTime}
    </select>

    <select id="getTomorrowWaitRecord" resultMap="BusReserveUserRecordVOMap">
        select
            br.id, br.user_id, br.user_name, br.dept_id, br.dept_name, br.info_id, br.info_name,br.start_day,br.start_time,br.halfway_location,bn.start_location,bn.end_location
        from standard_app.bus_reserve_user_record br
                 inner JOIN standard_app.bus_reserve_info bn on bn.id = br.info_id
        where br.reserve_status = #{reserveStatus} and br.start_day = #{tomorrow}
    </select>
    <select id="getByIds" resultMap="BusReserveUserRecordVOMap">
        select <include refid="Base_Column_List"/>
            from standard_app.bus_reserve_user_record
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


</mapper>

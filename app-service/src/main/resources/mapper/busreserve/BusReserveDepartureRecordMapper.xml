<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.busreserve.dao.BusReserveDepartureRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lanshan.app.busreserve.po.BusReserveDepartureRecord">
                <id column="id" property="id" />
                <result column="info_id" property="infoId" />
                <result column="info_name" property="infoName" />
                <result column="driver_user_id" property="driverUserId" />
                <result column="driver_name" property="driverName" />
                <result column="driver_phone" property="driverPhone" />
                <result column="license_plate" property="licensePlate" />
                <result column="license_plate_today" property="licensePlateToday" />
                <result column="driver_user_id_today" property="driverUserIdToday" />
                <result column="driver_name_today" property="driverNameToday" />
                <result column="driver_phone_today" property="driverPhoneToday" />
                <result column="start_time" property="startTime" />
                <result column="user_count" property="userCount" />
                <result column="normal_count" property="normalCount" />
                <result column="reserve_not_sit_count" property="reserveNotSitCount" />
                <result column="sit_not_reserve_count" property="sitNotReserveCount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
                id,
                info_id,
                info_name,
                driver_user_id,
                driver_name,
                driver_phone,
                license_plate,
                license_plate_today,
                driver_user_id_today,
                driver_name_today,
                driver_phone_today,
                start_time,
                user_count,
                normal_count,
                reserve_not_sit_count,
                sit_not_reserve_count
    </sql>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_departure_record (
            id,
            info_id,
            info_name,
            driver_user_id,
            driver_name,
            driver_phone,
            license_plate,
            license_plate_today,
            driver_user_id_today,
            driver_name_today,
            driver_phone_today,
            start_time,
            user_count,
            normal_count,
            reserve_not_sit_count,
            sit_not_reserve_count
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.infoId},
                #{entity.infoName},
                #{entity.driverUserId},
                #{entity.driverName},
                #{entity.driverPhone},
                #{entity.licensePlate},
                #{entity.licensePlateToday},
                #{entity.driverUserIdToday},
                #{entity.driverNameToday},
                #{entity.driverPhoneToday},
                #{entity.startTime},
                #{entity.userCount},
                #{entity.normalCount},
                #{entity.reserveNotSitCount},
                #{entity.sitNotReserveCount}
            )
        </foreach>
    </insert>

    <!-- 批量插入或更新 (PostgreSQL语法) -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO standard_app.bus_reserve_departure_record (
            id,
            info_id,
            info_name,
            driver_user_id,
            driver_name,
            driver_phone,
            license_plate,
            license_plate_today,
            driver_user_id_today,
            driver_name_today,
            driver_phone_today,
            start_time,
            user_count,
            normal_count,
            reserve_not_sit_count,
            sit_not_reserve_count
        ) VALUES
        <foreach collection="entities" item="entity" separator=",">
            (
                #{entity.id},
                #{entity.infoId},
                #{entity.infoName},
                #{entity.driverUserId},
                #{entity.driverName},
                #{entity.driverPhone},
                #{entity.licensePlate},
                #{entity.licensePlateToday},
                #{entity.driverUserIdToday},
                #{entity.driverNameToday},
                #{entity.driverPhoneToday},
                #{entity.startTime},
                #{entity.userCount},
                #{entity.normalCount},
                #{entity.reserveNotSitCount},
                #{entity.sitNotReserveCount}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
                info_id = EXCLUDED.info_id,
                info_name = EXCLUDED.info_name,
                driver_user_id = EXCLUDED.driver_user_id,
                driver_name = EXCLUDED.driver_name,
                driver_phone = EXCLUDED.driver_phone,
                license_plate = EXCLUDED.license_plate,
                license_plate_today = EXCLUDED.license_plate_today,
                driver_user_id_today = EXCLUDED.driver_user_id_today,
                driver_name_today = EXCLUDED.driver_name_today,
                driver_phone_today = EXCLUDED.driver_phone_today,
                start_time = EXCLUDED.start_time,
                user_count = EXCLUDED.user_count,
                normal_count = EXCLUDED.normal_count,
                reserve_not_sit_count = EXCLUDED.reserve_not_sit_count,
                sit_not_reserve_count = EXCLUDED.sit_not_reserve_count
    </insert>


</mapper>

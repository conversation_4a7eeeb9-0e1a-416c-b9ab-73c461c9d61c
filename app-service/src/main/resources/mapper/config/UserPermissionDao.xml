<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lanshan.app.config.dao.UserPermissionDao">

    <!-- 根据用户ID查询用户拥有的所有权限 -->
    <select id="selectUserPermissions" parameterType="Long" resultType="String">
        select distinct m.perms
        from sys_menu m
                 left join sys_role_menu rm on m.id = rm.menu_id
                 left join sys_user_role ur on rm.role_id = ur.role_id
                 left join sys_role r on r.role_id = ur.role_id
        where ur.user_id = #{userId}
          and m.perms is not null
          and m.perms != ''
          and r.status = '0'
    </select>

</mapper>
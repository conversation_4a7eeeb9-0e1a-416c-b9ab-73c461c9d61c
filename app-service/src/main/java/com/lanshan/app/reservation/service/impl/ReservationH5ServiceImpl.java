package com.lanshan.app.reservation.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.app.common.bo.PageQo;
import com.lanshan.app.common.constant.CommonConstant;
import com.lanshan.app.common.enums.ExceptionCodeEnum;
import com.lanshan.app.common.service.ISysConfigService;
import com.lanshan.app.common.utils.RedisService;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.app.reservation.converter.RsvReserveRecordConverter;
import com.lanshan.app.reservation.dto.NoticeSearchDTO;
import com.lanshan.app.reservation.entity.RsvReserveObject;
import com.lanshan.app.reservation.entity.RsvReserveRecord;
import com.lanshan.app.reservation.enums.ReserveStatusEnum;
import com.lanshan.app.reservation.service.*;
import com.lanshan.app.reservation.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 用户移动端预约服务API实现
 *
 * <AUTHOR>
 */
@Service("reservationH5Service")
@RequiredArgsConstructor
@Slf4j
public class ReservationH5ServiceImpl implements ReservationH5Service {

    private final RsvBannerService rsvBannerService;

    private final RsvVenueService rsvVenueService;

    private final RsvNoticeService rsvNoticeService;

    private final RsvReserveRecordService rsvReserveRecordService;

    private final RsvReserveObjectService rsvReserveObjectService;

    private final RsvBlacklistService rsvBlacklistService;

    private final ISysConfigService sysConfigService;

    private final RedisService redisService;

    @Override
    public List<RsvBannerVO> getBannerInfo() {
        return rsvBannerService.getEnableBannerList();
    }

    @Override
    public List<RsvVenueVO> getVenueInfo() {
        List<RsvVenueVO> openVenueList = rsvVenueService.getOpenVenueList();
        return openVenueList.stream()
                .filter(item -> Objects.equals(item.getIsOpen(), true))
                .collect(Collectors.toList());
    }

    /**
     * 团队预约获取场馆信息
     *
     * @return 场馆信息
     */
    @Override
    public List<RsvVenueVO> getVenueInfoForTeam() {
        List<RsvVenueVO> openVenueList = rsvVenueService.getOpenVenueList();

        return openVenueList.stream()
                .filter(item -> Objects.equals(item.getIsTeamOpen(), true))
                .collect(Collectors.toList());
    }

    @Override
    public List<RsvNoticeVO> getNoticeInfo() {
        return rsvNoticeService.getRecentList();
    }

    /**
     * 获取分页公告信息
     *
     * @param page 分页参数
     * @return 分页公告信息
     */
    @Override
    public IPage<RsvNoticeVO> getNoticeInfo(PageQo page) {
        return rsvNoticeService.getPage(new NoticeSearchDTO(page));
    }

    /**
     * 获取我的最近预约记录
     *
     * @return 我的最近预约记录
     */
    @Override
    public List<RsvReserveRecordVO> getMyRecentReserveRecord() {
        return rsvReserveRecordService.getMyRecentReserveRecord();
    }

    /**
     * 获取分页预约记录
     *
     * @param page 分页参数
     * @return 分页预约记录
     */
    @Override
    public IPage<RsvReserveRecordVO> getMyReserveRecord(PageQo page) {
        return rsvReserveRecordService.getMyReserveRecordPage(page);
    }

    /**
     * 获取可预约场馆类型列表
     *
     * @return 可预约场馆类型列表
     */
    @Override
    public List<ReserveTypeVO> getReserveType(Serializable id) {
        return rsvReserveObjectService.getAvailableCountByType(id);
    }

    /**
     * 获取可预约场地列表
     *
     * @param id   场馆ID
     * @param type 场地类型
     * @param date 日期
     * @return 可预约场地列表
     */
    @Override
    public List<RsvObjectGroupVO> getReserveObjectList(Serializable id, Long type, String date) {
        return rsvReserveObjectService.getAvailableObjectList(id, type, date);
    }

    /**
     * 用户创建预约。主要流程如下：
     * 检查预约场地信息，预约场地是否当天时间段开放；
     * 检查预约场地余位是否有剩余。如果没有剩余，则提示用户预约失败，请选择其他时间段；
     * 检查当前时间是否在可预约时间范围内。如果不在，则提示用户预约失败，请选择其他时间段；
     * 检查当前用户是否在黑名单。如果在，则提示用户在黑名单中，不允许预约；
     * 检查用户当日预约次数是否达到上限。如果达到，则提示用户已达到当日预约次数上限，不允许预约；
     * 检查当前用户是否达到本周预约次数上限。如果达到，则提示用户已达到本周预约次数上限，不允许预约；
     * 为该用户锁定预约对象库存，创建预约记录，预约对象库存减 1。
     *
     * @param id 预约对象ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createReservation(Long id) {

        //预约对象
        RsvReserveObject reserveObject = rsvReserveObjectService.getById(id);

        // 检查当前时间是否在可预约时间范围内。如果不在，则提示用户预约失败，请选择其他时间段；
        if (reserveObject == null || !reserveObject.getIsEnable()) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("暂无可预约时间段！");
        }

        // 检查场地余位数量
        if (reserveObject.getRemainCount() < 1) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("预约场地余位不足！");
        }

        //检查当前时间是否在可预约时间范围内
        String currentFullTime = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_MINUTE_FORMAT);
        log.info("当前时间：{}", currentFullTime);
        log.info("预约对象结束时间：{}", reserveObject.getFullEndTime());
        if (currentFullTime.compareTo(reserveObject.getFullEndTime()) > 0) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("当前时间不在可预约时间范围内，请选择其他时间段！");
        }

        // 当前用户ID
        String userId = SecurityContextHolder.getUserIdStr();
        //检查当前用户是否在黑名单。如果在，则提示用户在黑名单中，不允许预约；
        if (rsvBlacklistService.isExist(userId)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("当前用户在黑名单中，不允许预约！");
        }

        // 检查用户预约次数限制
        checkUserReserveLimit(userId, reserveObject);

        //保存用户的预约记录
        RsvReserveRecord record = buildReserveRecord(userId, reserveObject);
        rsvReserveRecordService.save(record);

        //更新预约对象库存
        LambdaUpdateWrapper<RsvReserveObject> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RsvReserveObject::getId, id)
                .eq(RsvReserveObject::getRemainCount, reserveObject.getRemainCount())
                .set(RsvReserveObject::getRemainCount, reserveObject.getRemainCount() - 1);
        boolean updateSuccess = rsvReserveObjectService.update(updateWrapper);

        if (!updateSuccess) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("预约冲突, 请重试！");
        }

        return Boolean.TRUE;
    }

    /**
     * 构建预约记录
     *
     * @param userId        用户ID
     * @param reserveObject 预约对象
     * @return 预约记录
     */
    @NotNull
    private RsvReserveRecord buildReserveRecord(String userId, RsvReserveObject reserveObject) {
        RsvReserveRecord record = new RsvReserveRecord();
        record.setUserId(userId);
        record.setUserName(rsvReserveObjectService.getUserNameFromAddressBook(userId));
        record.setVenueId(reserveObject.getVenueId());
        record.setObjectId(reserveObject.getId());
        record.setFieldId(reserveObject.getFieldId());
        record.setFieldName(reserveObject.getFieldName());
        record.setVenueName(reserveObject.getVenueName());
        record.setUseDate(reserveObject.getOpenDay());
        record.setUseTime(reserveObject.getOpenTime());
        record.setStatus(ReserveStatusEnum.RESERVED.getCode());
        record.setCreateTime(new Date());
        return record;
    }

    private void checkUserReserveLimit(String userId, RsvReserveObject reserveObject) {
        String dayLimit = sysConfigService.selectConfigByKey(CommonConstant.RSV_DAY_LIMIT_KEY);
        if (StringUtils.isNotBlank(dayLimit)) {
            // 检查当前用户当日预约次数是否达到上限。如果达到，则提示用户已达到当日预约次数上限，不允许预约；
            Integer dayLimitCount = Integer.valueOf(dayLimit);
            Integer dayCount = rsvReserveRecordService.getDayCount(userId, reserveObject.getOpenDay());
            if (dayCount >= dayLimitCount) {
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("当前用户当日预约次数已达到上限，不允许预约！");
            }
        }

        String weekLimit = sysConfigService.selectConfigByKey(CommonConstant.RSV_WEEK_LIMIT_KEY);
        if (StringUtils.isNotBlank(weekLimit)) {
            // 检查当前用户当日预约次数是否达到上限。如果达到，则提示用户已达到当日预约次数上限，不允许预约；
            Integer weekLimitCount = Integer.valueOf(weekLimit);
            Integer weekCount = rsvReserveRecordService.getWeekCount(userId, reserveObject.getOpenDay());
            if (weekCount >= weekLimitCount) {
                throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("当前用户本周预约次数已达到上限，不允许预约！");
            }
        }
    }

    /**
     * 取消预约
     * 1. 检查当前时间是否在可取消时间范围内。如果不在，则提示用户无法取消预约；
     * 2. 检查当前用户是否是预约记录的创建者。如果不同，则提示用户无操作权限；
     * 3. 为该用户解锁预约对象库存，删除预约记录，预约对象库存加 1。
     *
     * @param id 预约记录ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelReservation(Long id) {
        RsvReserveRecord rsvReserveRecord = rsvReserveRecordService.getById(id);

        if (rsvReserveRecord == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("预约记录不存在");
        }

        // 检查当前用户是否是预约记录的创建者
        if (!rsvReserveRecord.getUserId().equals(SecurityContextHolder.getUserIdStr())) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("无操作权限");
        }

        // 为该用户解锁预约对象库存，更新预约记录状态，预约对象库存加 1
        rsvReserveRecord.setStatus(ReserveStatusEnum.CANCEL.getCode());
        rsvReserveRecord.setUpdateTime(new Date());
        rsvReserveRecordService.updateById(rsvReserveRecord);

        RsvReserveObject reserveObject = rsvReserveObjectService.getById(rsvReserveRecord.getObjectId());
        LambdaUpdateWrapper<RsvReserveObject> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RsvReserveObject::getId, rsvReserveRecord.getObjectId())
                .eq(RsvReserveObject::getRemainCount, reserveObject.getRemainCount())
                .set(RsvReserveObject::getRemainCount, reserveObject.getRemainCount() + 1);
        boolean updateSuccess = rsvReserveObjectService.update(updateWrapper);

        if (!updateSuccess) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.param("取消预约失败");
        }

        return Boolean.TRUE;
    }

    /**
     * 获取用户可预约日期列表
     *
     * @return 用户可预约日期列表
     */
    @Override
    public List<UserAvailableDateVO> getUserAvailableDate(Serializable id) {
        return rsvReserveObjectService.getUserAvailableDate(id);
    }

    /**
     * 获取服务器时间ID
     *
     * @return 服务器时间ID
     */
    @Override
    public String getServerTimeId() {
        String serverTimeId = UUID.randomUUID().toString();
        redisService.setCacheObject(CommonConstant.SERVER_TIME_ID_KEY + serverTimeId, Calendar.getInstance().getTimeInMillis(), 8L, TimeUnit.HOURS);
        return serverTimeId;
    }

    /**
     * 签到记录
     *
     * @param serverTimeId 服务器时间ID
     * @return 签到记录
     */
    @Override
    public RsvReserveRecordVO signRecord(String serverTimeId) {

        Long serverTime = redisService.getCacheObject(CommonConstant.SERVER_TIME_ID_KEY + serverTimeId);
        if (serverTime == null) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("签到时间无效!");
        }
        String userId = SecurityContextHolder.getUserIdStr();
        Date signDate = new Date(serverTime);
        LambdaQueryWrapper<RsvReserveRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RsvReserveRecord::getUserId, userId);
        wrapper.in(RsvReserveRecord::getStatus, ReserveStatusEnum.RESERVED.getCode(), ReserveStatusEnum.USED.getCode());
        wrapper.eq(RsvReserveRecord::getUseDate, DatePattern.NORM_DATE_FORMAT.format(signDate));
        List<RsvReserveRecord> list = rsvReserveRecordService.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("没有场馆预约需要签到!");
        }

        for (RsvReserveRecord rsvReserveRecord : list) {
            Pair<DateTime, DateTime> timePair = rsvReserveRecord.getSignTimePeriod();
            if (timePair.getLeft().compareTo(signDate) < 0
                    && timePair.getRight().compareTo(signDate) > 0) {
                if (Objects.equals(rsvReserveRecord.getStatus(), ReserveStatusEnum.USED.getCode())) {
                    return RsvReserveRecordConverter.INSTANCE.toVO(rsvReserveRecord);
                } else if (Objects.equals(rsvReserveRecord.getStatus(), ReserveStatusEnum.RESERVED.getCode())) {
                    rsvReserveRecord.setStatus(ReserveStatusEnum.USED.getCode());
                    rsvReserveRecordService.updateById(rsvReserveRecord);
                    return RsvReserveRecordConverter.INSTANCE.toVO(rsvReserveRecord);
                }
            }
        }
        throw ExceptionCodeEnum.SYSTEM_COMMON_ERROR.toServiceException("不在签到时间范围！请稍后再试！");
    }

    @Override
    public List<RsvReserveRecordVO> listNeedSign() {
        String userId = SecurityContextHolder.getUserIdStr();
        Date signDate = new Date();
        LambdaQueryWrapper<RsvReserveRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RsvReserveRecord::getUserId, userId);
        wrapper.in(RsvReserveRecord::getStatus, ReserveStatusEnum.RESERVED.getCode(), ReserveStatusEnum.USED.getCode());
        wrapper.eq(RsvReserveRecord::getUseDate, DatePattern.NORM_DATE_FORMAT.format(signDate));
        List<RsvReserveRecord> list = rsvReserveRecordService.list(wrapper);
        return RsvReserveRecordConverter.INSTANCE.toVO(list);
    }

    @Override
    public Boolean signRecordV2(Long recordId) {
        RsvReserveRecord record = rsvReserveRecordService.getById(recordId);
        if (Objects.isNull(record)) {
            return false;
        }
        Date useDate = record.getUseDate();
        String useTime = record.getUseTime();
        String[] useTimeArray = useTime.split("-");
        String startUseTime = useTimeArray[0];
        String useDateStr = DateUtil.format(useDate, DatePattern.NORM_DATE_PATTERN);
        String useDateTimeStr = useDateStr + " " + startUseTime + ":00";
        DateTime useDateTime = DateUtil.parse(useDateTimeStr);
        DateTime useDateTimeStart = DateUtil.offsetMinute(useDateTime, -30);
        DateTime useDateTimeEnd = DateUtil.offsetMinute(useDateTime, 31);
        if (DateUtil.isIn(new Date(), useDateTimeStart, useDateTimeEnd)) {
            record.setStatus(ReserveStatusEnum.USED.getCode());
            rsvReserveRecordService.updateById(record);
            return true;
        }
        return false;
    }
}

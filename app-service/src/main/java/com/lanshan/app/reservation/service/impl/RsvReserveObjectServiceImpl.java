package com.lanshan.app.reservation.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.app.reservation.converter.RsvReserveObjectConverter;
import com.lanshan.app.reservation.dao.RsvFieldDao;
import com.lanshan.app.reservation.dao.RsvReserveObjectDao;
import com.lanshan.app.reservation.dao.RsvVenueDao;
import com.lanshan.app.reservation.dto.ReserveObjectSearchDTO;
import com.lanshan.app.reservation.dto.RsvObjectEnableDTO;
import com.lanshan.app.reservation.entity.RsvField;
import com.lanshan.app.reservation.entity.RsvReserveObject;
import com.lanshan.app.reservation.entity.RsvVenue;
import com.lanshan.app.reservation.entity.RsvVenueFieldType;
import com.lanshan.app.reservation.enums.FieldTypeEnum;
import com.lanshan.app.reservation.service.RsvReserveObjectService;
import com.lanshan.app.reservation.service.RsvVenueFieldTypeService;
import com.lanshan.app.reservation.vo.ReserveTypeVO;
import com.lanshan.app.reservation.vo.RsvObjectGroupVO;
import com.lanshan.app.reservation.vo.RsvReserveObjectVO;
import com.lanshan.app.reservation.vo.UserAvailableDateVO;
import com.lanshan.base.api.feign.addressbook.CpUserFeign;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.vo.user.UserInfoVo;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.IntegerCodec;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 预约对象信息表(RsvReserveObject)表服务实现类
 *
 * <AUTHOR>
 */
@Service("rsvReserveObjectService")
@RequiredArgsConstructor
public class RsvReserveObjectServiceImpl extends ServiceImpl<RsvReserveObjectDao, RsvReserveObject> implements RsvReserveObjectService {

    private static final String USER_TYPE_KEY = "address:book:user:type";
    // 默认用户类型为学生
    private static final int USER_TYPE_STUDENT = 2;
    // 默认用户类型为教师
    private static final int USER_TYPE_TEACHER = 1;
    private final RsvVenueDao rsvVenueDao;
    private final RsvFieldDao rsvFieldDao;
    private final CpUserFeign cpUserFeign;
    private final RedissonClient redissonClient;

    @Resource
    private RsvVenueFieldTypeService rsvVenueFieldTypeService;

    /**
     * 获取可用预约对象列表
     *
     * @param id   场馆ID
     * @param type 场地类型;1: 羽毛球；2：网球；3：篮球；4：乒乓球
     * @param date 日期
     * @return 预约对象列表
     */
    @Override
    public List<RsvObjectGroupVO> getAvailableObjectList(Serializable id, Long type, String date) {

        LambdaQueryWrapper<RsvReserveObject> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RsvReserveObject::getVenueId, id);
        wrapper.eq(RsvReserveObject::getFieldType, type);
        wrapper.eq(RsvReserveObject::getOpenDay, date);
        wrapper.eq(RsvReserveObject::getIsEnable, true);
        wrapper.orderByAsc(RsvReserveObject::getOpenTime);

        List<RsvReserveObject> list = super.list(wrapper);

        Map<String, List<RsvReserveObject>> objectMap =
                list.stream().collect(Collectors.groupingBy(RsvReserveObject::getFieldName));

        List<RsvObjectGroupVO> result = null;
        if (!objectMap.isEmpty()) {
            result = objectMap.entrySet().stream().map(entry -> {
                RsvObjectGroupVO vo = new RsvObjectGroupVO();
                vo.setFieldName(entry.getKey());
                vo.setReserveObjectList(RsvReserveObjectConverter.INSTANCE.toVO(entry.getValue()));
                return vo;
            }).collect(Collectors.toList());
            result.sort(Comparator.comparing(RsvObjectGroupVO::getFieldName));
        }

        return result;
    }

    /**
     * 获取可用预约对象数量
     *
     * @param id 场馆ID
     * @return 可用预约对象数量
     */
    @Override
    public List<ReserveTypeVO> getAvailableCountByType(Serializable id) {
        List<ReserveTypeVO> result = new ArrayList<>();

        LambdaQueryWrapper<RsvReserveObject> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RsvReserveObject::getVenueId, id);
        wrapper.ge(RsvReserveObject::getOpenDay, DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN));
        wrapper.eq(RsvReserveObject::getIsEnable, true);
        List<RsvReserveObject> list = super.list(wrapper);

        List<UserAvailableDateVO> userAvailableDateList = getUserAvailableDate(id);
        Set<String> availableDateSet = userAvailableDateList.stream().map(UserAvailableDateVO::getDate).collect(Collectors.toSet());

        // 过滤掉已过期的预约对象 和 当前用户不可以预约的日期
        String currentFullTime = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_MINUTE_FORMAT);
        List<RsvReserveObject> availableList = list.stream().filter(o -> currentFullTime.compareTo(o.getFullEndTime()) < 0
                && availableDateSet.contains(DateUtil.format(o.getOpenDay(), DatePattern.NORM_DATE_FORMAT))
        ).collect(Collectors.toList());

        // 统计每个类型的预约对象数量
        Map<Long, Integer> map = availableList.stream().collect(
                Collectors.groupingBy(RsvReserveObject::getFieldType, Collectors.summingInt(RsvReserveObject::getRemainCount))
        );
        //获取场地类型数据
        List<RsvVenueFieldType> venueFieldTypeList = rsvVenueFieldTypeService.list();
        Map<Long, RsvVenueFieldType> venueFieldTypeMap = venueFieldTypeList.stream().collect(Collectors
                .toMap(RsvVenueFieldType::getId, rsvVenueFieldType -> rsvVenueFieldType, (o, n) -> o));

        RsvVenue rsvVenue = rsvVenueDao.selectById(id);
        for (Long key : map.keySet()) {
            RsvVenueFieldType rsvVenueFieldType = venueFieldTypeMap.get(key);
            ReserveTypeVO reserveTypeVO = ReserveTypeVO.builder()
                    .reserveType(key)
                    .reserveTypeName(FieldTypeEnum.getDescByCode(key))
                    .reserveDateStart(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN))
                    .reserveDateEnd(DateUtil.format(DateUtil.offsetDay(new Date(), rsvVenue.getPreBookDay()), DatePattern.NORM_DATE_PATTERN))
                    .remainCount(map.get(key))
                    .build();
            if (Objects.nonNull(rsvVenueFieldType)) {
                reserveTypeVO.setReserveTypeName(rsvVenueFieldType.getName());
                reserveTypeVO.setFieldTypeIconUrl(rsvVenueFieldType.getIconUrl());
            }
            result.add(reserveTypeVO);
        }
        return result;
    }

    /**
     * 分页获取预约对象列表
     *
     * @param dto 查询条件
     * @return 预约对象列表
     */
    @Override
    public IPage<RsvReserveObjectVO> getPage(ReserveObjectSearchDTO dto) {
        Page<RsvReserveObject> page = new Page<>(dto.getPage(), dto.getSize());

        LambdaQueryWrapper<RsvReserveObject> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RsvReserveObject::getVenueId, dto.getId());
        wrapper.eq(dto.getFieldType() != null, RsvReserveObject::getFieldType, dto.getFieldType());
        wrapper.eq(StringUtils.isNotEmpty(dto.getDay()), RsvReserveObject::getOpenDay, dto.getDay());
        wrapper.eq(StringUtils.isNotEmpty(dto.getTime()), RsvReserveObject::getOpenTime, dto.getTime());
        wrapper.eq(dto.getFieldId() != null, RsvReserveObject::getFieldId, dto.getFieldId());
        wrapper.orderByAsc(RsvReserveObject::getId);
        Page<RsvReserveObject> pageData = super.page(page, wrapper);

        Map<Long, Integer> fieldLimitCountMap = getFieldLimitCountMap(dto.getId());
        IPage<RsvReserveObjectVO> result = pageData.convert(RsvReserveObjectConverter.INSTANCE::toVO);
        result.getRecords().forEach(vo -> vo.setUsedCount(Math.max(fieldLimitCountMap.getOrDefault(vo.getFieldId(), 0) - vo.getRemainCount(), 0)));

        return result;
    }

    /**
     * 启用/禁用预约对象
     *
     * @param id 预约对象ID
     * @return 是否成功
     */
    @Override
    public Boolean switchEnable(Serializable id) {

        RsvReserveObject rsvReserveObject = super.getById(id);
        if (rsvReserveObject != null) {
            rsvReserveObject.setIsEnable(!rsvReserveObject.getIsEnable());
            return super.updateById(rsvReserveObject);
        }
        return Boolean.TRUE;
    }

    /**
     * 批量启用/禁用预约对象
     *
     * @param dto 预约对象ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void switchEnable(RsvObjectEnableDTO dto) {
        if (dto.getIds() == null || dto.getIds().isEmpty()) {
            return;
        }

        LambdaUpdateWrapper<RsvReserveObject> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(RsvReserveObject::getId, dto.getIds());
        wrapper.set(RsvReserveObject::getIsEnable, dto.getEnable());
        super.update(wrapper);
    }

    /**
     * 从通讯录，根据用户ID获取用户姓名
     *
     * @param userId 用户ID
     * @return 用户姓名
     */
    @Override
    public String getUserNameFromAddressBook(String userId) {
        try {
            Result<List<UserInfoVo>> listResult = cpUserFeign.listUsersByUseridList(Collections.singletonList(userId));
            if (listResult.success()) {
                List<UserInfoVo> userList = listResult.getResult();
                if (!userList.isEmpty()) {
                    return userList.get(0).getName();
                }
            }
        } catch (Exception e) {
            // 记录日志或进行其他异常处理
            log.error("从通讯录获取用户名称异常", e);
        }
        return userId;
    }

    /**
     * 获取用户可预约日期列表
     *
     * @return 用户可预约日期列表
     */
    @Override
    public List<UserAvailableDateVO> getUserAvailableDate(Serializable id) {
        List<UserAvailableDateVO> result = new ArrayList<>();
        String userId = SecurityContextHolder.getUserIdStr();
        //获取用户的身份。1-教职工，2-学生，
        Integer userType = getUserType(userId);
        //获取场馆的配置信息
        RsvVenue venue = rsvVenueDao.selectById(id);
        String dayWeekLimit = userType == 2 ? venue.getDayWeekStudent() : venue.getDayWeekTeacher();
        Date today = new Date();
        for (int i = 0; i < venue.getPreBookDay(); i++) {
            Date date = DateUtil.offsetDay(today, i);
            if (dayWeekLimit.contains(String.valueOf(DateUtil.dayOfWeek(date) - 1))) {
                result.add(UserAvailableDateVO.builder()
                        .date(DateUtil.format(date, DatePattern.NORM_DATE_FORMAT))
                        .dayName(DateUtil.dayOfWeekEnum(date).toChinese("周"))
                        .shortDate(DateUtil.format(date, "MM-dd"))
                        .build());
            }
        }
        return result;
    }

    /**
     * 获取场地预约限制数量
     *
     * @param id 场馆ID
     * @return 场地预约限制数量
     */
    private Map<Long, Integer> getFieldLimitCountMap(Serializable id) {
        Map<Long, Integer> result = new HashMap<>();
        List<RsvField> rsvFieldList = rsvFieldDao.selectList(new LambdaQueryWrapper<RsvField>()
                .eq(RsvField::getVenueId, id));
        for (RsvField rsvField : rsvFieldList) {
            result.put(rsvField.getId(), rsvField.getLimitCount());
        }
        return result;
    }

    /**
     * 获取用户类型
     *
     * @param userId 用户ID
     * @return 用户类型 1-教职工，2-学生
     */
    @Override
    public Integer getUserType(String userId) {
        RMapCache<String, Integer> map = redissonClient.getMapCache(USER_TYPE_KEY, IntegerCodec.INSTANCE);
        Integer userType = map.get(userId);
        if (userType == null) {
            userType = fetchUserType(userId);
            map.put(userId, userType, 4, TimeUnit.HOURS);
        }
        return userType;
    }

    private Integer fetchUserType(String userId) {
        try {
            Result<List<UserInfoVo>> listResult = cpUserFeign.listUsersByUseridList(Collections.singletonList(userId));
            if (listResult.success()) {
                List<UserInfoVo> userList = listResult.getResult();
                if (!userList.isEmpty() && userList.get(0).getUserType() == USER_TYPE_TEACHER) {
                    return USER_TYPE_TEACHER;
                }
            }
        } catch (Exception e) {
            // 记录日志或进行其他异常处理
            log.error("获取用户类型异常", e);
        }
        return USER_TYPE_STUDENT;
    }
}


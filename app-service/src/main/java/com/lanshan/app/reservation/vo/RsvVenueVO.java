package com.lanshan.app.reservation.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 场馆信息表(RsvVenue)表实体VO类
 *
 * <AUTHOR>
 */
@ApiModel(value = "场馆信息表VO")
@Data
@ToString
public class RsvVenueVO implements Serializable {

    private static final long serialVersionUID = -6654101436817537569L;
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "场地名称.")
    private String name;

    @ApiModelProperty(value = "场地类型，0：仅团体预约；1：全部开放")
    private String type;

    @ApiModelProperty(value = "场地地址")
    private String address;

    @ApiModelProperty(value = "场地展示排序")
    private Integer sort;

    @ApiModelProperty(value = "是否删除")
    private Boolean isDeleted;

    @ApiModelProperty(value = "创建者")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "是否开放预约")
    private Boolean isOpen;

    @ApiModelProperty(value = "场馆图片地址")
    private String imgUrl;

    @ApiModelProperty(value = "学生可预约时间。0,1,2,3,4,5,6分别代表周日-周六")
    private String dayWeekStudent;

    @ApiModelProperty(value = "教师可预约时间。0,1,2,3,4,5,6分别代表周日-周六")
    private String dayWeekTeacher;

    @ApiModelProperty(value = "场地提前开放预约天数")
    private Integer preBookDay;

    @ApiModelProperty(value = "预约须知")
    private String notice;

    @ApiModelProperty(value = "场地设计图")
    private String designImgUrl;

    @ApiModelProperty(value = "是否团体预约，默认 true")
    private Boolean isTeamOpen;

    @ApiModelProperty(value = "所属校区编码")
    private String areaCode;

    @ApiModelProperty(value = "团队预约教师可预约时间。0,1,2,3,4,5,6分别代表周日-周六")
    private String teamDayWeekTeacher;

    @ApiModelProperty(value = "场地提前创建天数")
    private Integer preCreateDay;
}


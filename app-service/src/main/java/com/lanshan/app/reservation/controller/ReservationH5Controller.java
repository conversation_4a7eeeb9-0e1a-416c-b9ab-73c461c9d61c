package com.lanshan.app.reservation.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lanshan.app.common.bo.PageQo;
import com.lanshan.app.common.bo.Result;
import com.lanshan.app.reservation.service.ReservationH5Service;
import com.lanshan.app.reservation.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * 场馆预约用户移动端控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("h5/reservation")
@Api(tags = "场馆预约-用户移动端API", hidden = true)
@RequiredArgsConstructor
public class ReservationH5Controller {

    private final ReservationH5Service reservationH5Service;


    /**
     * 获取banner信息
     *
     * @return Result<List < RsvBannerVO>>
     */
    @ApiOperation("获取banner信息")
    @GetMapping("banner")
    public Result<List<RsvBannerVO>> getBannerInfo() {
        return Result.build(reservationH5Service.getBannerInfo());
    }

    /**
     * 获取场馆信息
     *
     * @return Result<List < RsvVenueVO>>
     */
    @ApiOperation("获取场馆信息（学生身份可预约的场馆列表）")
    @GetMapping("venue")
    public Result<List<RsvVenueVO>> getVenueInfo() {
        return Result.build(reservationH5Service.getVenueInfo());
    }

    /**
     * 获取公告信息
     *
     * @return Result<List < RsvNoticeVO>>
     */
    @ApiOperation("获取公告信息")
    @GetMapping("notice")
    public Result<List<RsvNoticeVO>> getNoticeInfo() {
        return Result.build(reservationH5Service.getNoticeInfo());
    }

    /**
     * 获取公告信息
     *
     * @return Result<List < RsvNoticeVO>>
     */
    @ApiOperation("获取更多公告信息")
    @GetMapping("notice/more")
    public Result<IPage<RsvNoticeVO>> getNoticePageInfo(PageQo page) {
        return Result.build(reservationH5Service.getNoticeInfo(page));
    }


    /**
     * 获取我的最近预约记录
     *
     * @return Result<List < RsvReserveRecordVO>>
     */
    @ApiOperation("获取我的最近预约记录")
    @GetMapping("recent/record")
    public Result<List<RsvReserveRecordVO>> getMyRecentReserveRecord() {
        return Result.build(reservationH5Service.getMyRecentReserveRecord());
    }

    /**
     * 分页获取我的预约记录
     *
     * @return Result<IPage < RsvReserveRecordVO>>
     */
    @ApiOperation("分页获取我的预约记录")
    @GetMapping("page/record")
    public Result<IPage<RsvReserveRecordVO>> getMyReserveRecord(PageQo page) {
        return Result.build(reservationH5Service.getMyReserveRecord(page));
    }


    /**
     * 获取可预约场馆类型列表
     *
     * @return Result<List < ReserveTypeVO>>
     */
    @ApiOperation("获取可预约场馆类型列表")
    @GetMapping("type/list")
    public Result<List<ReserveTypeVO>> getReserveType(@RequestParam @ApiParam(value = "场馆ID", required = true) Serializable id) {
        return Result.build(reservationH5Service.getReserveType(id));
    }


    @ApiOperation("获取可预约日期列表")
    @GetMapping("venue/date/available/list")
    public Result<List<UserAvailableDateVO>> getUserAvailableDate(@RequestParam @ApiParam(value = "场馆ID", required = true) Serializable id) {
        return Result.build(reservationH5Service.getUserAvailableDate(id));
    }

    /**
     * 获取可预约场地列表
     *
     * @return Result<List < RsvObjectGroupVO>>
     */
    @ApiOperation("获取可预约场地列表")
    @GetMapping("object/list")
    public Result<List<RsvObjectGroupVO>> getReserveObjectList(
            @RequestParam @ApiParam(value = "场馆ID", required = true) Serializable id,
            @RequestParam @ApiParam(value = "场地类型;1: 羽毛球；2：网球；3：篮球；4：乒乓球", required = true) Long type,
            @RequestParam @ApiParam(value = "预约日期。格式：YYYY-MM-DD", required = true) String date) {
        return Result.build(reservationH5Service.getReserveObjectList(id, type, date));
    }

    /**
     * 创建预约
     *
     * @param id 预约对象ID
     * @return Result<Boolean>
     */
    @ApiOperation("创建预约")
    @PostMapping("create")
    public Result<Boolean> createReservation(@ApiParam(value = "预约对象ID", required = true) @RequestParam Long id) {
        return Result.build(reservationH5Service.createReservation(id));
    }

    /**
     * 取消预约
     *
     * @param id 预约记录ID
     * @return Result<Boolean>
     */
    @ApiOperation("取消预约")
    @PostMapping("cancel")
    public Result<Boolean> cancelReservation(@ApiParam(value = "预约记录ID", required = true) @RequestParam Long id) {
        return Result.build(reservationH5Service.cancelReservation(id));
    }

    @ApiOperation("获取签到服务时间ID")
    @GetMapping("server/time/id")
    public Result<String> getServerTimeId() {
        return Result.build(reservationH5Service.getServerTimeId());
    }

    @ApiOperation("签到")
    @PostMapping("record/sign")
    public Result<RsvReserveRecordVO> signRecord(@ApiParam(value = "服务器时间ID", required = true) @RequestParam String serverTimeId) {
        return Result.build(reservationH5Service.signRecord(serverTimeId));
    }

    @ApiOperation("获取签到列表")
    @PostMapping("record/listNeedSign")
    public Result<List<RsvReserveRecordVO>> listNeedSign() {
        return Result.build(reservationH5Service.listNeedSign());
    }

    @ApiOperation("签到")
    @PostMapping("record/signV2")
    public Result<Boolean> signRecordV2(Long recordId) {
        return Result.build(reservationH5Service.signRecordV2(recordId));
    }

}

package com.lanshan.app.reservation.dto;

import com.lanshan.app.common.bo.PageQo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 预约场地情况搜索DTO
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "预约场地情况搜索DTO")
public class ReserveObjectSearchDTO extends PageQo implements Serializable {

    private static final long serialVersionUID = -5061347531020571695L;

    @ApiModelProperty(value = "场馆ID", required = true)
    private Long id;

    @ApiModelProperty(value = "日期。格式：YYYY-MM-DD", required = true)
    private String day;

    @ApiModelProperty(value = "场地类型;1: 羽毛球；2：网球；3：篮球；4：乒乓球")
    private Long fieldType;

    @ApiModelProperty(value = "时间段")
    private String time;

    @ApiModelProperty(value = "场地ID")
    private Long fieldId;
}

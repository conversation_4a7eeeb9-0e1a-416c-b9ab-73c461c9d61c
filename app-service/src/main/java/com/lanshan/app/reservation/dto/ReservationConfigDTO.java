package com.lanshan.app.reservation.dto;

import com.lanshan.app.reservation.vo.RsvVenueTimeConfigVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 预约配置DTO
 */
@Data
@ApiModel(value = "场馆预约配置DTO")
public class ReservationConfigDTO implements Serializable {
    private static final long serialVersionUID = -3236552607764805890L;

    @ApiModelProperty(value = "场馆ID")
    private Long venueId;

    @ApiModelProperty(value = "学生可预约时间。0,1,2,3,4,5,6分别代表周日-周六")
    private String[] dayWeekStudent;

    @ApiModelProperty(value = "教师可预约时间。0,1,2,3,4,5,6分别代表周日-周六")
    private String[] dayWeekTeacher;

    @ApiModelProperty(value = "场地提前开放预约天数")
    private Integer preBookDay;

    @ApiModelProperty(value = "预约须知")
    private String notice;

    @ApiModelProperty(value = "场地可预约时间段配置。已经不使用")
    @Deprecated
    private List<RsvVenueTimeConfigVO> timeConfigList;

    @ApiModelProperty(value = "团队预约教师可预约时间。0,1,2,3,4,5,6分别代表周日-周六")
    private String[] teamDayWeekTeacher;

    @ApiModelProperty(value = "场地提前创建天数")
    private Integer preCreateDay;

}

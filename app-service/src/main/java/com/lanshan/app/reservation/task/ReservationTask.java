package com.lanshan.app.reservation.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lanshan.app.common.constant.CommonConstant;
import com.lanshan.app.common.service.ISysConfigService;
import com.lanshan.app.reservation.entity.*;
import com.lanshan.app.reservation.enums.ReserveStatusEnum;
import com.lanshan.app.reservation.enums.VenueConfigType;
import com.lanshan.app.reservation.service.*;
import com.lanshan.app.reservation.vo.RsvVenueExcludeTimeVO;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ReservationTask {

    final RsvVenueService rsvVenueService;

    final RsvFieldService rsvFieldService;

    final RsvVenueTimeConfigService rsvVenueTimeConfigService;

    final RsvReserveObjectService rsvReserveObjectService;

    final RsvReserveRecordService rsvReserveRecordService;

    final RsvBlacklistService rsvBlacklistService;

    final ISysConfigService sysConfigService;

    final RsvVenueExcludeTimeService rsvVenueExcludeTimeService;

    /**
     * 场地预约对象生成任务
     */
    @XxlJob("generateReserveObject")
    public void generateReserveObject() {
        //获取所有开放预约的场馆
        List<RsvVenue> venueList = rsvVenueService.list(new LambdaQueryWrapper<RsvVenue>()
                .eq(RsvVenue::getIsDeleted, false)
                .eq(RsvVenue::getIsOpen, true));

        //获取场馆所有场地
        List<RsvField> fieldList = rsvFieldService.list(new LambdaQueryWrapper<RsvField>().eq(RsvField::getIsDeleted, false));
        Map<Long, List<RsvField>> fieldMap = fieldList.stream().collect(Collectors.groupingBy(RsvField::getVenueId));

        //获取场馆时间段配置
        List<RsvVenueTimeConfig> timeConfigList = rsvVenueTimeConfigService.list();
        Map<Long, Map<Integer, List<RsvVenueTimeConfig>>> timeConfigMap = timeConfigList.stream().collect(
                Collectors.groupingBy(RsvVenueTimeConfig::getVenueId,
                        Collectors.groupingBy(RsvVenueTimeConfig::getDayOfWeek)));

        // 查询时间大于当前时间的预约对象
        Date today = new Date();
        List<RsvReserveObject> existObjectList = rsvReserveObjectService.list(new LambdaQueryWrapper<RsvReserveObject>().ge(RsvReserveObject::getOpenDay, today));
        List<RsvReserveObject> reserveObjectList = new ArrayList<>();

        for (RsvVenue venue : venueList) {
//            Integer preBookDay = venue.getPreBookDay();
            Integer preCreateDay = venue.getPreCreateDay();
            List<RsvField> venueFieldList = fieldMap.getOrDefault(venue.getId(), new ArrayList<>());
            for (RsvField field : venueFieldList) {
                for (int i = 0; i < preCreateDay; i++) {
                    Date date = DateUtil.offsetDay(today, i);
                    int dayOfWeek = DateUtil.dayOfWeek(date) - 1;
                    List<RsvVenueTimeConfig> timePeriodList = timeConfigMap.getOrDefault(venue.getId(), new HashMap<>()).getOrDefault(dayOfWeek, new ArrayList<>());
                    for (RsvVenueTimeConfig timeConfig : timePeriodList) {
                        String dateStr = DateUtil.format(date, "yyyy-MM-dd");
                        boolean isEnable = true;
                        //查询不可预约日期,并排除相关日期数据
                        List<RsvVenueExcludeTimeVO> rsvVenueExcludeTimeVOS = rsvVenueExcludeTimeService.listByConfigId(timeConfig.getId(), VenueConfigType.TIME.getType());
                        if (CollUtil.isNotEmpty(rsvVenueExcludeTimeVOS)) {
                            Set<String> excludeDateSet = rsvVenueExcludeTimeVOS.stream().map(rsvVenueExcludeTimeVO -> {
                                return DateUtil.format(rsvVenueExcludeTimeVO.getExcludeDate(), "yyyy-MM-dd");
                            }).collect(Collectors.toSet());
                            if (excludeDateSet.contains(dateStr)) {
                                isEnable = false;
                            }

                        }

                        if (existsReserveObject(field.getId(), dateStr, timeConfig.getTimeOfDay(), existObjectList)) {
                            continue;
                        }
                        reserveObjectList.add(RsvReserveObject.builder()
                                .fieldId(field.getId())
                                .fieldName(field.getName())
                                .fieldType(field.getType())
                                .venueId(venue.getId())
                                .timeConfigId(timeConfig.getId())
                                .venueName(venue.getName())
                                .openDay(date)
                                .openTime(timeConfig.getTimeOfDay())
                                .remainCount(field.getLimitCount())
                                .isEnable(isEnable)
                                .build());
                    }
                }
            }
        }
        rsvReserveObjectService.saveBatch(reserveObjectList);
    }

    /**
     * 处理预约违约记录
     * 每天处理前一天的预约违约记录，将触发规则的人员拉入黑名单
     */
    @XxlJob("handleReserveBreak")
    public void handleReserveBreak() {

        //将今天以前的预约成功但是未使用的预约记录状态改为违约
        rsvReserveRecordService.update(new LambdaUpdateWrapper<RsvReserveRecord>()
                .lt(RsvReserveRecord::getUseDate, new Date())
                .eq(RsvReserveRecord::getStatus, ReserveStatusEnum.RESERVED.getCode())
                .set(RsvReserveRecord::getStatus, ReserveStatusEnum.BREAK.getCode()));

        // 统计用户违约次数，将触发规则的人员拉入黑名单
        String breakLimit = sysConfigService.selectConfigByKey(CommonConstant.RSV_MONTH_BREAK_LIMIT_KEY);
        if (StringUtils.isEmpty(breakLimit)) {
            return;
        }
        int breakLimitNum = Integer.parseInt(breakLimit);
        Set<String> existBlacklistUserIds = getExistBlacklistUserIds();
        List<RsvBlacklist> blacklistList = new ArrayList<>();

        rsvReserveRecordService.list(new QueryWrapper<RsvReserveRecord>()
                .select(" user_id, user_name, count(*) as count ")
                .lambda()
                .ge(RsvReserveRecord::getUseDate, DateUtil.beginOfMonth(DateUtil.yesterday()))
                .le(RsvReserveRecord::getUseDate, DateUtil.endOfMonth(DateUtil.yesterday()))
                .eq(RsvReserveRecord::getStatus, ReserveStatusEnum.BREAK.getCode())
                .groupBy(RsvReserveRecord::getUserId, RsvReserveRecord::getUserName)
                .having("count(*) >= " + breakLimitNum)
        ).forEach(record -> {
            if (existBlacklistUserIds.contains(record.getUserId())) {
                return;
            }
            blacklistList.add(RsvBlacklist.builder()
                    .userId(record.getUserId())
                    .userName(record.getUserName())
                    .comment("预约违约次数超过" + breakLimitNum + "次")
                    .startTime(new Date())
                    .endTime(DateUtil.offsetDay(new Date(), 30))
                    .createTime(new Date())
                    .build());

        });
        rsvBlacklistService.saveBatch(blacklistList);

    }


    /**
     * 判断是否存在预约对象
     *
     * @param fieldId         场地ID
     * @param date            日期
     * @param timeOfDay       时间段
     * @param existObjectList 已存在的预约对象
     * @return 是否存在
     */
    private Boolean existsReserveObject(Long fieldId, String date, String timeOfDay, List<RsvReserveObject> existObjectList) {
        return existObjectList.stream().anyMatch(object ->
                object.getFieldId().equals(fieldId) &&
                        DateUtil.format(object.getOpenDay(), "yyyy-MM-dd").equals(date) &&
                        object.getOpenTime().equals(timeOfDay));

    }

    /**
     * 获取已存在的黑名单用户ID集合
     *
     * @return 已存在的黑名单用户ID集合
     */
    private Set<String> getExistBlacklistUserIds() {
        return rsvBlacklistService.list(
                        new LambdaQueryWrapper<RsvBlacklist>()
                                .select(RsvBlacklist::getUserId)
                                .eq(RsvBlacklist::getIsDeleted, false)
                                .gt(RsvBlacklist::getEndTime, new Date())
                )
                .stream()
                .map(RsvBlacklist::getUserId).collect(Collectors.toSet());
    }
}

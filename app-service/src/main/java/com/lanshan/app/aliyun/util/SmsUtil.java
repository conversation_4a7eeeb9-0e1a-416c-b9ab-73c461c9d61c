package com.lanshan.app.aliyun.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.lanshan.app.common.service.ISysConfigService;
import com.lanshan.app.common.utils.JsonUtil;
import com.lanshan.app.aliyun.dto.SmsSendDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import static com.lanshan.app.aliyun.constant.AliYunConstant.*;


/**
 * 阿里云短信
 * <AUTHOR> yang.
 * @since 2022/12/19 11:41
 * @version 1.0
 */

@Component
@Slf4j
public class SmsUtil {

    @Resource
    private ISysConfigService sysConfigService;

    private static volatile Client clientInstance = null;

    private final static String REGION = "cn-hangzhou";

    private final static String ENDPOINT = "dysmsapi.aliyuncs.com";

    private final static String SUCCESS_CODE = "OK";

    public Client getClient(){
        if (clientInstance == null) {
            synchronized (SmsUtil.class){
                if (clientInstance == null){
                    Config config = new Config();
                    String accessKeyId = sysConfigService.selectConfigByKey(ACCESS_KEY_ID);
                    String accessKeySecret = sysConfigService.selectConfigByKey(ACCCESS_KEY_SECRET);
                    config.setAccessKeyId(accessKeyId);
                    config.setAccessKeySecret(accessKeySecret);
                    config.setRegionId(REGION);
                    config.setEndpoint(ENDPOINT);
                    try {
                        clientInstance = new Client(config);
                    }catch (Exception e) {
                        clientInstance = null;
                        log.error("短信客户端初始化失败",e);
                    }
                }
            }

        }
        return clientInstance;
    }

    /**
     * 根据模板id发送短信
     * <AUTHOR> yang.
     * @since 2025/4/17 11:22
     */
    public <T> void sendSmsByTemplate(String smsCode,String phoneNumbers,T record) {
        String content = getTemplateContent(smsCode);
        SmsSendDTO smsDTO = new SmsSendDTO();
        smsDTO.setTemplateCode(smsCode);
        //正则提取所有 ${xxx} 中的 xxx
        List<String> variableNames = ReUtil.findAll("\\$\\{(.*?)\\}", content, 1);
        // 如果模板里有参数
        if (CollUtil.isNotEmpty(variableNames)){
            //遍历取值、校验、组装
            List<SmsSendDTO.TemplateParam> params = new ArrayList<>();
            for (String varName : variableNames) {
                // 从 record 中通过 getter 拿到对应属性值
                Object raw = BeanUtil.getProperty(record, varName);
                String value = raw != null ? raw.toString() : null;
                if (value == null) {
                    value = "";
                }
                params.add(new SmsSendDTO.TemplateParam(varName, value));
            }
            smsDTO.setTemplateParams(params);
        }
        sendMessage(phoneNumbers, smsDTO);
    }

    /**
     * 发送短信
     * <AUTHOR> yang.
     * @since 2025/4/17 11:03
     */
    public boolean sendMessage(String phoneNumbers,SmsSendDTO smsSendDTO){
        boolean flag = false;
        Client client = getClient();
        SendSmsRequest request = new SendSmsRequest();
        request.setPhoneNumbers(phoneNumbers);
        String sign = sysConfigService.selectConfigByKey(SMS_SIGN);
        request.setSignName(sign);
        String templateCode = smsSendDTO.getTemplateCode();
        request.setTemplateCode(templateCode);
        //如果有模板参数，则设置模板参数
        String json = "";
        List<SmsSendDTO.TemplateParam> templateParams = smsSendDTO.getTemplateParams();
        if (CollUtil.isNotEmpty(templateParams)) {
            Map<String, Object> params = new HashMap<>(templateParams.size());
            for (SmsSendDTO.TemplateParam templateParam : templateParams) {
                params.put(templateParam.getName(), templateParam.getValue());
            }
            json = JsonUtil.toJson(params);
            request.setTemplateParam(json);
        }
        try {
            RuntimeOptions runtime = new RuntimeOptions();
            SendSmsResponse response = client.sendSmsWithOptions(request, runtime);
            String code = response.getBody().getCode();
            if (code.equals(SUCCESS_CODE)){
                flag = true;
                log.info("短信发送成功，手机号：{},模板：{},参数：{}", phoneNumbers,templateCode, json);
            }else {
                log.error("短信发送失败，手机号：{},模板：{},参数：{},原因：{}", phoneNumbers,templateCode, json, response.getBody().getMessage());
            }
        }catch (Exception e) {
            log.error(StrUtil.format("短信发送异常，手机号：{},模板：{},参数：{}", phoneNumbers,templateCode, json),e);
        }
        return flag;
    }


    /**
     * 查询短信模板
     * <AUTHOR> yang.
     * @since 2025/4/17 11:03
     */
    public List<QuerySmsTemplateListResponseBody.QuerySmsTemplateListResponseBodySmsTemplateList> querySmsTemplateList(){
        Client client = getClient();
        QuerySmsTemplateListRequest request = new QuerySmsTemplateListRequest();
        request.setPageIndex(1);
        request.setPageSize(50);
        try {
            RuntimeOptions runtime = new RuntimeOptions();
            QuerySmsTemplateListResponse response = client.querySmsTemplateListWithOptions(request, runtime);
            String code = response.getBody().getCode();
            if (code.equals(SUCCESS_CODE)){
                return response.getBody().getSmsTemplateList();
            }
        }catch (Exception e) {
            log.error("查询短信模板失败",e);
        }
        return null;
    }

    /**
     * 根据code获取模板内容
     * <AUTHOR> yang.
     * @since 2025/4/17 11:07
     */
    public String getTemplateContent(String templateCode){
        List<QuerySmsTemplateListResponseBody.QuerySmsTemplateListResponseBodySmsTemplateList> list = querySmsTemplateList();
        if (CollUtil.isEmpty(list)){
            return "";
        }
        String templateContent = list.stream()
                .filter(item -> item.getTemplateCode().equals(templateCode)).findFirst()
                .map(QuerySmsTemplateListResponseBody.QuerySmsTemplateListResponseBodySmsTemplateList::getTemplateContent).orElse("");
        return templateContent;
    }

    public static String getRandomCode(){
        Random random = new Random();
        int ranNum = (int) (random.nextDouble() * (999999 - 100000 + 1)) + 100000;
        return String.valueOf(ranNum);
    }


}

package com.lanshan.app.config;

import com.lanshan.app.config.interceptor.MagicApiDocInterceptor;
import com.lanshan.app.config.interceptor.ManagerAccessInterceptor;
import com.lanshan.app.config.interceptor.OpenAccessInterceptor;
import com.lanshan.app.config.interceptor.UserAccessInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * WebConfiguration
 *
 * <AUTHOR>
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Resource
    private OpenAccessInterceptor openAccessInterceptor;

    @Resource
    private UserAccessInterceptor userAccessInterceptor;

    @Resource
    private ManagerAccessInterceptor managerAccessInterceptor;

    @Resource
    private MagicApiDocInterceptor magicApiDocInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        //人脸管理后台管理员登录拦截验证，与网关配置和使用
        registry.addInterceptor(managerAccessInterceptor).addPathPatterns(
                "/img/manage/**",
                "/img/outsider/**",
                "/imgSysConfig/**",
                "/manage/reservation/**",
                "/h5/reservation/**",
                "/manage/fleaMarket/**",
                "/h5/fleaMarket/**",
                "/img/collect/**",
                "/h5/news/**",
                "/repair/**",
                "/mapi/**",
                "/suggest/**",
                "/cardSupplement/**",
                "/vehicle/**",
                "/checkin/**"
        );
//
//        // 人脸采集H5用户登陆拦截验证
//        registry.addInterceptor(userAccessInterceptor).addPathPatterns("/img/collect/**");

        // 开放接口权限验证拦截
        registry.addInterceptor(openAccessInterceptor).addPathPatterns("/open/access/**");

        registry.addInterceptor(magicApiDocInterceptor).addPathPatterns("/v2/api-docs/**");
    }
}

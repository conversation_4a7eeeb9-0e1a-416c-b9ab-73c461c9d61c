package com.lanshan.app.config.interceptor;

import com.lanshan.app.common.constant.SecurityConstant;
import com.lanshan.app.common.enums.ExceptionCodeEnum;
import com.lanshan.app.config.dao.UserPermissionDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.ssssssss.magicapi.core.interceptor.RequestInterceptor;
import org.ssssssss.magicapi.core.model.ApiInfo;
import org.ssssssss.magicapi.core.model.Options;
import org.ssssssss.magicapi.core.servlet.MagicHttpServletRequest;
import org.ssssssss.magicapi.core.servlet.MagicHttpServletResponse;
import org.ssssssss.script.MagicScriptContext;

import javax.annotation.Resource;
import java.util.List;

/**
 * magic-api 接口鉴权
 */
@Component
@Slf4j
public class MagicRequestInterceptor implements RequestInterceptor {

    @Resource
    private UserPermissionDao userPermissionDao;

    /**
     * 接口请求之前
     *
     * @param info    接口信息
     * @param context 脚本变量信息
     */
    @Override
    public Object preHandle(ApiInfo info, MagicScriptContext context, MagicHttpServletRequest request, MagicHttpServletResponse response) throws Exception {
        String permissionCode = info.getOptionValue(Options.PERMISSION);
        // 有权限码的接口 才进行鉴权
        if (StringUtils.isNotBlank(permissionCode)) {
            String userId = request.getHeader(SecurityConstant.DETAILS_USER_ID);
            if (StringUtils.isBlank(userId)) {

                throw ExceptionCodeEnum.UNAUTHORIZED.toServiceException();
            }
            if ("1".equals(userId)) {   //超级管理员放行
                return null;
            }
            // 使用 MyBatis 查询用户权限
            List<String> userPermissions = userPermissionDao.selectUserPermissions(Long.parseLong(userId));
            boolean hasPermission = userPermissions.contains(permissionCode);
            if (!hasPermission) {
                throw ExceptionCodeEnum.FORBIDDEN.toServiceException();
            }
        }
        return null;
    }

    /**
     * 接口执行之后
     */
    @Override
    public Object postHandle(ApiInfo info, MagicScriptContext context, Object value, MagicHttpServletRequest request, MagicHttpServletResponse response) throws Exception {
        return null;
    }
}
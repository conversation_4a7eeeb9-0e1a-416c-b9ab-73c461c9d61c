package com.lanshan.app.checkin.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.lanshan.app.checkin.enums.CheckinTaskStatus;
import com.lanshan.app.checkin.vo.CheckinRuleGroupVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

public class CheckinUtil {

    /**
     * 获取指定日期的打卡规则及状态
     * @param ruleGroup 规则配置对象
     * @param belongDay 日期字符串（格式yyyy-MM-dd）
     * @return Map结构：{"rules":规则集合, "taskStatus":状态值}
     */
    public static Map<String, Object> getCheckinStatus(CheckinRuleGroupVO ruleGroup, String belongDay) {
        Map<String, Object> result = new HashMap<>();
        List<String> ruleStrings = new ArrayList<>();

        int taskStatus = CheckinTaskStatus.ENDED.value;
        //belongDay（字符串）解析为 Date
        Date date = DateUtil.parse(belongDay,  "yyyy-MM-dd");
        // 计算星期几，DateUtil.dayOfWeek 返回 1~7（周日=1，周一=2…周六=7）
        // 这里转成 0~6  周日 为 0
        int checkinWeekday = (DateUtil.dayOfWeek(date)  + 6) % 7;

        // 1. 检查特殊免打卡日期
        //类型0：（精确匹配日期）
        //类型1：（日期区间判断）
        boolean isSpeOff = ruleGroup.getSpeOffdaysList().stream().anyMatch(day  ->
                (day.getType()  == 0 && DateUtil.isSameDay(day.getTime(),  date)) ||
                        (day.getType()  == 1 && DateUtil.isIn(date,  day.getBegTime(),  day.getEndTime()))
        );
        if (isSpeOff) {
            result.put("rules",  Collections.emptyList());
            result.put("taskStatus",   CheckinTaskStatus.ENDED.value);
            return result;
        }

        // 2. 处理特殊必须打卡日期
        //类型0：（精确匹配日期）
        //类型1：（日期区间判断）
        List<CheckinRuleGroupVO.CheckinTime> speTimes = ruleGroup.getSpeWorkdaysList().stream()
                .filter(day -> day.getType()  == 0 ?
                        DateUtil.isSameDay(day.getTime(),  date) :
                        DateUtil.isIn(date,  day.getBegTime(),  day.getEndTime()))
                .flatMap(day -> day.getCheckintime().stream())
                .collect(Collectors.toList());

        if (!speTimes.isEmpty())  {
            String timeRange = speTimes.stream()
                    .map(t -> String.format("%s-%s",
                            t.getWorkSecStr(),
                            t.getOffWorkSecStr()))
                    .collect(Collectors.joining(",  "));
            ruleStrings.add("时间段：(" + timeRange + ")");
            taskStatus = calculateStatus(speTimes,date);
        } else {
            // 3. 处理常规工作日规则
            for (CheckinRuleGroupVO.CheckinDate checkinDate : ruleGroup.getCheckinDateList())  {
                if (checkinDate.getWorkdays().contains(checkinWeekday))  {
                    // 合并连续工作日（如[1,2,3]→"周一至周三"）
                    List<Integer> sortedDays = checkinDate.getWorkdays().stream()
                            // 先把 0 -> 7，其它不变
                            .map(d -> d == 0 ? 7 : d)
                            .distinct()
                            .sorted()
                            .collect(Collectors.toList());
                    // 用更直观的星期映射：1=周一 … 7=周日
                    String[] weeks = {
                            "",      // 占位，代码里不访问
                            "周一","周二","周三","周四","周五","周六","周日"
                    };
                    StringBuilder weekdayRange = new StringBuilder();
                    int start = sortedDays.get(0);

                    for (int i = 0; i < sortedDays.size(); i++) {
                        // 如果到了最后一个，或下一个不连续，就结算一段区间
                        boolean isLast = (i == sortedDays.size() - 1);
                        boolean notNext = !isLast && sortedDays.get(i + 1) != sortedDays.get(i) + 1;
                        if (isLast || notNext) {
                            int end = sortedDays.get(i);
                            String startStr = weeks[start];
                            String endStr   = weeks[end];
                            // 起止相同就只写一个，否则写“至”
                            weekdayRange.append(startStr.equals(endStr)
                                    ? startStr
                                    : startStr + "至" + endStr);

                            if (!isLast) {
                                weekdayRange.append("、");
                                // 下一个片段的起点
                                start = sortedDays.get(i + 1);
                            }
                        }
                    }

                    String timeRange = checkinDate.getCheckintime().stream()
                            .map(t -> String.format("%s-%s",
                                    t.getWorkSecStr(),
                                    t.getOffWorkSecStr()))
                            .collect(Collectors.joining(",  "));
                    ruleStrings.add(weekdayRange  + "(" + timeRange + ")");
                    taskStatus = Math.min(taskStatus,  calculateStatus(checkinDate.getCheckintime(),date));
                }
            }
        }

        result.put("rules",  ruleStrings.isEmpty()  ? Collections.emptyList() : ruleStrings);
        result.put("taskStatus",  ruleStrings.isEmpty()  ? CheckinTaskStatus.ENDED.value : taskStatus);
        return result;
    }

    // 保留唯一辅助方法：状态计算
    private static int calculateStatus(List<CheckinRuleGroupVO.CheckinTime> times,Date targetDate) {
        Date now = new Date();
        DateTime begin = DateUtil.beginOfDay(now);
        if (DateUtil.compare(targetDate,  begin)  < 0) {
            // 历史日期：强制结束
            return CheckinTaskStatus.ENDED.value;
        } else if (!DateUtil.isSameDay(targetDate,  now)) {
            // 未来日期：默认未开始（或按规则计算）
            return CheckinTaskStatus.NOT_STARTED.value;
        }
        //当日0点至今的秒数
        long currentSec = DateUtil.betweenMs(begin, DateUtil.date())  / 1000;
        boolean isInProgress = times.stream()
                .anyMatch(t -> currentSec >= t.getWorkSec()  && currentSec <= t.getOffWorkSec());
        boolean hasFuture = times.stream().anyMatch(t  -> currentSec < t.getOffWorkSec());
        return isInProgress ? CheckinTaskStatus.IN_PROGRESS.value : (hasFuture ? CheckinTaskStatus.NOT_STARTED.value : CheckinTaskStatus.ENDED.value);
    }

    /**
     * 计算出勤率
     * <AUTHOR> yang.
     * @since 2025/4/28 17:04
     */
    public static BigDecimal getAttendanceRate(long userActiveCount, long checkinCount) {
        BigDecimal attendanceRate = BigDecimal.ZERO;

        if (checkinCount != 0) {
            if (userActiveCount != 0){
                BigDecimal div = NumberUtil.div(BigDecimal.valueOf(checkinCount), BigDecimal.valueOf(userActiveCount),4, RoundingMode.DOWN) ;
                attendanceRate = NumberUtil.mul(div, new BigDecimal(100));
                if (attendanceRate.compareTo(BigDecimal.valueOf(100)) > 0) {
                    attendanceRate = BigDecimal.valueOf(100);
                }
            }
            // 如果小数部分为0，去掉小数部分
            if (attendanceRate.stripTrailingZeros().scale() <= 0) {
                attendanceRate = attendanceRate.setScale(0, RoundingMode.DOWN);
            } else {
                attendanceRate = attendanceRate.setScale(1, RoundingMode.DOWN);
            }
        }
        return attendanceRate;

    }

    public static BigDecimal getAttendanceRate(BigDecimal size, BigDecimal attendance) {
        BigDecimal attendanceRate = BigDecimal.ZERO;

        if (!Objects.equals(attendance, BigDecimal.ZERO)) {
            if (!Objects.equals(size, BigDecimal.ZERO)){
                attendanceRate = NumberUtil.div(attendance, size,4, RoundingMode.DOWN) ;
                if (attendanceRate.compareTo(BigDecimal.valueOf(100)) > 0) {
                    attendanceRate = BigDecimal.valueOf(100);
                }
            }
            // 如果小数部分为0，去掉小数部分
            attendanceRate = formatPercentage(attendanceRate);
        }
        return attendanceRate;

    }

    /**
     * 计算第一个值相较于第二个值的增减百分比
     * @param firstValue  第一个数值
     * @param secondValue 第二个数值(基准值)
     * @return 百分比字符串(带符号，保留两位小数)
     */
    public static String calculateChangePercentage(BigDecimal firstValue, BigDecimal secondValue) {

        if (BigDecimal.ZERO.compareTo(secondValue) == 0) {
            if (BigDecimal.ZERO.compareTo(firstValue) == 0) {
                return "0%";
            } else {
                // second 为 0，first 不为 0，按规则返回 +first
                BigDecimal percent = firstValue.setScale(2, RoundingMode.HALF_UP);
                // 如果小数部分为0，去掉小数部分
                percent = formatPercentage(percent);
                return String.format("%s%.2f%%", percent.signum() > 0 ? "+" : "", percent.doubleValue());
            }
        }

        if (BigDecimal.ZERO.compareTo(firstValue) == 0) {
            // first 为 0，second 不为 0，按规则返回 -second
            BigDecimal percent = secondValue.negate().setScale(2, RoundingMode.HALF_UP);
            // 如果小数部分为0，去掉小数部分
            percent = formatPercentage(percent);
            return String.format("+%.2f%%", percent.doubleValue());
        }

        // 正常情况
        BigDecimal difference = firstValue.subtract(secondValue);
        BigDecimal ratio = difference.divide(secondValue, 8, RoundingMode.HALF_UP);
        BigDecimal percentage = ratio.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        // 如果小数部分为0，去掉小数部分
        percentage = formatPercentage(percentage);
        return String.format("%s%.2f%%", percentage.signum() > 0 ? "+" : "", percentage.doubleValue());
    }

    // 如果小数部分为0，去掉小数部分
    public static BigDecimal formatPercentage(BigDecimal percentage) {
        if (percentage.stripTrailingZeros().scale() <= 0) {
            percentage = percentage.setScale(0, RoundingMode.DOWN);
        } else {
            percentage = percentage.setScale(1, RoundingMode.DOWN);
        }
        return percentage;
    }

    /**
     * 获取两个时间范围所有的日期
     * <AUTHOR> yang.
     * @since 2025/4/28 17:37
     */
    public static List<String> getDateRangeList(Date startTime, Date endTime) {
        List<String> dateList = new ArrayList<>();

        // 1. 安全校验
        if (startTime == null || endTime == null) {
            return Collections.emptyList();
        }

        // 2. 时间标准化处理
        Date startDay = DateUtil.beginOfDay(startTime);
        Date endDay = DateUtil.beginOfDay(endTime);

        // 3. 自动矫正倒序时间
        if (startDay.after(endDay))  {
            Date temp = startDay;
            startDay = endDay;
            endDay = temp;
        }

        // 4. 循环生成日期序列
        Date currentDate = startDay;
        while (!currentDate.after(endDay))  {
            dateList.add(DateUtil.format(currentDate,  DatePattern.NORM_DATE_PATTERN));
            currentDate = DateUtil.offsetDay(currentDate,  1); // 使用线程安全的日期计算
        }

        return dateList;
    }

    public static Date getValidEndDate(Date startDayDate, Date endDayDate) {
        Date nowDate = new Date();

        // 如果任一为 null，视为无效
        if (startDayDate == null || endDayDate == null) {
            return null;
        }

        // 如果 endDayDate 早于当前时间，直接返回
        if (endDayDate.before(nowDate)) {
            return endDayDate;
        }

        // 如果当前时间在范围内
        if (!nowDate.before(startDayDate) && !nowDate.after(endDayDate)) {
            // 返回当前时间和endDayDate中较早的一个
            return nowDate.before(endDayDate) ? nowDate : endDayDate;
        }

        // 当前时间不在范围内
        return null;
    }

    /**
     * 提取路径倒数n级路径
     * <AUTHOR> yang.
     * @since 2025/03/11 10:22
     * @param level 倒数几个层级
     */
    public static String getLastLevels(String path,int level) {
        if (StrUtil.isEmpty(path)){
            return StrUtil.EMPTY;
        }
        // 1. 去除末尾斜杠并分割路径（自动过滤空元素）
        String trimmedPath = StrUtil.removeSuffix(path,  StrPool.SLASH);
        List<String> parts = StrUtil.splitTrim(trimmedPath,  StrPool.SLASH);

        // 2. 处理层级不足的情况
        if (CollUtil.isEmpty(parts)  || parts.size()  < level) {
            return trimmedPath;
        }

        // 3. 截取倒数两级并拼接结果
        List<String> lastTwo = CollUtil.sub(parts,  parts.size()  - level, parts.size());
        return StrUtil.join(StrPool.SLASH,  lastTwo);
    }

    public static void main(String[] args) {
        System.out.println(getLastLevels("机电工程3班", 2));
    }


}

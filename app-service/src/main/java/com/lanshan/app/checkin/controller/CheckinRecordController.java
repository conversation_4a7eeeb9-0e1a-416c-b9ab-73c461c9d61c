package com.lanshan.app.checkin.controller;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.checkin.dto.*;
import com.lanshan.app.checkin.po.CheckinRecord;
import com.lanshan.app.checkin.service.*;
import com.lanshan.app.checkin.task.CheckinDataTask;
import com.lanshan.app.checkin.vo.*;
import com.lanshan.app.common.service.ISysConfigService;
import com.lanshan.app.common.utils.ExcelUtils;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;

import static com.lanshan.app.checkin.constant.CheckinConstant.*;

/**
 * 打卡记录
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/checkin/record")
public class CheckinRecordController {

    @Resource
    private CheckinRecordService checkinRecordService;
    @Resource
    private CheckinUserDailyService checkinUserDailyService;
    @Resource
    private CheckinDataSyncService checkinDataSyncService;
    @Resource
    private CheckinGradeDailyService checkinGradeDailyService;
    @Resource
    private CheckinAuditUserService checkinAuditUserService;
    @Resource
    private CheckinDataTask checkinDataTask;

    /**
     * 同步打卡记录
     * <AUTHOR> yang.
     * @since 2025/4/25 10:24
     */
    @PostMapping(value = "/syncCheckinRecord",produces = "application/json;charset=UTF-8")
    public Result<Object> syncCheckinRecord(@RequestBody @Valid CheckinRecordSyncDTO dto) {
        checkinDataSyncService.syncCheckinRecord(DateUtil.parse(dto.getStartTime()) , DateUtil.parse(dto.getEndTime()));
        return Result.build();
    }

    /**
     * 获取指定用户 获取某天的开始时间 到现在 的打卡记录
     * <AUTHOR> yang.
     * @since 2025/4/25 10:24
     */
    @PostMapping(value = "/getWxCpCheckinRecord",produces = "application/json;charset=UTF-8")
    public Result<List<CheckinRecord>> getWxCpCheckinRecord(@RequestBody @Valid List<String> userIds){
        Date now = new Date();
        Date begin = DateUtil.beginOfDay(now);
        List<CheckinRecord> checkinRecords = checkinRecordService.getCheckinRecordByWxCp(userIds, begin, now);
        return Result.build(checkinRecords);
    }

    @PostMapping(value = "/pushCheckinWxCpMessage",produces = "application/json;charset=UTF-8")
    public Result<Object>  pushCheckinWxCpMessage(){
        checkinDataTask.pushCheckinWxCpMessage();
        return Result.build();
    }

    /**
     * 获取班级 打卡记录
     * <AUTHOR> yang.
     * @since 2025/4/29 17:15
     */
    @PostMapping(value = "/getDeptCheckinDailyRecord",produces = "application/json;charset=UTF-8")
    public Result<Map<String, Object>> getDeptCheckinDailyRecord(@RequestBody @Valid CheckinDailyDTO dto) {
        List<CheckinUserDailyExcelVO> list = checkinUserDailyService.getDeptCheckinDailyRecord(dto);
        CheckinRuleWithStatisticsVO statistics = checkinGradeDailyService.getGradeStatisticsByDay(dto.getDeptId(), dto.getDeptName(), dto.getBelongDay());
        Map<String, Object> map = new HashMap<>();
        map.put("records", list);
        map.put("statistics", statistics);
        return Result.build(map);
    }



    /**
     * 获取用户 打卡记录
     * <AUTHOR> yang.
     * @since 2025/4/29 17:15
     */
    @PostMapping(value = "/getUserCheckinDailyRecord",produces = "application/json;charset=UTF-8")
    public Result<CheckinDailyVO> getUserCheckinDailyRecord(@RequestBody @Valid CheckinDailyUserDTO dto) {
        CheckinDailyVO vo = checkinUserDailyService.getUserCheckinDailyRecord(dto);
        return Result.build(vo);
    }


    /**
     * 企微原始打卡数据
     * <AUTHOR> yang.
     * @since 2025/5/14 10:04
     */
    @PostMapping(value = "/getUserCheckinRecord",produces = "application/json;charset=UTF-8")
    public Result<Page<CheckinRecord>> getUserCheckinRecord(@RequestBody @Valid CheckinRecordPageDTO dto) {
        Page<CheckinRecord> pg = checkinRecordService.getUserCheckinRecord(dto);
        return Result.build(pg);
    }

    /**
     * 设置 在校未打卡
     * <AUTHOR> yang.
     * @since 2025/4/30 15:07
     */
    @PostMapping(value = "/changeUserUnSchool",produces = "application/json;charset=UTF-8")
    public Result<Object> changeUserUnSchool(@RequestBody @Valid CheckinUserStatusDTO dto) {
        checkinUserDailyService.changeUserUnSchool(dto);
        Date time = DateUtil.parse(dto.getBelongDay(), DatePattern.NORM_DATE_PATTERN);
        checkinGradeDailyService.saveCheckinGradeDaily(new CheckinGradeDailyStatisticsDTO(
                Arrays.asList(dto.getDeptId()),time,time
        ));
        return Result.build();
    }

    /**
     * 设置 无需打卡
     * <AUTHOR> yang.
     * @since 2025/4/30 15:07
     */
    @PostMapping(value = "/changeUserRemark",produces = "application/json;charset=UTF-8")
    public Result<Object> changeUserRemark(@RequestBody @Valid CheckinUserStatusDTO dto) {
        checkinUserDailyService.changeUserRemark(dto);
        Date time = DateUtil.parse(dto.getBelongDay(), DatePattern.NORM_DATE_PATTERN);
        checkinGradeDailyService.saveCheckinGradeDaily(new CheckinGradeDailyStatisticsDTO(
                Arrays.asList(dto.getDeptId()),time,time
        ));
        return Result.build();
    }

    /**
     * 获取班级指定日期范围的折线图、饼图打卡数据统计
     * <AUTHOR> yang.
     * @since 2025/4/30 17:25
     */
    @PostMapping(value = "/getCheckinRangeStatistics",produces = "application/json;charset=UTF-8")
    public Result<CheckinRangeStatisticsVO> getCheckinRangeStatistics(@RequestBody @Valid CheckinRangeDTO dto){
        CheckinRangeStatisticsVO result = checkinGradeDailyService.getCheckinRangeStatistics(dto);
        return Result.build(result);
    }



    /**
     * 通知未打卡用户
     * <AUTHOR> yang.
     * @since 2025/5/6 10:50
     */
    @PostMapping(value = "/sendWxCpMessageToUnCheckinUser",produces = "application/json;charset=UTF-8")
    public Result<Object> sendWxCpMessageToUnCheckinUser(@RequestBody @Valid MessageUnCheckinUserDTO dto) {
        checkinUserDailyService.sendWxCpMessageToUnCheckinUser(dto);
        return Result.build();
    }

    /**
     * 获取某天 所有院系统计信息
     * <AUTHOR> yang.
     * @since 2025/5/6 14:31
     */
    @PostMapping(value = "/getFacultyStatisticsByDay" ,produces = "application/json;charset=UTF-8")
    public Result<Object> getFacultyStatisticsByDay(@RequestParam("belongDay")String belongDay) {
        Map<String, Object> map = checkinGradeDailyService.getFacultyStatisticsByDay(belongDay);
        return Result.build(map);
    }

    /**
     * 获取某月 所有院系统计信息
     * <AUTHOR> yang.
     * @since 2025/5/6 14:31
     */
    @PostMapping(value = "/getFacultyStatisticsByMonth" ,produces = "application/json;charset=UTF-8")
    public Result<CheckinSchoolStatisticsVO> getFacultyStatisticsByMonth(@RequestBody @Valid CheckinStatisticsMonthDTO dto){
        CheckinSchoolStatisticsVO vo = checkinGradeDailyService.getFacultyStatisticsByMonth(dto);
        return Result.build(vo);
    }

    /**
     * 获取某天 院系下班级的统计信息
     * <AUTHOR> yang.
     * @since 2025/5/6 14:31
     */
    @PostMapping(value = "/getGradeStatisticsByDay" ,produces = "application/json;charset=UTF-8")
    public Result<Object> getGradeStatisticsByDay(@RequestBody @Valid CheckinGradeStatisticsVO dto) {
        Map<String, Object> map = checkinGradeDailyService.getGradeStatisticsByDay(dto);
        return Result.build(map);
    }


    /**
     * 获取某天 院系领导管理的院系的统计信息
     * <AUTHOR> yang.
     * @since 2025/5/6 14:31
     */
    @PostMapping(value = "/getOnlyFacultyStatisticsByDay" ,produces = "application/json;charset=UTF-8")
    public Result<Object> getOnlyFacultyStatisticsByDay(@RequestParam("belongDay")String belongDay){
        String userId = SecurityContextHolder.getUserIdStr();
        Map<String, Object> map = checkinGradeDailyService.getOnlyFacultyStatisticsByDay(userId,belongDay);
        return Result.build(map);
    }

    /**
     * 获取某月 院系领导管理的院系的统计信息
     * <AUTHOR> yang.
     * @since 2025/5/6 14:31
     */
    @PostMapping(value = "/getOnlyFacultyStatisticsByMonth" ,produces = "application/json;charset=UTF-8")
    public Result<Object> getOnlyFacultyStatisticsByMonth(@RequestBody CheckinStatisticsMonthDTO dto){
        String userId = SecurityContextHolder.getUserIdStr();
        CheckinSchoolStatisticsVO map = checkinGradeDailyService.getOnlyFacultyStatisticsByMonth(userId,dto);
        return Result.build(map);
    }

    /**
     * 导出班级打卡记录
     * <AUTHOR> yang.
     * @since 2025/4/30 15:50
     */
    @PostMapping(value = "/exportDeptCheckinRecord",produces = "application/json;charset=UTF-8")
    public void exportDeptCheckinRecord(@RequestBody @Valid CheckinDailyDTO dto, HttpServletResponse response) {
        List<CheckinUserDailyExcelVO> records = checkinUserDailyService.getDeptCheckinDailyRecord(dto);
        ExcelUtils.exportExcelByRecords(dto.getDeptName() + "_" +dto.getBelongDay()+"_"+"打卡记录", records, CheckinUserDailyExcelVO.class, response);
    }

    /**
     * 导出班级日期区间打卡统计
     * <AUTHOR> yang.
     * @since 2025/5/6 10:07
     */
    @PostMapping(value = "/exportDeptDateCheckinRecord",produces = "application/json;charset=UTF-8")
    public void exportDeptDateCheckinRecord(@RequestBody @Valid CheckinRangeDTO dto, HttpServletResponse response){
        List<CheckinUserDateExcelVO> list = checkinGradeDailyService.exportDeptDateCheckinRecord(dto);
        String sheetName = StrUtil.format("{}至{}_{}打卡记录",dto.getStartDay(),dto.getEndDay(),dto.getDeptName());
        ExcelUtils.exportExcelByRecords(sheetName, list, CheckinUserDateExcelVO.class, response);
    }

    /**
     * 导出 全校日打卡记录
     * <AUTHOR> yang.
     * @since 2025/5/9 11:15
     */
    @PostMapping(value = "/exportDayAllCheckinRecord",produces = "application/json;charset=UTF-8")
    public void exportDayAllCheckinRecord(@RequestBody @Valid CheckinDailyDTO dto, HttpServletResponse response){
        List<CheckinUserDailyExcelVO> records = checkinUserDailyService.exportDayAllCheckinRecord(dto);
        ExcelUtils.exportExcelByRecords(dto.getDeptName() + "_" +dto.getBelongDay()+"_"+"打卡记录", records, CheckinUserDailyExcelVO.class, response);
    }

    /**
     * 导出 学院日打卡记录
     * <AUTHOR> yang.
     * @since 2025/5/9 11:15
     */
    @PostMapping(value = "/exportDayCheckinRecord",produces = "application/json;charset=UTF-8")
    public void exportDayCheckinRecord(@RequestBody @Valid CheckinDailyDTO dto, HttpServletResponse response){
        List<CheckinUserDailyExcelVO> records = checkinUserDailyService.exportDayCheckinRecord(dto);
        ExcelUtils.exportExcelByRecords(dto.getDeptName() + "_" +dto.getBelongDay()+"_"+"打卡记录", records, CheckinUserDailyExcelVO.class, response);
    }

    /**
     * 导出 全校月打卡记录
     * <AUTHOR> yang.
     * @since 2025/5/9 13:54
     */
    @PostMapping(value = "/exportMonthAllCheckinRecord",produces = "application/json;charset=UTF-8")
    public void exportMonthAllCheckinRecord(@RequestBody @Valid CheckinRangeDTO dto, HttpServletResponse response){
        List<CheckinUserDateExcelVO> list = checkinGradeDailyService.exportMonthAllCheckinRecord(dto);
        String sheetName = StrUtil.format("{}至{}_{}打卡记录",dto.getStartDay(),dto.getEndDay(),dto.getDeptName());
        ExcelUtils.exportExcelByRecords(sheetName, list, CheckinUserDateExcelVO.class, response);
    }

    /**
     * 导出 学院月打卡记录
     * <AUTHOR> yang.
     * @since 2025/5/9 13:54
     */
    @PostMapping(value = "/exportMonthCheckinRecord",produces = "application/json;charset=UTF-8")
    public void exportMonthCheckinRecord(@RequestBody @Valid CheckinRangeDTO dto, HttpServletResponse response){
        List<CheckinUserDateExcelVO> list = checkinGradeDailyService.exportMonthCheckinRecord(dto);
        String sheetName = StrUtil.format("{}至{}_{}打卡记录",dto.getStartDay(),dto.getEndDay(),dto.getDeptName());
        ExcelUtils.exportExcelByRecords(sheetName, list, CheckinUserDateExcelVO.class, response);
    }

    @PostMapping(value = "/pushWxCpCardMsg",produces = "application/json;charset=UTF-8")
    public Result<Object> pushWxCpCardMsg() {
       log.info("准备开始同步企业微信打卡数据");
        Date now = new Date();
        Date beforeTen = DateUtil.offsetMinute(now, -10);
        checkinDataSyncService.syncCheckinData(beforeTen, now);
        log.info("同步企业微信打卡数据完成");
        return Result.build();
    }

}

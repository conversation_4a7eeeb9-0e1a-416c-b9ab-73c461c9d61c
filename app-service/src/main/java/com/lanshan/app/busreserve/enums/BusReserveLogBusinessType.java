package com.lanshan.app.busreserve.enums;

import com.lanshan.app.busreserve.vo.BusReserveEnumTypeVO;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2025/03/10 20:37
 */

public enum BusReserveLogBusinessType {

    OTHER(0,"其他"),
    INSERT(1,"新增"),
    UPDATE(2,"修改"),
    DELETE(3,"删除");

    public final Integer value;
    public final String label;

    BusReserveLogBusinessType(Integer type, String label) {
        this.value = type;
        this.label = label;
    }

    public BusReserveEnumTypeVO toVO() {
        return new BusReserveEnumTypeVO(this.value, this.label);
    }

    public static List<BusReserveEnumTypeVO> getAllBusinessType() {
        return Arrays.stream(BusReserveLogBusinessType.values())
                .map(BusReserveLogBusinessType::toVO)
                .collect(Collectors.toList());
    }

}

package com.lanshan.app.busreserve.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveDepartureRecordPageDTO;
import com.lanshan.app.busreserve.po.BusReserveDepartureRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.busreserve.vo.BusReserveDepartureRecordExcelVO;

import java.util.List;

/**
 * <p>
 * 班车预约-班车记录 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
public interface BusReserveDepartureRecordService extends IService<BusReserveDepartureRecord> {

    /**
     * 获取班车记录分页列表
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    Page<BusReserveDepartureRecord> getBusReserveDepartureRecordPageList(BusReserveDepartureRecordPageDTO dto);

    /**
     * 导出班车记录
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    List<BusReserveDepartureRecordExcelVO> exportBusReserveDepartureRecord(BusReserveDepartureRecordPageDTO dto);

    void insertBatchDepartureRecord(List<BusReserveDepartureRecord> departureRecords);
}

package com.lanshan.app.busreserve.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveAuditPageDTO;
import com.lanshan.app.busreserve.po.BusReserveAppointment;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 班车预约-代预约人 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
public interface BusReserveAppointmentService extends IService<BusReserveAppointment> {

    /**
     * 获取代预约人分页列表
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    Page<BusReserveAppointment> getBusReserveAppointmentPageList(BusReserveAuditPageDTO dto);
}

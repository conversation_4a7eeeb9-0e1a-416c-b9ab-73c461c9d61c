package com.lanshan.app.busreserve.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.*;
import com.lanshan.app.busreserve.enums.BusReserveLogBusinessType;
import com.lanshan.app.busreserve.po.BusReserveInfo;
import com.lanshan.app.busreserve.dao.BusReserveInfoMapper;
import com.lanshan.app.busreserve.po.BusReserveOperationLog;
import com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord;
import com.lanshan.app.busreserve.po.BusReserveUserRecord;
import com.lanshan.app.busreserve.service.BusReserveInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.busreserve.service.BusReserveOperationLogService;
import com.lanshan.app.busreserve.util.BusReserveUtil;
import com.lanshan.app.busreserve.vo.BusReserveInfoVO;
import com.lanshan.app.common.service.ISysConfigService;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.base.api.qo.CollResult;
import com.lanshan.base.api.utils.Result;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.lanshan.app.busreserve.constant.BusReserveConstant.*;

/**
 * <p>
 * 班车预约-班次信息 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
@Service
public class BusReserveInfoServiceImpl extends ServiceImpl<BusReserveInfoMapper, BusReserveInfo> implements BusReserveInfoService {

    @Resource
    private BusReserveOperationLogService busReserveOperationLogService;
    @Resource
    private BusReserveInfoMapper busReserveInfoMapper;
    @Resource
    private ISysConfigService sysConfigService;

    @Override
    public Page<BusReserveInfo> getBusReserveInfoPageList(BusReserveInfoPageDTO dto) {
        Page<BusReserveInfo> pg = new Page<>(dto.getPage(), dto.getSize());
        LambdaQueryWrapper<BusReserveInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(BusReserveInfo::getStartTimeToday,BusReserveInfo::getCreateTime);
        if (StrUtil.isNotEmpty(dto.getLicensePlate())){
            wrapper.like(BusReserveInfo::getLicensePlate,dto.getLicensePlate());
        }
        if (StrUtil.isNotEmpty(dto.getName())){
            wrapper.like(BusReserveInfo::getName,dto.getName());
        }

        if (StrUtil.isNotEmpty(dto.getDriverName())){
            wrapper.like(BusReserveInfo::getDriverName,dto.getDriverName());
        }
        pg = this.page(pg, wrapper);
        return pg;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertBusReserveInfo(BusReserveInfo busReserveInfo) {
        Date now = new Date();
        busReserveInfo.setLicensePlateToday(busReserveInfo.getLicensePlate());
        busReserveInfo.setDriverUserIdToday(busReserveInfo.getDriverUserId());
        busReserveInfo.setDriverNameToday(busReserveInfo.getDriverName());
        busReserveInfo.setDriverPhoneToday(busReserveInfo.getDriverPhone());
        busReserveInfo.setShowStatus(0);
        busReserveInfo.setCreateTime(now);
        Date startTimeToday = BusReserveUtil.calculateStartTime(busReserveInfo, DateUtil.formatDate(now));
        busReserveInfo.setStartTimeToday(startTimeToday);
        int startTimeSeconds = BusReserveUtil.timeStrToSeconds(busReserveInfo.getStartTime());
        busReserveInfo.setStartTimeSeconds(startTimeSeconds);
        this.save(busReserveInfo);
        //操作日志

        Long id = busReserveInfo.getId();
        String name = busReserveInfo.getName();
        String userId = SecurityContextHolder.getUserIdStr();
        String userName = SecurityContextHolder.getUserName();
        BusReserveOperationLog log = new BusReserveOperationLog(
                BusReserveLogBusinessType.INSERT.value,userId,userName,"",busReserveInfo.getUpdateInfo(),now,id,name
        );
        busReserveOperationLogService.save(log);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBusReserveInfo(BusReserveInfo busReserveInfo) {
        Date now = new Date();
        Long id = busReserveInfo.getId();
        BusReserveInfo currInfo = this.getById(id);
        if (currInfo == null) {
            return;
        }
        if (StrUtil.isNotEmpty(busReserveInfo.getStartTime())) {
            int startTimeSeconds = BusReserveUtil.timeStrToSeconds(busReserveInfo.getStartTime());
            busReserveInfo.setStartTimeSeconds(startTimeSeconds);
        }
        this.updateById(busReserveInfo);
        String name = busReserveInfo.getName();
        String userId = SecurityContextHolder.getUserIdStr();
        String userName = SecurityContextHolder.getUserName();
        BusReserveOperationLog log = new BusReserveOperationLog(
                BusReserveLogBusinessType.UPDATE.value,userId,userName,currInfo.getUpdateInfo(),busReserveInfo.getUpdateInfo(),now,id,name
        );
        busReserveOperationLogService.save(log);

    }

    @Override
    public List<BusReserveInfo> changeBusReserveInfoShowStatus(BusReserveShowStatusDTO dto) {
        Date now = new Date();
        List<BusReserveInfo> records = this.listByIds(dto.getIds());
        if (CollUtil.isEmpty(records)) {
            return List.of();
        }
        String showStatusStr = getShowStatusStr(records.get(0).getShowStatus());
        String showStatusStrCurr = getShowStatusStr(dto.getShowStatus());
        BusReserveInfoServiceImpl proxy = (BusReserveInfoServiceImpl) AopContext.currentProxy();
        proxy.updateBusReserveInfoShowStatus(dto.getIds(), dto.getShowStatus());
        //操作日志
        String userId = SecurityContextHolder.getUserIdStr();
        String userName = SecurityContextHolder.getUserName();
        List<BusReserveOperationLog> logs = new ArrayList<>(records.size());
        BusReserveOperationLog log = null;
        for (BusReserveInfo record : records) {
            Long id = record.getId();
            String name = record.getName();
            log = new BusReserveOperationLog(
                    BusReserveLogBusinessType.UPDATE.value,userId,userName,
                    showStatusStr,showStatusStrCurr,now,id,name
            );
            logs.add(log);
        }
        busReserveOperationLogService.saveBatchBusReserveOperationLog(logs);

        return records;
    }

    @Override
    public Result<Object>  removeBatchBusReserveInfo(List<Long> ids) {
        List<BusReserveInfo> records = this.listByIds(ids);
        boolean allOff = records.stream()
                .allMatch(info -> info.getShowStatus() != null && info.getShowStatus() == 0);
        if (!allOff) {
            return Result.build().error("存在未下线的班次，请先下线后再删除！");
        }
        this.removeByIds(ids);
        return Result.build();
    }

    @Override
    public List<BusReserveInfoVO> getBusReserveInfoByLocationAndDay(BusReserveInfoDTO dto) {
        //查询对应的班次
        List<BusReserveInfoVO> list = busReserveInfoMapper.getBusReserveInfoByLocationAndDay(
            dto.getStartLocation(),dto.getEndLocation(),dto.getStartDay(),dto.getWeek(),dto.getSeconds()
        );
        return list;
    }

    @Override
    public List<BusReserveInfoVO> getBusReserveInfoByDay(String startDay) {
        DateTime time = DateUtil.parseDate(startDay);
        Integer week = DateUtil.dayOfWeekEnum(time).getValue();
        List<BusReserveInfoVO> list = busReserveInfoMapper.getBusReserveInfoByLocationAndDay(
                null,null,startDay,week,null
        );
        return list;
    }

    @Override
    public BusReserveInfoVO getBusReserveInfoByIdAndDay(BusReserveInfoDetailDTO dto) {
        return busReserveInfoMapper.getBusReserveInfoByIdAndDay(dto);
    }

    @Override
    public Result<Object> checkBusReserveInfo(BusReserveInfo info,String startDay) {
        Date now = new Date();
        if (info == null || info.getShowStatus().equals(0)){
            return Result.build().error("班次已下线！");
        }
        //判断是否过了截止时间
        Date startTime = BusReserveUtil.calculateStartTime(info, startDay);
        if (startTime == null){
            return Result.build().error("已到预约截止时间！");
        }
        Date deadline = DateUtil.offsetHour(startTime, - info.getDeadlineHour());
        if (DateUtil.compare(now,deadline) >= 0){
            return Result.build().error("已到预约截止时间！");
        }
        return Result.build();
    }

    @Override
    public List<BusReserveInfoVO> getBusReserveInfoByDriver(BusReserveDriverDTO dto) {
        return busReserveInfoMapper.getBusReserveInfoByDriver(dto);
    }

    @Override
    public void updateCheckCode(Long infoId, String code,String codeUri) {
        busReserveInfoMapper.updateCheckCode(infoId, code,codeUri);
    }

    @Override
    public String getBusReserveCheckCode(ReserveInfoRecordDTO dto) {
        Long infoId = dto.getInfoId();
        String startDay = dto.getStartDay();
        BusReserveInfo info = this.getById(infoId);
        if (info == null || info.getShowStatus() == 0){
            return "";
        }
        final String corpId = sysConfigService.selectConfigByKey(CORP_ID_KEY);
        final String agentId = sysConfigService.selectConfigByKey(AGENT_ID_KEY);
        final String template = sysConfigService.selectConfigByKey(CHECK_CODE_URI);
        String code = IdUtil.fastSimpleUUID();
        String codeUri = StrUtil.format(template, agentId, corpId, code,infoId,startDay);
        this.updateCheckCode(infoId,code,codeUri);
        return codeUri;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateBusReserveInfoShowStatus(List<Long> ids, Integer showStatus){
        busReserveInfoMapper.updateBusReserveInfoShowStatus(ids, showStatus);
    }

    public static String getShowStatusStr(Integer showStatus){
        if (showStatus == null) {
            return "";
        }
        if (showStatus == 1) {
            return "运行状态: 上线";
        }else {
            return "运行状态: 下线";
        }
    }
}

package com.lanshan.app.busreserve.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveOperationLogPageDTO;
import com.lanshan.app.busreserve.po.BusReserveOperationLog;
import com.lanshan.app.busreserve.dao.BusReserveOperationLogMapper;
import com.lanshan.app.busreserve.service.BusReserveOperationLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.common.utils.IdGenerator;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 班车预约-操作日志 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-21
 */
@Service
public class BusReserveOperationLogServiceImpl extends ServiceImpl<BusReserveOperationLogMapper, BusReserveOperationLog> implements BusReserveOperationLogService {

    @Resource
    private BusReserveOperationLogMapper busReserveOperationLogMapper;

    @Override
    public Page<BusReserveOperationLog> getBusReserveOperationLogPageList(BusReserveOperationLogPageDTO dto) {
        Page<BusReserveOperationLog> pg = new Page<>(dto.getPage(), dto.getSize());
        LambdaQueryWrapper<BusReserveOperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(BusReserveOperationLog::getCreateTime,BusReserveOperationLog::getId);
        if (dto.getBusinessType() != null) {
            wrapper.eq(BusReserveOperationLog::getBusinessType,dto.getBusinessType());
        }
        if (StrUtil.isNotEmpty(dto.getInfoName())){
            wrapper.like(BusReserveOperationLog::getInfoName,dto.getInfoName());
        }
        if (StrUtil.isNotEmpty(dto.getUserName())){
            wrapper.like(BusReserveOperationLog::getUserName,dto.getUserName());
        }
        if (StrUtil.isNotEmpty(dto.getStartTime())){
            wrapper.ge(BusReserveOperationLog::getCreateTime,dto.getStartTime());
        }
        if (StrUtil.isNotEmpty(dto.getEndTime())){
            wrapper.le(BusReserveOperationLog::getCreateTime,dto.getEndTime());
        }

        pg = this.page(pg,wrapper);
        return pg;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchBusReserveOperationLog(List<BusReserveOperationLog> records) {
        if (CollUtil.isEmpty(records)){
            return;
        }
        records.forEach(record -> record.setId(IdGenerator.generateId()));
        busReserveOperationLogMapper.insertBatch(records);
    }

}

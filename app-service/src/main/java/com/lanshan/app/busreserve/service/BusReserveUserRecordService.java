package com.lanshan.app.busreserve.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.*;
import com.lanshan.app.busreserve.po.BusReserveUserRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.busreserve.vo.*;
import com.lanshan.base.api.qo.CollResult;
import com.lanshan.base.api.utils.Result;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 班车预约-企微用户预约记录 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
public interface BusReserveUserRecordService extends IService<BusReserveUserRecord> {

    /**
     * 发车日期查询企微用户乘坐记录
     * <AUTHOR> yang.
     * @since 2025/5/21 17:39
     */
    List<BusReserveUserRecord> getBusReserveUserRecordByDay(String startDay);

    /**
     * 企微用户乘坐记录分页列表
     * <AUTHOR> yang.
     * @since 2025/5/21 17:39
     */
    Page<BusReserveUserRecord> getBusReserveUserRecordPageList(BusReserveUserRecordPageDTO dto);

    /**
     * 企微用户乘坐记录
     * <AUTHOR> yang.
     * @since 2025/5/21 17:39
     */
    List<BusReserveUserRecordExcelVO> exportBusReserveUserRecord(BusReserveUserRecordPageDTO dto);

    /**
     * 企微用户预约记录
     * <AUTHOR> yang.
     * @since 2025/5/21 17:39
     */
    Page<BusReserveUserRecord> getMineBusReserveUserRecord(BusReserveUserRecordPageDTO dto);

    /**
     * 企微用户预约详情
     * <AUTHOR> yang.
     * @since 2025/5/21 17:51
     */
    BusReserveUserRecordDetailVO getMineBusReserveUserRecordDetail(Long id);

    /**
     * 批量修改状态
     * <AUTHOR> yang.
     * @since 2025/5/22 10:33
     * @param ids 预约记录id集合
     * @param reserveStatus 预约状态
     */
    boolean updateBusReserveUserRecord(Collection<Long> ids, Integer reserveStatus);

    /**
     * 根据 班次id集合、发车日期、预约状态查询预约记录
     * <AUTHOR> yang.
     * @since 2025/5/22 16:43
     * @param ids 班次id集合
     * @param reserveStatus 预约状态
     */
    List<BusReserveUserRecord> getUserRecordByInfoIdsAndReserveStatus(Collection<Long> ids,String startDay,Integer reserveStatus);

    /**
     * 根据班次id，发车日期查询未取消的记录
     * <AUTHOR> yang.
     * @since 2025/5/28 10:27
     */
    List<BusReserveUserRecord> getUserRecordByInfoAndDay(Long infoId,String startDay);

    /**
     * 登录用户信息
     * <AUTHOR> yang.
     * @since 2025/5/23 16:54
     */
    BusReserveUserVO getBusReserveUserInfo(String userId);

    /**
     * 判断是否能预约班次
     * <AUTHOR> yang.
     * @since 2025/5/23 17:20
     */
    Boolean checkBusReserveUserAuth(BusReserveUserAuthDTO dto,BusReserveInfoVO info);

    /**
     * 新增预约记录
     * <AUTHOR> yang.
     * @since 2025/5/27 9:32
     */
    void busReserve(BusReserveDTO dto, BusReserveInfoVO info);

    /**
     * 改签预约记录
     * <AUTHOR> yang.
     * @since 2025/5/27 9:32
     * @param dto 改签信息
     * @param info 新班次信息
     * @param recordOld 原预约记录
     */
    void changeReserve(BusReserveChangeDTO dto, BusReserveInfoVO info,BusReserveUserRecordDetailVO recordOld);

    /**
     * 根据 班次id、发车日期、预约状态查询预约记录
     * <AUTHOR> yang.
     * @since 2025/5/22 16:43
     */
    int getUserCountByInfoIdsAndReserveStatus(Long infoId, String startDay, Integer reserveStatus);

    /**
     * 添加坐而不约记录
     * <AUTHOR> yang.
     * @since 2025/5/28 16:21
     */
    void insertSitNotReserveUserRecord(BusReserveInfoCodeDTO dto);


    BusReserveUserRecord getBusReserveRecordByLocation(Long infoId,String startLocation, String endLocation, String userId, String startDay);

    /**
     * 查询发车时间区间的记录
     * <AUTHOR> yang.
     * @since 2025/6/3 10:2
     */
    List<BusReserveUserRecordVO> getWaitRecordByDate(String startTime, String endTime, Integer reserveStatus);

    List<BusReserveUserRecordVO> getTomorrowWaitRecord(String tomorrow, Integer reserveStatus);
}

package com.lanshan.app.busreserve.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * 班车预约-审核人
 * <AUTHOR> yang.
 * @since 2025-05-20
 */

@Data
@TableName(value = "bus_reserve_audit", autoResultMap = true, schema = "standard_app")
public class BusReserveAudit implements Serializable {

	private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    //审核人学工号
    private String userId;

    //审核人昵称
    private String userName;

    //手机号
    private String phone;

    //创建时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date createTime;



}

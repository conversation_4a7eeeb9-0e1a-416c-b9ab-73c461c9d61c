package com.lanshan.app.busreserve.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveAuditPageDTO;
import com.lanshan.app.busreserve.po.BusReserveAppointment;
import com.lanshan.app.busreserve.service.BusReserveAppointmentService;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 代预约人
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/busReserve/appointment")
public class BusReserveAppointmentController {

    @Resource
    private BusReserveAppointmentService busReserveAppointmentService;

    /**
     * 获取代预约人分页列表
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    @PostMapping(value = "/getBusReserveAppointmentPageList" ,produces = "application/json;charset=UTF-8")
    public Result<Page<BusReserveAppointment>> getBusReserveAppointmentPageList(@RequestBody BusReserveAuditPageDTO dto){
        Page<BusReserveAppointment> pg = busReserveAppointmentService.getBusReserveAppointmentPageList(dto);
        return Result.build(pg);
    }

    /**
     * 批量新增代预约人
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    @PostMapping(value = "/saveBatchBusReserveAppointment" ,produces = "application/json;charset=UTF-8")
    public Result<Object> saveBatchBusReserveAppointment(@RequestBody List<BusReserveAppointment> list){
        Date now = new Date();
        list.forEach(b -> b.setCreateTime(now));
        busReserveAppointmentService.saveBatch(list);
        return Result.build();
    }

    /**
     * 批量删除代预约人
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    @PostMapping(value = "/removeBusReserveAppointmentByIds" ,produces = "application/json;charset=UTF-8")
    public Result<Object> removeBusReserveAppointmentByIds(@RequestBody Collection<String> ids){
        busReserveAppointmentService.removeByIds(ids);
        return Result.build();
    }

}

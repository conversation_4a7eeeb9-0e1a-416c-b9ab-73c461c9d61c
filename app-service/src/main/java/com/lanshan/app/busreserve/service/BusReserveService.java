package com.lanshan.app.busreserve.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lanshan.app.busreserve.dto.*;
import com.lanshan.app.busreserve.enums.BusReserveStatus;
import com.lanshan.app.busreserve.po.BusReserveDepartureRecord;
import com.lanshan.app.busreserve.po.BusReserveInfo;
import com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord;
import com.lanshan.app.busreserve.po.BusReserveUserRecord;
import com.lanshan.app.busreserve.util.BusReserveUtil;
import com.lanshan.app.busreserve.vo.*;
import com.lanshan.app.checkin.util.CheckinUtil;
import com.lanshan.app.common.utils.JsonUtil;
import com.lanshan.app.common.utils.ThreadPoolUtil;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.vo.user.UserInfoPartVO;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.Collator;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Service
public class BusReserveService {

    @Resource
    private BusReserveInfoService busReserveInfoService;
    @Autowired
    private BusReserveUserRecordService userRecordService;
    @Autowired
    private BusReserveOutsideUserRecordService outsideUserRecordService;
    @Autowired
    private BusReserveSseService sseService;
    @Resource
    private BusReserveDepartureRecordService busReserveDepartureRecordService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private BusReserveMessageService busReserveMessageService;

    private static final String LOCK_PREFIX = "lock_busReserve";


    /**
     * 获取班车的预约记录
     * <AUTHOR> yang.
     * @since 2025/5/22 19:15
     */
    public Map<String ,Object> getBusReserveInfoRecord(ReserveInfoRecordDTO dto) {
        Map<String ,Object> result = new HashMap<>(2);
        List<ReserveInfoRecordVO> list = new ArrayList<>();
        ReserveInfoRecordVO vo = null;
        Long infoId = dto.getInfoId();
        String startDay = dto.getStartDay();
        BusReserveInfo info = busReserveInfoService.getById(infoId);
        List<BusReserveUserRecord> records = userRecordService.getUserRecordByInfoAndDay(infoId, startDay);
        List<BusReserveOutsideUserRecord> outsideRecords = outsideUserRecordService.getOutsideUserRecordByInfoAndDay(infoId, startDay);
        for (BusReserveUserRecord record : records) {
            vo = new ReserveInfoRecordVO();
            BeanUtil.copyProperties(record, vo);
            vo.setOutside(0);
            vo.setStartLocation(info.getStartLocation());
            vo.setEndLocation(info.getEndLocation());
            list.add(vo);
        }
        for (BusReserveOutsideUserRecord outsideRecord : outsideRecords) {
            vo = new ReserveInfoRecordVO();
            BeanUtil.copyProperties(outsideRecord, vo);
            vo.setOutside(1);
            vo.setUserId(outsideRecord.getPhone());
            vo.setStartLocation(info.getStartLocation());
            vo.setEndLocation(info.getEndLocation());
            list.add(vo);
        }

        // 按status分组
        Map<Integer, List<ReserveInfoRecordVO>> groupedMap = list.stream()
                .collect(Collectors.groupingBy(ReserveInfoRecordVO::getStatus));

        // 未核验：status == 1，按 userName 拼音排序
        List<ReserveInfoRecordVO> group1 = groupedMap.getOrDefault(1, Collections.emptyList());
        group1.sort(Comparator.comparing(ReserveInfoRecordVO::getUserName, Collator.getInstance(Locale.CHINA)));

        // 已核验：status == 2，按 checkTime 倒序
        List<ReserveInfoRecordVO> group2 = groupedMap.getOrDefault(2, Collections.emptyList());
        group2.sort(Comparator.comparing(ReserveInfoRecordVO::getCheckTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed());

        // 坐而不约：status == 3，按 createTime 倒序
        List<ReserveInfoRecordVO> group3 = groupedMap.getOrDefault(3, Collections.emptyList());
        group3.sort(Comparator.comparing(ReserveInfoRecordVO::getCreateTime, Comparator.nullsLast(Comparator.naturalOrder())).reversed());

        //中途上车点统计
        List<String> allPoints = info.getHalfwayLocation();
        if (allPoints == null) {
            allPoints = Collections.emptyList();
        }
        //筛选、分组、计数
        Map<String, Long> groupedCount = group1.stream()
                // 只保留未核验(1) 且 halfwayLocation 不为 null 的记录
                .filter(re ->  re.getHalfwayLocation() != null)
                // 按中途上车点分组并计数
                .collect(Collectors.groupingBy(
                        ReserveInfoRecordVO::getHalfwayLocation,
                        Collectors.counting()
                ));
        List<BusLocationVO> group = new ArrayList<>(allPoints.size());
        for (String point : allPoints) {
            Long count = groupedCount.getOrDefault(point, 0L);
            group.add(new BusLocationVO(point, count));
        }
        result.put("notCheck", group1);
        result.put("checked", group2);
        result.put("notCome", group3);
        result.put("locationGroup", group);
        return result;
    }


    /**
     * 班车下线，把未乘车的改为已取消
     * <AUTHOR> yang.
     * @since 2025/6/11 15:02
     */
    public void changeBusReserveOffline(List<Long> ids) {
        //企微用户
        List<BusReserveUserRecord> records = userRecordService.getUserRecordByInfoIdsAndReserveStatus(ids,null, BusReserveStatus.WAIT.value);
        //外部用户
        List<BusReserveOutsideUserRecord> outsideRecords = outsideUserRecordService.getOutSideUserRecordByInfoIdsAndReserveStatus(ids,null, BusReserveStatus.WAIT.value);
        List<Long> recordIds = records.stream().map(BusReserveUserRecord::getId).collect(Collectors.toList());
        List<Long> outsideRecordIds = outsideRecords.stream().map(BusReserveOutsideUserRecord::getId).collect(Collectors.toList());
        BusReserveService proxy = (BusReserveService) AopContext.currentProxy();
        proxy.updateBusReserveUserRecord(recordIds,outsideRecordIds,BusReserveStatus.CANCEL.value);
    }

    /**
     * 批量修改状态
     * <AUTHOR> yang.
     * @since 2025/6/11 15:07
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBusReserveUserRecord(List<Long> ids,List<Long> outsideIds,Integer reserveStatus){
        userRecordService.updateBusReserveUserRecord(ids,reserveStatus);
        outsideUserRecordService.updateBusReserveOutsideUserRecord(outsideIds,reserveStatus);
    }

    /**
     * 根据始发-终点，班次日期，查询班车信息
     * <AUTHOR> yang.
     * @since 2025/5/23 16:11
     */
    public List<BusReserveInfoVO> getBusReserveInfoByLocationAndDay(BusReserveInfoDTO dto){
        List<BusReserveInfoVO> list = busReserveInfoService.getBusReserveInfoByLocationAndDay(dto);
        if (CollUtil.isEmpty(list)){
            return list;
        }
        List<Long> ids = list.stream().map(BusReserveInfoVO::getId).collect(Collectors.toList());
        //查询今天 班次 的剩余座位数，从企微用户和外部用户记录中，查待乘车的
        //企微用户
        List<BusReserveUserRecord> records = userRecordService.getUserRecordByInfoIdsAndReserveStatus(ids,dto.getStartDay(), BusReserveStatus.WAIT.value);
        //外部用户
        List<BusReserveOutsideUserRecord> outsideRecords = outsideUserRecordService.getOutSideUserRecordByInfoIdsAndReserveStatus(ids,dto.getStartDay(), BusReserveStatus.WAIT.value);

        //根据班次id分组，统计次数
        Map<Long, Integer> userRecordMap = records.stream()
                .collect(Collectors.groupingBy(BusReserveUserRecord::getInfoId,Collectors.collectingAndThen(Collectors.counting(), Long::intValue)));
        Map<Long, Integer> outsideUserRecordMap = outsideRecords.stream()
                .collect(Collectors.groupingBy(BusReserveOutsideUserRecord::getInfoId,Collectors.collectingAndThen(Collectors.counting(), Long::intValue)));
        for (BusReserveInfoVO info : list) {
            Long id = info.getId();
            Integer userCount = info.getUserCount();
            Integer count = userRecordMap.getOrDefault(id, 0);
            Integer outsideCount = outsideUserRecordMap.getOrDefault(id, 0);
            int seatCount = userCount - count - outsideCount;
            seatCount = Math.max(seatCount, 0);
            info.setSeatCount(seatCount);
        }

        return list;
    }

    /**
     * 根据班次id，班次日期，查询班车详情
     * <AUTHOR> yang.
     * @since 2025/5/23 16:11
     */
    public BusReserveInfoVO getBusReserveInfoByIdAndDay(BusReserveInfoDetailDTO dto){
        BusReserveInfoVO detail = busReserveInfoService.getBusReserveInfoByIdAndDay(dto);
        if (detail == null){
            return null;
        }
        Long id = dto.getId();
        String startDay = dto.getStartDay();
        //实时计算剩余座位
        //班车座位数量
        Integer userCount = detail.getUserCount();
        //企微用户在该班次 该日期下 待乘车的数量
        int corpCount = userRecordService.getUserCountByInfoIdsAndReserveStatus(id,startDay,BusReserveStatus.WAIT.value);
        //外部用户在该班次 该日期下 待乘车的数量
        int outsideCount = outsideUserRecordService.getOutSideUserCountByInfoIdsAndReserveStatus(id,startDay,BusReserveStatus.WAIT.value);
        int seatCount = userCount - corpCount - outsideCount;
        seatCount = Math.max(seatCount, 0);
        detail.setSeatCount(seatCount);
        return detail;
    }


    /**
     * 判断是否能预约班次
     * <AUTHOR> yang.
     * @since 2025/5/23 17:20
     */
    public Result<Object> checkBusReserveUserAuth(BusReserveUserAuthDTO dto,BusReserveInfoVO info){

        Result<Object> r = busReserveInfoService.checkBusReserveInfo(info, dto.getStartDay());
        if (r.hasError()){
            return r;
        }
        Integer outside = dto.getOutside();
        boolean flag ;
        if (outside == 1){
            flag= outsideUserRecordService.checkBusReserveOutsideUserAuth(dto,info);
        }else {
            flag= userRecordService.checkBusReserveUserAuth(dto,info);
        }
        if (!flag){
            return Result.build().error("当天已有预约记录，不可重复预约");
        }

        return Result.build();
    }

    /**
     * 预约班车 分布式锁 控制并发，保证同一班次同一天的预约流程串行执行
     * <AUTHOR> yang.
     * @since 2025/5/26 14:10
     */
    public Result<Object> busReserve(BusReserveDTO dto) {
        String lockKey = LOCK_PREFIX +":infoId_"+  dto.getInfoId() +":startDay_"+ dto.getStartDay();
        RLock lock = redissonClient.getLock(lockKey);
        boolean acquired = false;
        try {
            // 最多等 500ms，持锁 5s 自动释放
            acquired = lock.tryLock(500, 5, TimeUnit.SECONDS);
            if (!acquired) {
                return Result.build().error("系统繁忙，请稍后重试");
            }
            Long infoId = dto.getInfoId();
            String startDay = dto.getStartDay();
            Integer outside = dto.getOutside();
            List<String> userIds = dto.getUsers().stream().map(UserInfoPartVO::getUserId).collect(Collectors.toList());

            Integer reserveStatus = BusReserveStatus.WAIT.value;
            BusReserveInfoDetailDTO detailDTO = new BusReserveInfoDetailDTO();
            detailDTO.setId(infoId);
            detailDTO.setStartDay(startDay);
            BusReserveInfoVO info = busReserveInfoService.getBusReserveInfoByIdAndDay(detailDTO);
            //校验乘车人是否能预约
            BusReserveUserAuthDTO authDTO = new BusReserveUserAuthDTO(outside,userIds,infoId,startDay);
            Result<Object> r = this.checkBusReserveUserAuth(authDTO, info);
            if (r.hasError()){
                return r;
            }

            //实时计算剩余座位
            //班车座位数量
            Integer userCount = info.getUserCount();
            //企微用户在该班次 该日期下 待乘车的数量
            int corpCount = userRecordService.getUserCountByInfoIdsAndReserveStatus(infoId,startDay,reserveStatus);
            //外部用户在该班次 该日期下 待乘车的数量
            int outsideCount = outsideUserRecordService.getOutSideUserCountByInfoIdsAndReserveStatus(infoId,startDay,reserveStatus);
            int seatCount = userCount - corpCount - outsideCount - userIds.size();
            if (seatCount < 0){
                return Result.build().error("当前座位不足！");
            }
            info.setSeatCount(seatCount);
            // 插入预约记录  细分为子服务事务控制

            if (outside == 1){
                //外部用户
                outsideUserRecordService.busReserve(dto,info);
            }else {
                //企微用户
                userRecordService.busReserve(dto,info);
            }
            //发消息给调度员
            busReserveMessageService.sendDispatcherMessage(info,startDay);
            return Result.build();

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return Result.build().error("操作被中断，请重试");
        } finally {
            if (acquired && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    /**
     * 改签，应该复用和“预约”相同的分布式锁 (废弃)
     * <AUTHOR> yang.
     * @since 2025/5/27 10:43
     */
    @Deprecated
    public Result<Object> changeReserve(BusReserveChangeDTO dto){
        String oldLockKey = LOCK_PREFIX +":infoId_"+  dto.getOldInfoId() +":startDay_"+ dto.getStartDay();
        String newLockKey = LOCK_PREFIX +":infoId_"+  dto.getInfoId() +":startDay_"+ dto.getStartDay();

        RLock locOld = redissonClient.getLock(oldLockKey);
        RLock lockNew = redissonClient.getLock(newLockKey);
        RLock multiLock = redissonClient.getMultiLock(locOld, lockNew);

        boolean acquired = false;
        try {
            acquired = multiLock.tryLock(500, 5, TimeUnit.SECONDS);
            if (!acquired) {
                return Result.build().error("系统繁忙，请稍后重试");
            }
            //查询旧预约记录
            BusReserveUserRecordDetailVO recordOld = userRecordService.getMineBusReserveUserRecordDetail(dto.getRecordId());
            if (recordOld == null || CollUtil.isEmpty(recordOld.getUserRecords())){
                return Result.build().error("未查询到旧预约记录，改签失败");
            }
            Integer changeCount = recordOld.getRecord().getChangeCount();
            if (changeCount > 0){
                return Result.build().error("每趟班次仅能改签一次");
            }
            List<BusReserveUserRecordVO> recordsOld = recordOld.getUserRecords();
            List<String> userIds = recordsOld.stream().map(BusReserveUserRecordVO::getUserId).collect(Collectors.toList());
            //查询新班次的信息
            Long infoId = dto.getInfoId();
            String startDay = dto.getStartDay();
            BusReserveInfoDetailDTO detailDTO = new BusReserveInfoDetailDTO();
            detailDTO.setId(infoId);
            detailDTO.setStartDay(startDay);
            BusReserveInfoVO info = busReserveInfoService.getBusReserveInfoByIdAndDay(detailDTO);
            //校验新班次能否预约
            //校验乘车人是否能预约
            BusReserveUserAuthDTO authDTO = new BusReserveUserAuthDTO(0,userIds,infoId,startDay);
            Result<Object> r = this.checkBusReserveUserAuth(authDTO, info);
            if (r.hasError()){
                return r;
            }

            //实时计算剩余座位
            //班车座位数量
            Integer userCount = info.getUserCount();
            //企微用户在该班次 该日期下 待乘车的数量
            int corpCount = userRecordService.getUserCountByInfoIdsAndReserveStatus(infoId,startDay,BusReserveStatus.WAIT.value);
            //外部用户在该班次 该日期下 待乘车的数量
            int outsideCount = outsideUserRecordService.getOutSideUserCountByInfoIdsAndReserveStatus(infoId,startDay,BusReserveStatus.WAIT.value);
            int seatCount = userCount - corpCount - outsideCount - userIds.size();
            if (seatCount <= 0){
                return Result.build().error("当前座位不足！");
            }
            info.setSeatCount(seatCount);
            // 修改预约记录  细分为子服务事务控制
            userRecordService.changeReserve(dto,info,recordOld);
            //异步发送消息提醒给所有改签人

            return Result.build();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return Result.build().error("操作被中断，请重试");
        } finally {
            if (acquired && multiLock.isHeldByCurrentThread()) {
                multiLock.unlock();
            }
        }
    }

    /**
     * 获取预约码对应的预约记录
     * <AUTHOR> yang.
     * @since 2025/5/28 14:34
     */
    public ReserveInfoRecordVO getBusReserveInfoByCode(BusReserveInfoCodeDTO dto) {
        ReserveInfoRecordVO vo = null;
        Integer outside = dto.getOutside();
        String userId = dto.getUserId();
        String code = dto.getCode();
        String startDay = dto.getStartDay();
        BusReserveInfo info = busReserveInfoService.getOne(new LambdaQueryWrapper<BusReserveInfo>().eq(BusReserveInfo::getCheckCode, code));
        if (info == null){
            return null;
        }
        Long infoId = info.getId();
        if (outside == 0){
            //企微用户
            LambdaQueryWrapper<BusReserveUserRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper
                    .eq(BusReserveUserRecord::getUserId, userId)
                    .eq(BusReserveUserRecord::getInfoId, infoId)
                    .eq(BusReserveUserRecord::getStartDay, startDay)
                    .ne(BusReserveUserRecord::getReserveStatus,BusReserveStatus.CANCEL.value);
            BusReserveUserRecord record = userRecordService.getOne(wrapper);
            if (record != null){
                vo = new ReserveInfoRecordVO();
                BeanUtil.copyProperties(record, vo);
                vo.setOutside(0);
                vo.setStartLocation(info.getStartLocation());
                vo.setEndLocation(info.getEndLocation());
            }
        }else {
            LambdaQueryWrapper<BusReserveOutsideUserRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper
                    .eq(BusReserveOutsideUserRecord::getPhone, userId)
                    .eq(BusReserveOutsideUserRecord::getInfoId, infoId)
                    .eq(BusReserveOutsideUserRecord::getStartDay, startDay)
                    .ne(BusReserveOutsideUserRecord::getReserveStatus,BusReserveStatus.CANCEL.value);
            BusReserveOutsideUserRecord record = outsideUserRecordService.getOne(wrapper);
            if (record != null){
                vo = new ReserveInfoRecordVO();
                BeanUtil.copyProperties(record, vo);
                vo.setUserId(record.getPhone());
                vo.setOutside(1);
                vo.setStartLocation(info.getStartLocation());
                vo.setEndLocation(info.getEndLocation());
            }
        }
        return vo;
    }

    /**
     * 当天同向是否还有未乘车预约记录
     * <AUTHOR> yang.
     * @since 2025/5/29 16:27
     */
    public ReserveInfoRecordVO getBusReserveRecordByLocation(Long infoId,String startLocation,String endLocation,String userId,Integer outSide,String startDay){
        ReserveInfoRecordVO vo = null;
        if (outSide == 0){
            //企微用户
            BusReserveUserRecord record = userRecordService.getBusReserveRecordByLocation(infoId,startLocation,endLocation,userId,startDay);
            if (record != null){
                vo = new ReserveInfoRecordVO();
                BeanUtil.copyProperties(record, vo);
                vo.setOutside(0);
                vo.setStartLocation(startLocation);
                vo.setEndLocation(endLocation);
            }
        }else {
            BusReserveOutsideUserRecord record = outsideUserRecordService.getBusReserveRecordByLocation(infoId,startLocation,endLocation,userId,startDay);
            if (record != null){
                vo = new ReserveInfoRecordVO();
                BeanUtil.copyProperties(record, vo);
                vo.setOutside(1);
                vo.setStartLocation(startLocation);
                vo.setEndLocation(endLocation);
                vo.setUserId(record.getPhone());
            }
        }

        return vo;
    }

    /**
     * 乘客扫码
     * <AUTHOR> yang.
     * @since 2025/5/28 14:34
     */
    public Result<Object> scanCheckCode(BusReserveInfoCodeDTO dto) {
        String msg = "";
        Integer outside = dto.getOutside();
        String code = dto.getCode();
        String startDay = dto.getStartDay();
        BusReserveInfo info = busReserveInfoService.getOne(new LambdaQueryWrapper<BusReserveInfo>().eq(BusReserveInfo::getCheckCode, code));
        if (info == null){
            return Result.build().error(-1,"核验码无效",null);
        }
        //没查询到记录，给司机发sse消息

        ReserveInfoRecordVO record = this.getBusReserveInfoByCode(dto);
        String driverUserId = info.getDriverUserIdToday();
        String driverKey = sseService.getDriverKey(driverUserId, code);
        if (record == null){
            //给司机发sse消息
            sseService.notifyDriverScan(driverKey, JsonUtil.toJson(dto));
            msg = "您没有预约今日本班次 或使用企业微信扫码再试，请等待司机操作";
            return Result.build().error(-2,msg,null);
        }
        //查询当天同向是否还有未乘车预约记录
        ReserveInfoRecordVO recordOther = this.getBusReserveRecordByLocation(
                info.getId(),info.getStartLocation(),info.getEndLocation(),record.getUserId(),record.getOutside(),startDay
        );
        if (recordOther != null){
            dto.setOldRecordId(recordOther.getId());
            sseService.notifyDriverScan(driverKey, JsonUtil.toJson(dto));
            msg = StrUtil.format("您今日预约的班次为{}，请等待司机操作",recordOther.getInfoName());
            return Result.build().error(-3,msg,null);
        }

        //修改为已核验
        Date now = new Date();
        if (outside == 0){
            //企微用户
            LambdaUpdateWrapper<BusReserveUserRecord> wrapper = new LambdaUpdateWrapper<>();
            wrapper
                    .eq(BusReserveUserRecord::getId,record.getId())
                    .set(BusReserveUserRecord::getReserveStatus,BusReserveStatus.NORMAL.value)
                    .set(BusReserveUserRecord::getCheckTime,now);
            userRecordService.update(wrapper);
        }else {
            LambdaUpdateWrapper<BusReserveOutsideUserRecord> wrapper = new LambdaUpdateWrapper<>();
            wrapper
                    .eq(BusReserveOutsideUserRecord::getId,record.getId())
                    .set(BusReserveOutsideUserRecord::getReserveStatus,BusReserveStatus.NORMAL.value)
                    .set(BusReserveOutsideUserRecord::getCheckTime,now);
            outsideUserRecordService.update(wrapper);
        }

        return Result.build(msg,info);

    }

    /**
     * 司机 确认|拒绝
     * <AUTHOR> yang.
     * @since 2025/5/28 16:13
     */
    public Result<Object> driverConfirm(BusReserveInfoCodeDTO dto) {
        String userId = dto.getUserId();
        String code = dto.getCode();
        String userKey = sseService.getUserKey(userId, code);
        BusReserveService proxy = (BusReserveService) AopContext.currentProxy();
        proxy.driverConfirmUpdateRecord(dto);
        //给乘客发sse消息
        BusReserveInfo info = busReserveInfoService.getById(dto.getInfoId());
        BusReserveInfoUserDTO userDTO = new BusReserveInfoUserDTO(dto.getInfoId(), info.getName(), dto.getConfirmStatus());
        sseService.notifyPassengerResult(userKey, JsonUtil.toJson(userDTO));
        return Result.build();
    }

    /**
     * 司机 确认时，取消旧记录，新增新记录
     * <AUTHOR> yang.
     * @since 2025/5/28 16:13
     */
    @Transactional(rollbackFor = Exception.class)
    public void driverConfirmUpdateRecord(BusReserveInfoCodeDTO dto){
        Integer confirmStatus = dto.getConfirmStatus();
        Integer outside = dto.getOutside();
        Long oldRecordId = dto.getOldRecordId();
        if (confirmStatus == 1){
            //确认,根据outside添加预约记录
            if (outside == 0){
                if (oldRecordId != null){
                    userRecordService.updateBusReserveUserRecord(Arrays.asList(oldRecordId), BusReserveStatus.CANCEL.value);
                }
                //企微用户
                userRecordService.insertSitNotReserveUserRecord(dto);
            }else {
                if (oldRecordId != null){
                    outsideUserRecordService.updateBusReserveOutsideUserRecord(Arrays.asList(oldRecordId), BusReserveStatus.CANCEL.value);
                }
                //外部用户
                outsideUserRecordService.insertSitNotReserveOutsideUserRecord(dto);
            }
        }
    }


    /**
     * 批量新增|修改 预约记录、发车记录、班车配置
     * <AUTHOR> yang.
     * @since 2025/6/3 15:33
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchRecord(
            List<Long> updateRecords, List<Long> updateOutsideRecords, List<BusReserveDepartureRecord> departureRecords, List<BusReserveInfo> list
    ){
        userRecordService.updateBusReserveUserRecord(updateRecords,BusReserveStatus.RESERVE_NOT_SIT.value);
        outsideUserRecordService.updateBusReserveOutsideUserRecord(updateOutsideRecords,BusReserveStatus.RESERVE_NOT_SIT.value);
        busReserveDepartureRecordService.insertBatchDepartureRecord(departureRecords);
        busReserveInfoService.updateBatchById(list);
    }

}

package com.lanshan.app.busreserve.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class BusReserveInfoCodeDTO implements Serializable {
    private static final long serialVersionUID = 349098901669614034L;

    private String code;

    //班次id
    private Long infoId;

    //当天其他未乘坐的记录id
    private Long oldRecordId;

    private String startDay;

    //userId | phone
    private String userId;

    private String name;

    private Long deptId;

    private String deptName;

    private String gender;

    //是否外部用户  1是 0否
    private Integer outside;

    //职务
    private String position;

    //审核人id
    private String auditUserId;

    //审核人姓名
    private String auditName;

    //理由
    private String reason;

    //0 拒绝 1 确认
    private Integer confirmStatus;

}

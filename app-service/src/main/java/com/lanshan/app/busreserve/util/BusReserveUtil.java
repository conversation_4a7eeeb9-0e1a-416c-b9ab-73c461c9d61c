package com.lanshan.app.busreserve.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import cn.hutool.core.util.StrUtil;
import com.lanshan.app.busreserve.po.BusReserveInfo;

import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

public class BusReserveUtil {

    /**
     * 计算发车时间
     * <AUTHOR> yang.
     * @since 2025/5/26 16:07
     * @param info 班次信息
     * @param startDay 日期 yyyy-MM-dd
     */
    public static Date calculateStartTime(BusReserveInfo info,String startDay) {
        DateTime now = DateUtil.parseDate(startDay);
        DateTime departure = null;
        String startTime = info.getStartTime();                       // "HH:mm"

        if (Integer.valueOf(1).equals(info.getRepeatStatus())) {
            // 重复班次：判断是否在 weekList 里
            if (info.getWeekList() != null && !info.getWeekList().isEmpty()) {
                Week week = DateUtil.dayOfWeekEnum(now);
                int code = week.getValue();
                if (info.getWeekList().contains(code)) {
                    // 拼接 "yyyy-MM-dd HH:mm:ss" 再解析
                    String dtStr = startDay + " " + startTime+":00";
                    departure = DateUtil.parse(dtStr, DatePattern.NORM_DATETIME_PATTERN);
                }
            }
        } else {
            // 非重复班次，直接拼接 "yyyy-MM-dd HH:mm:ss" 再解析
            if (info.getStartDay() != null && StrUtil.isNotEmpty(startTime)) {
//                DateTime sd = DateUtil.beginOfDay(DateUtil.date(info.getStartDay()));
//                if (DateUtil.isSameDay(sd, now)) {
//                    String dtStr = startDay + " " + startTime+":00";
//                    departure = DateUtil.parse(dtStr, DatePattern.NORM_DATETIME_PATTERN);
//                }
                String dtStr = DateUtil.format(info.getStartDay(), DatePattern.NORM_DATE_PATTERN) + " " + startTime+":00";
                departure = DateUtil.parse(dtStr, DatePattern.NORM_DATETIME_PATTERN);
            }
        }

        return departure;
    }

    /**
     * 将 "HH:mm" 时间字符串转为当天零点起的秒数
     * @param timeStr 格式如 "08:30"
     * @return 秒数，比如 "08:30" 返回 30600
     */
    public static int timeStrToSeconds(String timeStr) {
        // 构造当天的时间，比如 2025-05-22 08:30
        DateTime dateTime = DateUtil.parse(DateUtil.today() + " " + timeStr, "yyyy-MM-dd HH:mm");
        // 获取从 0 点开始的秒数
        long seconds = (dateTime.getTime() - DateUtil.beginOfDay(dateTime).getTime()) / 1000;
        return (int) seconds;
    }

}

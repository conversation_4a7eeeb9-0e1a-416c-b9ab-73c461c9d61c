package com.lanshan.app.busreserve.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 班车预约-企微用户预约记录
 * <AUTHOR> yang.
 * @since 2025-05-20
 */

@Data
@TableName(value = "bus_reserve_user_record", autoResultMap = true, schema = "standard_app")
public class BusReserveUserRecord implements Serializable {

	private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    //学工号
    private String userId;

    //姓名
    private String userName;

    //部门id
    private Long deptId;

    //部门名称
    private String deptName;

    //班次id
    private Long infoId;

    //班次名称
    private String infoName;

    //司机 id
    private String driverUserId;

    //司机姓名
    private String driverName;

    //司机手机号
    private String driverPhone;

    //是否代预约人 0否 1是
    private Integer replaceStatus;

    //代预约人id
    private String replaceUserId;

    //代预约人姓名
    private String replaceUserName;

    //状态 1待乘车 2正常乘车 3约而不坐 4坐而不约 5已取消
    private Integer reserveStatus;

    //发车时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date startTime;

    //核验时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date checkTime;

    //创建时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date createTime;

    //性别
    private String userGender;

    //中途上车点
    private String halfwayLocation;

    //发车日期
	@JsonFormat(pattern = "yyyy-MM-dd",locale="zh", timezone="GMT+8")
    private Date startDay;

    //乘车人数
    private Integer userCount;

    //改签次数
    private Integer changeCount;


    //代预约人手机号
    private String replacePhone;

    //预约id集合
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Long> recordIds;
}

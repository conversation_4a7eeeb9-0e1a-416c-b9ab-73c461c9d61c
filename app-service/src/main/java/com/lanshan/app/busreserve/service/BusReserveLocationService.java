package com.lanshan.app.busreserve.service;

import com.lanshan.app.busreserve.po.BusReserveLocation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 班车预约-上车点 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
public interface BusReserveLocationService extends IService<BusReserveLocation> {

    /**
     * 获取上车点列表
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     * @param location 上车点
     */
    List<BusReserveLocation> getBusReserveLocationList(String location);

    /**
     * 编辑上车点
     * <AUTHOR> yang.
     * @since 2025/5/21 10:44
     */
    void editBusReserveLocation(BusReserveLocation busReserveLocation, String userId);

}

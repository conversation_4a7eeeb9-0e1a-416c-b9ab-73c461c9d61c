package com.lanshan.app.busreserve.dao;

import com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 班车预约-外部用户预约记录 Mapper 接口
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
@Mapper
public interface BusReserveOutsideUserRecordMapper extends BaseMapper<BusReserveOutsideUserRecord> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BusReserveOutsideUserRecord> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BusReserveOutsideUserRecord> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BusReserveOutsideUserRecord> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<BusReserveOutsideUserRecord> entities);

    /**
     * 主键id查询
     * <AUTHOR> yang.
     * @since 2025/5/22 9:36
     */
    BusReserveOutsideUserRecordVO getBusReserveOutsideUserRecordById(@Param("id") String id);

    /**
     * 批量修改状态
     * <AUTHOR> yang.
     * @since 2025/5/22 10:29
     */
    int updateBusReserveOutsideUserRecord(@Param("ids")Collection<Long> ids,@Param("reserveStatus") Integer reserveStatus);

    /**
     * 班次id、发车日期、用户id集合查询
     * <AUTHOR> yang.
     * @since 2025/5/23 17:27
     */
    List<BusReserveOutsideUserRecordVO> getOutsideUserRecordByInfoAndDayAndUser(
            @Param("infoId") Long infoId, @Param("startDay") String startDay, @Param("userIds") List<String> userIds,@Param("reserveStatusNot") Integer reserveStatusNot
    );

    int getOutSideUserCountByInfoIdsAndReserveStatus(
            @Param("infoId") Long infoId,@Param("startDay") String startDay,@Param("reserveStatus") Integer reserveStatus
    );

    BusReserveOutsideUserRecord getBusReserveRecordByLocation(
            @Param("infoId") Long infoId,@Param("startLocation") String startLocation,@Param("endLocation") String endLocation,
            @Param("userId") String userId,@Param("startDay") String startDay,@Param("reserveStatus") Integer reserveStatus
    );

    List<BusReserveOutsideUserRecordVO> getWaitRecordByDate(@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("reserveStatus") Integer reserveStatus);

    List<BusReserveOutsideUserRecordVO> getTomorrowWaitRecord(@Param("tomorrow") String tomorrow,@Param("reserveStatus") Integer reserveStatus);
}

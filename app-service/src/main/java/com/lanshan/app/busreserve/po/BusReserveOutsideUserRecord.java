package com.lanshan.app.busreserve.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * 班车预约-外部用户预约记录
 * <AUTHOR> yang.
 * @since 2025-05-20
 */

@Data
@TableName(value = "bus_reserve_outside_user_record", autoResultMap = true, schema = "standard_app")
public class BusReserveOutsideUserRecord implements Serializable {

	private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    //姓名
    private String userName;

    //性别
    private String userGender;

    //部门id
    private Long deptId;

    //部门名称
    private String deptName;

    //职务
    private String position;

    //审核人id
    private String auditUserId;

    //审核人姓名
    private String auditName;

    //理由
    private String reason;

    //班次id
    private Long infoId;

    //班次名称
    private String infoName;

    //司机 id
    private String driverUserId;

    //司机姓名
    private String driverName;

    //司机手机号
    private String driverPhone;

    //预约状态 1待乘车 2正常乘车 3约而不坐 4坐而不约 5已取消
    private Integer reserveStatus;

    //发车时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date startTime;

    //核验时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date checkTime;

    //创建时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date createTime;

    //中途上车点
    private String halfwayLocation;

    //发车日期
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date startDay;

    //手机号
    private String phone;

    //审核人手机号
    private String auditPhone;

}

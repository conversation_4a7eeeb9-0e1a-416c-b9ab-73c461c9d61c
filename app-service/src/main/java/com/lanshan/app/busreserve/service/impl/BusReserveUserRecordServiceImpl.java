package com.lanshan.app.busreserve.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.*;
import com.lanshan.app.busreserve.enums.BusReserveStatus;
import com.lanshan.app.busreserve.po.BusReserveAppointment;
import com.lanshan.app.busreserve.po.BusReserveInfo;
import com.lanshan.app.busreserve.po.BusReserveUserRecord;
import com.lanshan.app.busreserve.dao.BusReserveUserRecordMapper;
import com.lanshan.app.busreserve.service.BusReserveAppointmentService;
import com.lanshan.app.busreserve.service.BusReserveInfoService;
import com.lanshan.app.busreserve.service.BusReserveUserRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.busreserve.util.BusReserveUtil;
import com.lanshan.app.busreserve.vo.*;
import com.lanshan.app.common.utils.FeignResultUtil;
import com.lanshan.app.common.utils.IdGenerator;
import com.lanshan.base.api.feign.addressbook.CpUserFeign;
import com.lanshan.base.api.qo.CollResult;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.vo.user.UserInfoPartVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 班车预约-企微用户预约记录 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */

@Slf4j
@Service
public class BusReserveUserRecordServiceImpl extends ServiceImpl<BusReserveUserRecordMapper, BusReserveUserRecord> implements BusReserveUserRecordService {

    @Resource
    private BusReserveUserRecordMapper busReserveUserRecordMapper;
    @Resource
    private CpUserFeign cpUserFeign;
    @Resource
    private BusReserveInfoService busReserveInfoService;
    @Resource
    private BusReserveAppointmentService busReserveAppointmentService;

    //是代预约
    public static final Integer REPLACE_STATUS = 1;

    @Override
    public List<BusReserveUserRecord> getBusReserveUserRecordByDay(String startDay) {
        LambdaQueryWrapper<BusReserveUserRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(BusReserveUserRecord::getReserveStatus,BusReserveStatus.CANCEL.value);
        wrapper.eq(BusReserveUserRecord::getStartDay,startDay);
        wrapper.orderByDesc(BusReserveUserRecord::getCreateTime,BusReserveUserRecord::getId);

        return this.list(wrapper);
    }

    @Override
    public Page<BusReserveUserRecord> getBusReserveUserRecordPageList(BusReserveUserRecordPageDTO dto) {
        Page<BusReserveUserRecord> pg = new Page<>(dto.getPage(), dto.getSize());
        LambdaQueryWrapper<BusReserveUserRecord> wrapper = getUserRecordQueryWrapper(dto);
        pg = this.page(pg, wrapper);
        return pg;
    }

    @Override
    public List<BusReserveUserRecordExcelVO> exportBusReserveUserRecord(BusReserveUserRecordPageDTO dto) {
        LambdaQueryWrapper<BusReserveUserRecord> wrapper = getUserRecordQueryWrapper(dto);
        List<BusReserveUserRecord> records = this.list(wrapper);
        List<BusReserveUserRecordExcelVO> list = BeanUtil.copyToList(records, BusReserveUserRecordExcelVO.class);
        return list;
    }

    private LambdaQueryWrapper<BusReserveUserRecord> getUserRecordQueryWrapper(BusReserveUserRecordPageDTO dto) {
        LambdaQueryWrapper<BusReserveUserRecord> wrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotEmpty(dto.getUserName())){
            wrapper.like(BusReserveUserRecord::getUserName, dto.getUserName());
        }
        if (StrUtil.isNotEmpty(dto.getDriverName())){
            wrapper.like(BusReserveUserRecord::getDriverName, dto.getDriverName());
        }
        if (StrUtil.isNotEmpty(dto.getInfoName())){
            wrapper.like(BusReserveUserRecord::getInfoName, dto.getInfoName());
        }
        if (StrUtil.isNotEmpty(dto.getStartTime())){
            wrapper.ge(BusReserveUserRecord::getStartTime, dto.getStartTime());
        }
        if (StrUtil.isNotEmpty(dto.getEndTime())){
            wrapper.le(BusReserveUserRecord::getStartTime, dto.getEndTime());
        }

        if (dto.getReserveStatus() != null) {
            wrapper.eq(BusReserveUserRecord::getReserveStatus, dto.getReserveStatus());
        }else {
            //后管看的是乘车记录
            List<Integer> reserveStatusList = Arrays.asList(
                    BusReserveStatus.NORMAL.value, BusReserveStatus.RESERVE_NOT_SIT.value,BusReserveStatus.SIT_NOT_RESERVE.value
            );
            wrapper.in(BusReserveUserRecord::getReserveStatus,reserveStatusList);
        }
        if (StrUtil.isNotEmpty(dto.getUserId())){
            wrapper.like(BusReserveUserRecord::getUserId, dto.getUserId());
        }
        //创建时间排序
        wrapper.orderByDesc(BusReserveUserRecord::getCreateTime,BusReserveUserRecord::getId);
        return wrapper;
    }

    @Override
    public Page<BusReserveUserRecord> getMineBusReserveUserRecord(BusReserveUserRecordPageDTO dto) {
        Page<BusReserveUserRecord> pg = new Page<>(dto.getPage(), dto.getSize());
        LambdaQueryWrapper<BusReserveUserRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusReserveUserRecord::getUserId, dto.getUserId());
        //发车时间排序
        wrapper.orderByDesc(BusReserveUserRecord::getStartTime,BusReserveUserRecord::getId);
        pg = this.page(pg, wrapper);
        return pg;
    }

    @Override
    public BusReserveUserRecordDetailVO getMineBusReserveUserRecordDetail(Long id) {
        BusReserveUserRecordDetailVO detailVO = new BusReserveUserRecordDetailVO();
        BusReserveUserRecordVO record = busReserveUserRecordMapper.getBusReserveUserRecordById(id);
        if (record != null){
            detailVO.setRecord(record);
            Integer replaceStatus = record.getReplaceStatus();
            //是代预约人，查询所有被预约的记录
            if (REPLACE_STATUS.equals(replaceStatus)){
//                //班次，发车时间，代预约人 都满足的
//                String replaceUserId = record.getUserId();
//                Long infoId = record.getInfoId();
//                String startTimeStr = DateUtil.formatDateTime(record.getStartTime());
//                List<BusReserveUserRecordVO> records = busReserveUserRecordMapper.getBusReserveUserRecordByInfoIdAndStartTime(infoId,startTimeStr,replaceUserId);
                List<BusReserveUserRecordVO> records = busReserveUserRecordMapper.getByIds(record.getRecordIds());
                detailVO.setUserRecords(records);
            }else {
                detailVO.setUserRecords(Arrays.asList(record));
            }
        }
        return detailVO;
    }

    @Override
    public boolean updateBusReserveUserRecord(Collection<Long> ids, Integer reserveStatus) {
        if(CollUtil.isEmpty(ids) || reserveStatus == null){
            return false;
        }
        return busReserveUserRecordMapper.updateBusReserveUserRecord(ids,reserveStatus) > 0;
    }

    @Override
    public List<BusReserveUserRecord> getUserRecordByInfoIdsAndReserveStatus(Collection<Long> ids, String startDay,Integer reserveStatus) {
        LambdaQueryWrapper<BusReserveUserRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BusReserveUserRecord::getInfoId, ids);
        if (StrUtil.isNotEmpty(startDay)){
            wrapper.eq(BusReserveUserRecord::getStartDay, startDay);
        }
        if (reserveStatus != null){
            wrapper.eq(BusReserveUserRecord::getReserveStatus, reserveStatus);
        }
        return this.list(wrapper);
    }

    @Override
    public List<BusReserveUserRecord> getUserRecordByInfoAndDay(Long infoId, String startDay) {
        LambdaQueryWrapper<BusReserveUserRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(BusReserveUserRecord::getInfoId, infoId)
                .eq(BusReserveUserRecord::getStartDay, startDay)
                .ne(BusReserveUserRecord::getReserveStatus,BusReserveStatus.CANCEL.value);

        return this.list(wrapper);
    }

    @Override
    public BusReserveUserVO getBusReserveUserInfo(String userId) {
        BusReserveUserVO user = new BusReserveUserVO();

        List<UserInfoPartVO> userList = FeignResultUtil.success(cpUserFeign.getUserByIds(Arrays.asList(userId)));
        UserInfoPartVO userInfo = userList.get(0);
        BeanUtil.copyProperties(userInfo,user);
        //是否司机
        List<BusReserveInfo> list = busReserveInfoService.list(new LambdaQueryWrapper<BusReserveInfo>().eq(BusReserveInfo::getDriverUserId, userId));
        boolean isDriver = CollUtil.isNotEmpty(list);
        //是否是代预约人
        BusReserveAppointment one = busReserveAppointmentService.getOne(new LambdaQueryWrapper<BusReserveAppointment>().eq(BusReserveAppointment::getUserId, userId));
        boolean isProxy = one != null;
        user.setIsDriver(isDriver);
        user.setIsProxy(isProxy);
        return user;
    }

    @Override
    public Boolean checkBusReserveUserAuth(BusReserveUserAuthDTO dto,BusReserveInfoVO info) {
        Long infoId = dto.getInfoId();
        String startDay = dto.getStartDay();
        List<String> userIds = dto.getUserIds();
        String startLocation = info.getStartLocation();
        //日期、班次、未取消的记录
        List<BusReserveUserRecordVO> records = busReserveUserRecordMapper.getUserRecordByInfoAndDayAndUser(null, startDay, userIds,BusReserveStatus.CANCEL.value);
        boolean auth = true;
        //有起点相同的记录，终止循环
        for (BusReserveUserRecordVO record : records) {
            String startLocationRe = record.getStartLocation();
            if (startLocation.equals(startLocationRe) ){
                auth = false;
                break;
            }
        }

        return auth;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void busReserve(BusReserveDTO dto, BusReserveInfoVO info) {
        List<String> userIds = dto.getUsers().stream().map(UserInfoPartVO::getUserId).collect(Collectors.toList());
        List<UserInfoPartVO> users = FeignResultUtil.success(cpUserFeign.getUserByIds(userIds));
        if (CollUtil.isEmpty(users)){
            return;
        }
        String replaceUserId = dto.getReplaceUserId();
        BusReserveAppointment appointment = null;
        if (StrUtil.isNotEmpty(replaceUserId)){
            appointment = busReserveAppointmentService.getOne(new LambdaQueryWrapper<BusReserveAppointment>().eq(BusReserveAppointment::getUserId, replaceUserId));
        }
        int size = users.size();
        List<BusReserveUserRecord> records = new ArrayList<>(size);
        BusReserveUserRecord record = null;
        for (UserInfoPartVO user : users) {
            record = new BusReserveUserRecord();
            record.setId(IdGenerator.generateId());
            record.setUserId(user.getUserId());
            record.setUserName(user.getName());
            record.setDeptId(user.getDeptId());
            record.setDeptName(user.getDeptName());
            record.setInfoId(info.getId());
            record.setInfoName(info.getName());
            record.setDriverUserId(info.getDriverUserIdToday());
            record.setDriverName(info.getDriverNameToday());
            record.setDriverPhone(info.getDriverPhoneToday());
            if (user.getUserId().equals(dto.getReplaceUserId())){
                record.setReplaceStatus(1);
            }else {
                record.setReplaceStatus(0);
            }
            if (appointment != null){
                record.setReplaceUserId(appointment.getUserId());
                record.setReplaceUserName(appointment.getUserName());
                record.setReplacePhone(appointment.getPhone());
            }
            record.setReserveStatus(BusReserveStatus.WAIT.value);
            //计算发车时间
            Date startTime = BusReserveUtil.calculateStartTime(info, dto.getStartDay());
            record.setStartTime(startTime);
            record.setCreateTime(new Date());
            record.setUserGender(user.getGender());
            record.setHalfwayLocation(dto.getHalfwayLocation());
            record.setStartDay(DateUtil.parseDate(dto.getStartDay()));
            record.setUserCount(size);
            record.setChangeCount(0);
            records.add(record);
        }
        List<Long> ids = records.stream().map(BusReserveUserRecord::getId).collect(Collectors.toList());
        records.forEach(r -> r.setRecordIds(ids));
        busReserveUserRecordMapper.insertBatch(records);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeReserve(BusReserveChangeDTO dto, BusReserveInfoVO info,BusReserveUserRecordDetailVO recordOld) {
        List<BusReserveUserRecordVO> records = recordOld.getUserRecords();
        List<BusReserveUserRecord> recordsNew = new ArrayList<>(records.size());
        BusReserveUserRecord record = null;
        for (BusReserveUserRecordVO re : records) {
            record = new BusReserveUserRecord();
            record.setId(re.getId());
            record.setInfoId(info.getId());
            record.setInfoName(info.getName());
            record.setDriverUserId(info.getDriverUserIdToday());
            record.setDriverName(info.getDriverNameToday());
            record.setDriverPhone(info.getDriverPhoneToday());
            //计算发车时间
            Date startTime = BusReserveUtil.calculateStartTime(info, dto.getStartDay());
            record.setStartTime(startTime);
            Integer changeCount = re.getChangeCount() == null ? 0 : re.getChangeCount();
            record.setChangeCount(changeCount);
            recordsNew.add(record);
        }
        busReserveUserRecordMapper.changeReserveBatch(recordsNew);
    }

    @Override
    public int getUserCountByInfoIdsAndReserveStatus(Long infoId, String startDay, Integer reserveStatus) {
        return busReserveUserRecordMapper.getUserCountByInfoIdsAndReserveStatus(infoId, startDay, reserveStatus);
    }

    @Override
    public void insertSitNotReserveUserRecord(BusReserveInfoCodeDTO dto) {
        Date now = new Date();
        BusReserveInfo info = busReserveInfoService.getById(dto.getInfoId());
        BusReserveUserRecord record = new BusReserveUserRecord();
        record.setUserId(dto.getUserId());
        record.setUserName(dto.getName());
        record.setDeptId(dto.getDeptId());
        record.setDeptName(dto.getDeptName());
        record.setInfoId(info.getId());
        record.setInfoName(info.getName());
        record.setDriverUserId(info.getDriverUserIdToday());
        record.setDriverName(info.getDriverNameToday());
        record.setDriverPhone(info.getDriverPhoneToday());
        record.setReplaceStatus(0);
        record.setReserveStatus(BusReserveStatus.SIT_NOT_RESERVE.value);
        //计算发车时间
        Date startTime = BusReserveUtil.calculateStartTime(info, dto.getStartDay());
        record.setStartTime(startTime);
        record.setCreateTime(now);
        record.setCheckTime(now);
        record.setUserGender(dto.getGender());
        record.setStartDay(DateUtil.parseDate(dto.getStartDay()));
        record.setUserCount(1);
        record.setChangeCount(0);
        this.save(record);
    }

    @Override
    public BusReserveUserRecord getBusReserveRecordByLocation(Long infoId, String startLocation, String endLocation, String userId, String startDay) {
        return busReserveUserRecordMapper.getBusReserveRecordByLocation(infoId, startLocation, endLocation, userId, startDay, BusReserveStatus.WAIT.value);
    }

    @Override
    public List<BusReserveUserRecordVO> getWaitRecordByDate(String startTime, String endTime,Integer reserveStatus) {

        return busReserveUserRecordMapper.getWaitRecordByDate(startTime, endTime, reserveStatus);
    }

    @Override
    public List<BusReserveUserRecordVO> getTomorrowWaitRecord(String tomorrow, Integer reserveStatus) {
        return busReserveUserRecordMapper.getTomorrowWaitRecord(tomorrow, reserveStatus);
    }

}

package com.lanshan.app.busreserve.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.A;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusReserveInfoUserDTO implements Serializable {
    private static final long serialVersionUID = -2243487453744190404L;

    private Long infoId;

    private String infoName;

    //0 拒绝 1 确认
    private Integer confirmStatus;
}

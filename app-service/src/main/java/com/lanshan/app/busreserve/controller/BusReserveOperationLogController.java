package com.lanshan.app.busreserve.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveOperationLogPageDTO;
import com.lanshan.app.busreserve.po.BusReserveOperationLog;
import com.lanshan.app.busreserve.service.BusReserveOperationLogService;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 班车操作日志
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */


@Slf4j
@RestController
@RequestMapping("/busReserve/operationLog")
public class BusReserveOperationLogController {

    @Resource
    private BusReserveOperationLogService busReserveOperationLogService;

    /**
     * 获取班车操作日志分页列表
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    @PostMapping(value = "/getBusReserveOperationLogPageList" ,produces = "application/json;charset=UTF-8")
    public Result<Page<BusReserveOperationLog>> getBusReserveOperationLogPageList(@RequestBody BusReserveOperationLogPageDTO dto){
        Page<BusReserveOperationLog> pg = busReserveOperationLogService.getBusReserveOperationLogPageList(dto);
        return Result.build(pg);
    }

}

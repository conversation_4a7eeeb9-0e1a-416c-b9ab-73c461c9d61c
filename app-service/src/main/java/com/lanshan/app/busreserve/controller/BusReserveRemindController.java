package com.lanshan.app.busreserve.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lanshan.app.busreserve.dto.BusReserveRemindCancelDTO;
import com.lanshan.app.busreserve.dto.BusReserveRemindSaveDTO;
import com.lanshan.app.busreserve.po.BusReserveRemind;
import com.lanshan.app.busreserve.service.BusReserveRemindService;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 预约提醒
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/busReserve/remind")
public class BusReserveRemindController {

    @Resource
    private BusReserveRemindService busReserveRemindService;

    /**
     * 开启预约提醒
     * <AUTHOR> yang.
     * @since 2025/5/23 17:20
     */
    @PostMapping(value = "/insertBusReserveRemind",produces = "application/json;charset=UTF-8")
    public Result<Object> insertBusReserveRemind(@RequestBody BusReserveRemindSaveDTO dto){
        BusReserveRemind one = busReserveRemindService.getOne(new LambdaQueryWrapper<BusReserveRemind>()
                .eq(BusReserveRemind::getInfoId, dto.getInfoId())
                .eq(BusReserveRemind::getUserId, dto.getUserId())
                .eq(BusReserveRemind::getStartDay, dto.getStartDayStr())
        );
        if(one!= null){
            return Result.build().error("已开启预约提醒，无需重复开启！");
        }
        BusReserveRemind remind = new BusReserveRemind();
        BeanUtil.copyProperties(dto,remind);
        remind.setCreateTime(new Date());
        busReserveRemindService.save(remind);
        return Result.build();
    }

    /**
     * 取消预约提醒
     * <AUTHOR> yang.
     * @since 2025/5/23 17:20
     */
    @PostMapping(value = "/cancelBusReserveRemind",produces = "application/json;charset=UTF-8")
    public Result<Object> cancelBusReserveRemind(@RequestBody BusReserveRemindCancelDTO dto){
        BusReserveRemind one = busReserveRemindService.getOne(new LambdaQueryWrapper<BusReserveRemind>()
                .eq(BusReserveRemind::getInfoId, dto.getInfoId())
                .eq(BusReserveRemind::getUserId, dto.getUserId())
                .eq(BusReserveRemind::getStartDay, dto.getStartDayStr())
        );
        if(one!= null){
            busReserveRemindService.removeById(one.getId());
        }
        return Result.build();
    }

}

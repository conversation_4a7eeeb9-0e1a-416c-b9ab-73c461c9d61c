package com.lanshan.app.busreserve.vo;

import com.lanshan.base.api.vo.user.UserInfoPartVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class BusReserveUserVO extends UserInfoPartVO {

    private static final long serialVersionUID = -6937738014327426205L;

    //是否司机
    private Boolean isDriver;

    //是否代预约人
    private Boolean isProxy;

    private String phone;

}

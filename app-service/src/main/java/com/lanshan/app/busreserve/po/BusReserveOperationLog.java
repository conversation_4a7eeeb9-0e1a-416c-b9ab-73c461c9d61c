package com.lanshan.app.busreserve.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * 班车预约-操作日志
 * <AUTHOR> yang.
 * @since 2025-05-21
 */

@Data
@TableName(value = "bus_reserve_operation_log", autoResultMap = true, schema = "standard_app")
public class BusReserveOperationLog implements Serializable {

	private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    //操作类型（0其它 1新增 2修改 3删除）
    private Integer businessType;

    //操作人id
    private String userId;

    //操作人姓名
    private String userName;

    //操作前
    private String operationBefore;

    //操作后
    private String operationAfter;

    //操作时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date createTime;

    //班次id
    private Long infoId;

    private String infoName;

    public BusReserveOperationLog() {
    }

    public BusReserveOperationLog(Integer businessType, String userId, String userName, String operationBefore, String operationAfter, Date createTime, Long infoId, String infoName) {
        this.businessType = businessType;
        this.userId = userId;
        this.userName = userName;
        this.operationBefore = operationBefore;
        this.operationAfter = operationAfter;
        this.createTime = createTime;
        this.infoId = infoId;
        this.infoName = infoName;
    }
}

package com.lanshan.app.busreserve.vo;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lanshan.app.busreserve.enums.BusReserveStatus;
import com.lanshan.app.common.annotation.ExcelHeader;
import lombok.Data;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class BusReserveUserRecordExcelVO implements Serializable {

    private static final long serialVersionUID = 1159643826053789260L;

    @ExcelHeader(title = "预约编号")
    private Long id;

    @ExcelHeader(title = "学工号")
    private String userId;

    @ExcelHeader(title = "姓名")
    private String userName;

    @ExcelHeader(title = "部门")
    private String deptName;

    @ExcelHeader(title = "班次名称")
    private String infoName;

    @ExcelHeader(title = "司机姓名")
    private String driverName;

    @ExcelHeader(title = "司机手机号")
    private String driverPhone;

    //发车时间
    @ExcelHeader(required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date startTime;

    @ExcelHeader(title = "发车时间")
    private String startTimeStr;

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
        this.startTimeStr = DateUtil.format(startTime, DatePattern.NORM_DATETIME_PATTERN);
    }

    //申请时间
    @ExcelHeader(required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date createTime;

    @ExcelHeader(title = "创建时间")
    private String createTimeStr;

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
        this.createTimeStr = DateUtil.format(createTime, DatePattern.NORM_DATETIME_PATTERN);
    }

    //是否代预约人 0否 1是
    @ExcelHeader(required = false)
    private Integer replaceStatus;

    @ExcelHeader(title = "是否代预约人")
    private String replaceStatusStr;

    public void setReplaceStatus(Integer replaceStatus) {
        this.replaceStatus = replaceStatus;
        this.replaceStatusStr = replaceStatus == 1 ? "是" : "否";
    }

    @ExcelHeader(title = "代预约人")
    private String replaceUserName;

    //状态 1待乘车 2正常乘车 3约而不坐 4坐而不约 5已取消
    @ExcelHeader(required = false)
    private Integer reserveStatus;

    @ExcelHeader(title = "状态")
    private String reserveStatusStr;

    public void setReserveStatus(Integer reserveStatus) {
        this.reserveStatus = reserveStatus;
        this.reserveStatusStr = BusReserveStatus.getName(reserveStatus);
    }

    //核验时间
    @ExcelHeader(required = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date checkTime;

    @ExcelHeader(title = "核验时间")
    private String checkTimeStr;

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
        this.checkTimeStr = DateUtil.format(checkTime, DatePattern.NORM_DATETIME_PATTERN);
    }

    @ExcelHeader(title = "中途上车点")
    private String halfwayLocation;

}

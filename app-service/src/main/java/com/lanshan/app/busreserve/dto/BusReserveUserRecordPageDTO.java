package com.lanshan.app.busreserve.dto;

import com.lanshan.base.api.qo.PageQo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class BusReserveUserRecordPageDTO  extends PageQo {
    private static final long serialVersionUID = 321827004868025534L;

    //学工号
    private String userId;

    //姓名
    private String userName;

    //司机姓名
    private String driverName;

    //班次名称
    private String infoName;

    //状态 2正常乘车 3约而不坐 4坐而不约
    private Integer reserveStatus;

    //发车时间 yyyy-MM-dd HH:mm:ss
    private String startTime;

    private String endTime;
}

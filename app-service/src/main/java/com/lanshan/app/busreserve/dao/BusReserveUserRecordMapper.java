package com.lanshan.app.busreserve.dao;

import com.lanshan.app.busreserve.po.BusReserveUserRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lanshan.app.busreserve.vo.BusReserveUserRecordDetailVO;
import com.lanshan.app.busreserve.vo.BusReserveUserRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 班车预约-企微用户预约记录 Mapper 接口
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
@Mapper
public interface BusReserveUserRecordMapper extends BaseMapper<BusReserveUserRecord> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BusReserveUserRecord> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BusReserveUserRecord> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BusReserveUserRecord> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<BusReserveUserRecord> entities);

    /**
     * 主键id查询
     * <AUTHOR> yang.
     * @since 2025/5/22 9:36
     */
    BusReserveUserRecordVO getBusReserveUserRecordById(@Param("id") Long id);

    /**
     * 班次id和发车时间查询
     * <AUTHOR> yang.
     * @since 2025/5/22 9:36
     * @param infoId 班次id
     * @param startTimeStr 班车发车时间字符串(yyyy-MM-dd HH:mm:ss)
     */
    List<BusReserveUserRecordVO> getBusReserveUserRecordByInfoIdAndStartTime(
            @Param("infoId") Long infoId,@Param("startTimeStr") String startTimeStr,@Param("replaceUserId") String replaceUserId
    );

    /**
     * 批量修改状态
     * <AUTHOR> yang.
     * @since 2025/5/22 10:29
     */
    int updateBusReserveUserRecord(@Param("ids")Collection<Long> ids,@Param("reserveStatus") Integer reserveStatus);

    /**
     * 班次id、发车日期、用户id查询
     * <AUTHOR> yang.
     * @since 2025/5/23 17:27
     */
    List<BusReserveUserRecordVO> getUserRecordByInfoAndDayAndUser(
            @Param("infoId") Long infoId, @Param("startDay") String startDay, @Param("userIds") List<String> userIds,@Param("reserveStatusNot") Integer reserveStatusNot
    );

    int getUserCountByInfoIdsAndReserveStatus(
            @Param("infoId") Long infoId,@Param("startDay") String startDay,@Param("reserveStatus") Integer reserveStatus
    );

    int changeReserveBatch(@Param("entities") List<BusReserveUserRecord> entities);

    BusReserveUserRecord getBusReserveRecordByLocation(
            @Param("infoId") Long infoId,@Param("startLocation") String startLocation,@Param("endLocation") String endLocation,
            @Param("userId") String userId,@Param("startDay") String startDay,@Param("reserveStatus") Integer reserveStatus
    );

    List<BusReserveUserRecordVO> getWaitRecordByDate(@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("reserveStatus") Integer reserveStatus);

    List<BusReserveUserRecordVO> getTomorrowWaitRecord(@Param("tomorrow") String tomorrow,@Param("reserveStatus") Integer reserveStatus);

    List<BusReserveUserRecordVO> getByIds(@Param("ids") Collection<Long> recordIds);
}

package com.lanshan.app.busreserve.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * 班车预约-班车记录
 * <AUTHOR> yang.
 * @since 2025-05-20
 */

@Data
@TableName(value = "bus_reserve_departure_record", autoResultMap = true, schema = "standard_app")
public class BusReserveDepartureRecord implements Serializable {

	private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    //班次id
    private Long infoId;

    private String infoName;

    //司机 id
    private String driverUserId;

    //司机姓名
    private String driverName;

    //司机手机号
    private String driverPhone;

    //车牌号
    private String licensePlate;

    //今日车牌
    private String licensePlateToday;

    //今日司机id
    private String driverUserIdToday;

    //今日司机姓名
    private String driverNameToday;

    //今日司机手机号
    private String driverPhoneToday;

    //发车时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date startTime;

    //核载人数
    private Integer userCount;

    //正常乘车人数
    private Integer normalCount;

    //约而不坐人数
    private Integer reserveNotSitCount;

    //坐而不约人数
    private Integer sitNotReserveCount;



}

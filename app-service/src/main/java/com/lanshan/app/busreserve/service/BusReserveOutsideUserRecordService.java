package com.lanshan.app.busreserve.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveDTO;
import com.lanshan.app.busreserve.dto.BusReserveInfoCodeDTO;
import com.lanshan.app.busreserve.dto.BusReserveOutSideUserRecordPageDTO;
import com.lanshan.app.busreserve.dto.BusReserveUserAuthDTO;
import com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.busreserve.vo.BusReserveInfoVO;
import com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordExcelVO;
import com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordVO;
import com.lanshan.base.api.qo.CollResult;
import com.lanshan.base.api.utils.Result;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 班车预约-外部用户预约记录 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
public interface BusReserveOutsideUserRecordService extends IService<BusReserveOutsideUserRecord> {

    /**
     * 发车日期查询外部用户乘坐记录
     * <AUTHOR> yang.
     * @since 2025/5/21 17:39
     */
    List<BusReserveOutsideUserRecord> getOutsideUserRecordByDay(String startDay);

    /**
     * 外部用户乘坐记录分页列表
     * <AUTHOR> yang.
     * @since 2025/5/22 11:26
     */
    Page<BusReserveOutsideUserRecord> getOutsideUserRecordPageList(BusReserveOutSideUserRecordPageDTO dto);


    /**
     * 外部用户乘坐记录
     * <AUTHOR> yang.
     * @since 2025/5/22 11:26
     */
    List<BusReserveOutsideUserRecordExcelVO> exportOutsideUserRecord(BusReserveOutSideUserRecordPageDTO dto);

    /**
     * 外部用户预约记录
     * <AUTHOR> yang.
     * @since 2025/5/22 11:26
     */
    Page<BusReserveOutsideUserRecord> getOutsideUserRecord(BusReserveOutSideUserRecordPageDTO dto);


    /**
     * 外部用户预约详情
     * <AUTHOR> yang.
     * @since 2025/5/21 17:51
     * @param id 预约记录id
     */
    BusReserveOutsideUserRecordVO getMineBusReserveOutsideUserRecordDetail(String id);

    /**
     * 批量修改状态
     * <AUTHOR> yang.
     * @since 2025/5/22 10:04
     */
    boolean updateBusReserveOutsideUserRecord(Collection<Long> ids, Integer reserveStatus);

    /**
     * 根据 班次id集合、发车日期、预约状态查询预约记录
     * <AUTHOR> yang.
     * @since 2025/5/22 16:43
     * @param ids 班次id集合
     * @param reserveStatus 预约状态
     */
    List<BusReserveOutsideUserRecord> getOutSideUserRecordByInfoIdsAndReserveStatus(Collection<Long> ids, String startDay,Integer reserveStatus);

    /**
     * 根据班次id，发车日期查询未取消的记录
     * <AUTHOR> yang.
     * @since 2025/5/28 10:27
     */
    List<BusReserveOutsideUserRecord> getOutsideUserRecordByInfoAndDay(Long infoId, String startDay);

    Boolean checkBusReserveOutsideUserAuth(BusReserveUserAuthDTO dto,BusReserveInfoVO info);

    /**
     * 新增预约记录
     * <AUTHOR> yang.
     * @since 2025/5/26 15:55
     */
    void busReserve(BusReserveDTO dto, BusReserveInfoVO info);

    /**
     * 根据 班次id、发车日期、预约状态查询预约记录
     * <AUTHOR> yang.
     * @since 2025/5/22 16:43
     */
    int getOutSideUserCountByInfoIdsAndReserveStatus(Long infoId, String startDay, Integer reserveStatus);

    /**
     * 增加坐而不约记录
     * <AUTHOR> yang.
     * @since 2025/5/28 16:28
     */
    void insertSitNotReserveOutsideUserRecord(BusReserveInfoCodeDTO dto);


    BusReserveOutsideUserRecord getBusReserveRecordByLocation(Long infoId, String startLocation, String endLocation, String userId, String startDay);

    List<BusReserveOutsideUserRecordVO> getWaitRecordByDate(String startTime, String endTime,Integer reserveStatus);

    List<BusReserveOutsideUserRecordVO> getTomorrowWaitRecord(String tomorrow, Integer reserveStatus);
}

package com.lanshan.app.busreserve.vo;

import com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class BusReserveOutsideUserRecordVO extends BusReserveOutsideUserRecord {
    private static final long serialVersionUID = 3969826499090392766L;
    //始发站 武昌校区 | 嘉鱼校区
    private String startLocation;

    //终点站 武昌校区 | 嘉鱼校区
    private String endLocation;
}

package com.lanshan.app.busreserve.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lanshan.app.busreserve.po.BusReserveLocation;
import com.lanshan.app.busreserve.dao.BusReserveLocationMapper;
import com.lanshan.app.busreserve.service.BusReserveLocationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.common.utils.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 班车预约-上车点 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
@Service
public class BusReserveLocationServiceImpl extends ServiceImpl<BusReserveLocationMapper, BusReserveLocation> implements BusReserveLocationService {

    @Override
    public List<BusReserveLocation> getBusReserveLocationList(String location) {
        LambdaQueryWrapper<BusReserveLocation> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(BusReserveLocation::getCreateTime, BusReserveLocation::getId);
        if (StrUtil.isNotEmpty(location)){
            wrapper.like(BusReserveLocation::getLocation, location);
        }
        List<BusReserveLocation> list = this.list(wrapper);
        return list;
    }

    @Override
    public void editBusReserveLocation(BusReserveLocation busReserveLocation, String userId) {
        if (busReserveLocation.getId() == null){
            busReserveLocation.setCreateUserId(userId);
            busReserveLocation.setCreateTime(new Date());
        }
        this.saveOrUpdate(busReserveLocation);
    }

}

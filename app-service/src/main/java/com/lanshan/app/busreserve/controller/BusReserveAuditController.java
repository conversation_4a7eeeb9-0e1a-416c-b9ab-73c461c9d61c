package com.lanshan.app.busreserve.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveAuditPageDTO;
import com.lanshan.app.busreserve.po.BusReserveAudit;
import com.lanshan.app.busreserve.service.BusReserveAuditService;
import com.lanshan.base.api.page.PageResult;
import com.lanshan.base.api.qo.CollResult;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * 审核人
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/busReserve/audit")
public class BusReserveAuditController {

    @Resource
    private BusReserveAuditService busReserveAuditService;

    /**
     * 获取审核人
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    @PostMapping(value = "/getBusReserveAuditList" ,produces = "application/json;charset=UTF-8")
    public Result<CollResult<BusReserveAudit>> getBusReserveAuditList(@RequestBody BusReserveAuditPageDTO dto){
        List<BusReserveAudit> records = busReserveAuditService.getBusReserveAuditList(dto);
        return Result.build(new CollResult<>(records));
    }

    /**
     * 批量新增审核人
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    @PostMapping(value = "/saveBatchBusReserveAudit" ,produces = "application/json;charset=UTF-8")
    public Result<Object> saveBatchBusReserveAudit(@RequestBody List<BusReserveAudit> list){
        Date now = new Date();
        list.forEach(b -> b.setCreateTime(now));
        busReserveAuditService.saveBatch(list);
        return Result.build();
    }

    /**
     * 批量删除审核人
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    @PostMapping(value = "/removeBusReserveAuditByIds" ,produces = "application/json;charset=UTF-8")
    public Result<Object> removeBusReserveAuditByIds(@RequestBody Collection<String> ids){
        busReserveAuditService.removeByIds(ids);
        return Result.build();
    }
}

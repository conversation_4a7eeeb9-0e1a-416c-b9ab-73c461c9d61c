package com.lanshan.app.busreserve.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveAuditPageDTO;
import com.lanshan.app.busreserve.po.BusReserveDispatcher;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.busreserve.vo.BusReserveInfoVO;

/**
 * <p>
 * 班车预约-调度员 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
public interface BusReserveDispatcherService extends IService<BusReserveDispatcher> {

    /**
     * 获取调度员分页列表
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    Page<BusReserveDispatcher> getBusReserveDispatcherPageList(BusReserveAuditPageDTO dto);
}

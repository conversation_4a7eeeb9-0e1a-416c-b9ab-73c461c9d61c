package com.lanshan.app.busreserve.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * 班车预约-上车点
 * <AUTHOR> yang.
 * @since 2025-05-20
 */

@Data
@TableName(value = "bus_reserve_location", autoResultMap = true, schema = "standard_app")
public class BusReserveLocation implements Serializable {

	private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    //上车地点
    private String location;

    //申请时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date createTime;

    //创建人
    private String createUserId;



}

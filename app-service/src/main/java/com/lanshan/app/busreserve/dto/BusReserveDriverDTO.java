package com.lanshan.app.busreserve.dto;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 * <AUTHOR> yang.
 * @since  2022/12/20 10:49
 * @version  1.0
 */

@Data
public class BusReserveDriverDTO implements Serializable {
    private static final long serialVersionUID = -1923180869529080021L;

    private String startDay;

    private String userId;

    //1 2 3 4 5 6 7   1代表周日  2代表周一
    private Integer week;

    //当前时间 距离当天0点的秒数
    private Integer seconds;

    //根据发车日期计算星期，计算现在距离当天0点的秒数
    public void setStartDay(String startDay) {
        this.startDay = startDay;
        if (StrUtil.isNotEmpty(startDay)){
            DateTime time = DateUtil.parseDate(startDay);
            this.week = DateUtil.dayOfWeekEnum(time).getValue();
            Date now = new Date();
            if (DateUtil.isSameDay(time, now)) {
                this.seconds = (int) ((now.getTime() - DateUtil.beginOfDay(now).getTime()) / 1000);
            }
        }
    }
}

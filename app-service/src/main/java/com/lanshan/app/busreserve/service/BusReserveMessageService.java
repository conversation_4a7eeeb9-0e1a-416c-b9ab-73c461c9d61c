package com.lanshan.app.busreserve.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lanshan.app.aliyun.util.SmsUtil;
import com.lanshan.app.busreserve.enums.BusReserveStatus;
import com.lanshan.app.busreserve.po.*;
import com.lanshan.app.busreserve.vo.BusReserveInfoVO;
import com.lanshan.app.busreserve.vo.BusReserveSmsVO;
import com.lanshan.app.checkin.util.CheckinUtil;
import com.lanshan.app.common.service.ISysConfigService;
import com.lanshan.app.common.utils.FeignResultUtil;
import com.lanshan.app.common.utils.JsonUtil;
import com.lanshan.app.common.utils.ThreadPoolUtil;
import com.lanshan.base.api.dto.message.TextcardMsgBody;
import com.lanshan.base.api.feign.message.MessageFeign;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessageSendResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.lanshan.app.busreserve.constant.BusReserveConstant.*;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@Service
public class BusReserveMessageService {

    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private MessageFeign messageFeign;
    @Resource
    private SmsUtil smsUtil;
    @Resource
    private BusReserveUserRecordService userRecordService;
    @Resource
    private BusReserveOutsideUserRecordService outsideUserRecordService;
    @Resource
    private BusReserveRemindService remindService;
    @Resource
    private BusReserveInfoService busReserveInfoService;
    @Resource
    private BusReserveDispatcherService busReserveDispatcherService;


    private static String downLineMsg = "您预约的{}的班车 {} 已下线, 暂不发车, 如有需要，请预约其他班次。";

    private static String concernMsg = "您关注的{}的班车 {} 现有余座, 如有需要，请尝试预约。";

    private static String dispatcherMsg = "{}的{}预约率已超过{}%，请注意查看。";

    /**
     * 班车下线 发送 企微消息 | 短信消息
     * <AUTHOR> yang.
     * @since 2025/5/22 16:39
     */
    public void sendShowStatusMsg(List<Long> ids,List<BusReserveInfo> records){
        //在班次下 未登车的记录
        //企微用户
        List<BusReserveUserRecord> list = userRecordService.getUserRecordByInfoIdsAndReserveStatus(ids,null, BusReserveStatus.WAIT.value);
        //外部用户
        List<BusReserveOutsideUserRecord> outSideList = outsideUserRecordService.getOutSideUserRecordByInfoIdsAndReserveStatus(ids,null, BusReserveStatus.WAIT.value);
        final String title = "班车取消提醒";
        if (CollUtil.isNotEmpty(list)){
            //企微消息
            final String corpId = sysConfigService.selectConfigByKey(CORP_ID_KEY);
            final String agentId = sysConfigService.selectConfigByKey(AGENT_ID_KEY);
            final String reserveUrl = sysConfigService.selectConfigByKey(RESERVE_URI);
            for (BusReserveUserRecord userRecord : list) {
                Long id = userRecord.getId();
                String infoName = userRecord.getInfoName();
                String day = DateUtil.format(userRecord.getStartDay(), DatePattern.NORM_DATE_PATTERN);
                String content = StrUtil.format(downLineMsg, day, infoName);
                final String uri = StrUtil.format(reserveUrl, agentId, corpId,id);
                ThreadPoolUtil.execute(() -> {
                    this.sendTextCard(uri, title, content, userRecord.getUserId());
                });
            }
        }
        if (CollUtil.isNotEmpty(outSideList)){
            //短信消息
            final String smsOffline = sysConfigService.selectConfigByKey(SMS_OFFLINE);
            for (BusReserveOutsideUserRecord userRecord : outSideList) {
                String infoName = userRecord.getInfoName();
                String phone = userRecord.getPhone();
                String day = DateUtil.format(userRecord.getStartDay(), DatePattern.NORM_DATE_PATTERN);
                ThreadPoolUtil.execute(() -> {
                    BusReserveSmsVO smsVO = new BusReserveSmsVO(day,infoName,null);
                    smsUtil.sendSmsByTemplate(smsOffline, phone,smsVO);
                });
            }
        }
    }

    /**
     * 调度员 发送 企微消息
     * <AUTHOR> yang.
     * @since 2025/5/22 16:39
     */
    public void sendDispatcherMessage(BusReserveInfoVO info,String startDay) {
        //预约率
        Integer seatCurr = info.getSeatCount();
        Integer userCurr = info.getUserCount();
        BigDecimal attendanceRate = CheckinUtil.getAttendanceRate(userCurr.longValue(), seatCurr.longValue());
        int rate = attendanceRate.intValue();
        LambdaQueryWrapper<BusReserveDispatcher> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(BusReserveDispatcher::getReserveRate, rate);
        List<BusReserveDispatcher> list = busReserveDispatcherService.list(wrapper);
        if (CollUtil.isEmpty(list)){
            return;
        }
        final String corpId = sysConfigService.selectConfigByKey(CORP_ID_KEY);
        final String agentId = sysConfigService.selectConfigByKey(AGENT_ID_KEY);
        final String detailUrl = sysConfigService.selectConfigByKey(DETAIL_URI);
        final String uri = StrUtil.format(detailUrl, agentId, corpId);
        final String title = "余座预警提醒";
        for (BusReserveDispatcher dispatcher : list) {
            String userId = dispatcher.getUserId();
            int rat = dispatcher.getReserveRate().intValue();
            //发送消息
            String content = StrUtil.format(dispatcherMsg, startDay, info.getName(),rat);
            ThreadPoolUtil.execute(() -> {
                this.sendTextCard(uri, title, content, userId);
            });
        }

    }

    /**
     * 发送余座提醒
     * <AUTHOR> yang.
     * @since 2025/5/29 15:19
     */
    public void sendConcernMsg(Long infoId, Date startDay){
        BusReserveInfo info = busReserveInfoService.getById(infoId);
        if (info == null){
            return;
        }
        String startLocation = info.getStartLocation();
        String endLocation = info.getEndLocation();
        Map<String ,Object> paramMap = new HashMap<>(3);
        paramMap.put("startLocation",startLocation);
        paramMap.put("endLocation",endLocation);
        String infoName = info.getName();
        String startDayStr = DateUtil.formatDate(startDay);
        List<BusReserveRemind> remindList = remindService.getRemindByInfoIdAndDay(infoId, startDayStr);
        if (CollUtil.isEmpty(remindList)){
            return;
        }
        //用户类型分组  1企微用户 2外部用户
        final String title = "余座提醒";
        final String corpId = sysConfigService.selectConfigByKey(CORP_ID_KEY);
        final String agentId = sysConfigService.selectConfigByKey(AGENT_ID_KEY);
        final String uri = sysConfigService.selectConfigByKey(BUS_SEL_URI);
        final String detailUrl = StrUtil.format(uri, agentId,corpId, URLUtil.encode(JsonUtil.toJson(paramMap)));
        String content = StrUtil.format(concernMsg, startDayStr, infoName);
        Map<Integer, List<BusReserveRemind>> typeMap = remindList.stream().collect(Collectors.groupingBy(BusReserveRemind::getUserType));
        for (Map.Entry<Integer, List<BusReserveRemind>> entry : typeMap.entrySet()) {
            Integer userType = entry.getKey();
            List<BusReserveRemind> reminds = entry.getValue();
            List<String> userIds = reminds.stream().map(BusReserveRemind::getUserId).collect(Collectors.toList());
            if (userType == 1) {
                //企微消息
                String toUser = CollUtil.join(userIds, "|");
                ThreadPoolUtil.execute(() -> {
                    this.sendTextCard(detailUrl, title, content, toUser);
                });
            }else {
                //短信消息
                String toUser = CollUtil.join(userIds, ",");
                //模板code
                final String tempCode = sysConfigService.selectConfigByKey(SMS_SEAT);
                ThreadPoolUtil.execute(() -> {
                    BusReserveSmsVO smsVO = new BusReserveSmsVO(startDayStr,infoName,null);
                    smsUtil.sendSmsByTemplate(tempCode, toUser,smsVO);
                });
            }
        }

    }

    /**
     * 文本卡片消息
     * <AUTHOR> yang.
     * @since 2025/5/22 18:04
     */
    public void sendTextCard(final String linkUrl, String title, String content, String toUser) {
        final String corpId = sysConfigService.selectConfigByKey(CORP_ID_KEY);
        final String agentId = sysConfigService.selectConfigByKey(AGENT_ID_KEY);
        TextcardMsgBody message = new TextcardMsgBody();
        message.setTitle(title);
        message.setDescription(content);
        message.setUrl(linkUrl);
        message.setToUser(toUser);
        message.setAgentId(Integer.parseInt(agentId));
        try{
            Result<WxCpMessageSendResult> r = messageFeign.sendTextCard(corpId, agentId, message);
            if (!r.success()){
                String msg = StrUtil.format("班车预约-推送消息失败, title: {}, content: {},linkUrl:{}, toUser: {}, msg: {}",
                        title, content,linkUrl, toUser, r.getMsg());
                log.error(msg);
            }
            String msg = StrUtil.format("班车预约-推送消息成功, title: {}, content: {},linkUrl:{}, toUser: {}, msg: {}",
                    title, content,linkUrl, toUser, r.getMsg());
            log.error(msg);
        } catch (Exception e) {
            String msg = StrUtil.format("班车预约-推送消息异常, title: {}, content: {},linkUrl:{}, toUser: {}",
                    title, content,linkUrl, toUser);
            log.error(msg, e);
        }
    }

}

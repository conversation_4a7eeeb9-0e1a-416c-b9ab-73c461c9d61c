package com.lanshan.app.busreserve.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class BusReserveChangeDTO implements Serializable {
    private static final long serialVersionUID = 4732718467645784661L;

    @NotNull
    //记录id
    private Long recordId;

    //修改后的班次id
    @NotNull
    private Long infoId;

    //修改前的班次id
    @NotNull
    private Long oldInfoId;

    //发车日期  yyyy-MM-dd
    @NotNull
    private String startDay;

}

package com.lanshan.app.busreserve.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.*;
import com.lanshan.app.busreserve.po.BusReserveInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord;
import com.lanshan.app.busreserve.po.BusReserveUserRecord;
import com.lanshan.app.busreserve.vo.BusReserveInfoVO;
import com.lanshan.base.api.qo.CollResult;
import com.lanshan.base.api.utils.Result;

import java.util.List;

/**
 * <p>
 * 班车预约-班次信息 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
public interface BusReserveInfoService extends IService<BusReserveInfo> {

    /**
     * 获取班车配置列表
     * <AUTHOR> yang.
     * @since 2025/5/22 19:15
     */
    Page<BusReserveInfo> getBusReserveInfoPageList(BusReserveInfoPageDTO dto);

    /**
     * 新增班车配置
     * <AUTHOR> yang.
     * @since 2025/5/22 14:22
     */
    void insertBusReserveInfo(BusReserveInfo busReserveInfo);

    /**
     * 修改班车配置
     * <AUTHOR> yang.
     * @since 2025/5/22 14:22
     */
    void updateBusReserveInfo(BusReserveInfo busReserveInfo);

    /**
     * 批量 上线|下线
     * <AUTHOR> yang.
     * @since 2025/5/22 14:22
     */
    List<BusReserveInfo> changeBusReserveInfoShowStatus(BusReserveShowStatusDTO dto);

    /**
     * 批量删除班车配置
     * <AUTHOR> yang.
     * @since 2025/5/22 14:22
     */
    Result<Object>  removeBatchBusReserveInfo(List<Long> ids);

    /**
     * 获取指定地点和日期可预约的班车列表
     * <AUTHOR> yang.
     * @since 2025/5/22 19:15
     */
    List<BusReserveInfoVO> getBusReserveInfoByLocationAndDay(BusReserveInfoDTO dto);

    /**
     * 获取指定日期的班车列表
     * <AUTHOR> yang.
     * @since 2025/5/22 19:15
     */
    List<BusReserveInfoVO> getBusReserveInfoByDay(String startDay);


    /**
     * 根据班次id和日期获取班次详情
     * <AUTHOR> yang.
     * @since 2025/5/22 19:15
     */
    BusReserveInfoVO getBusReserveInfoByIdAndDay(BusReserveInfoDetailDTO dto);

    /**
     * 判断班车是否可预约
     * <AUTHOR> yang.
     * @since 2025/5/27 9:49
     */
    Result<Object> checkBusReserveInfo(BusReserveInfo info,String startDay);

    /**
     * 获取司机的班车列表
     * <AUTHOR> yang.
     * @since 2025/5/28 9:50
     */
    List<BusReserveInfoVO> getBusReserveInfoByDriver(BusReserveDriverDTO dto);

    /**
     * 修改核验码
     * <AUTHOR> yang.
     * @since 2025/5/28 10:57
     */
    void updateCheckCode(Long infoId, String code,String codeUri);

    /**
     * 生成核验码地址
     * <AUTHOR> yang.
     * @since 2025/5/29 14:03
     */
    String getBusReserveCheckCode(ReserveInfoRecordDTO dto);
}

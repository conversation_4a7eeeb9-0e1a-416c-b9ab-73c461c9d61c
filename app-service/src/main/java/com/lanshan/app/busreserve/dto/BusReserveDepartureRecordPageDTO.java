package com.lanshan.app.busreserve.dto;

import com.lanshan.base.api.qo.PageQo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class BusReserveDepartureRecordPageDTO extends PageQo {
    private static final long serialVersionUID = -6715767171148511393L;

    //车牌号
    private String licensePlate;

    //班次名称
    private String infoName;

    //司机姓名
    private String driverName;

    //发车时间 yyyy-MM-dd HH:mm:ss
    private String startTime;

    private String endTime;

}

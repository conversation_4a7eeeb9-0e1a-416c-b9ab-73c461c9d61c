package com.lanshan.app.busreserve.vo;

import com.lanshan.app.busreserve.po.BusReserveUserRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class BusReserveUserRecordVO extends BusReserveUserRecord {

    private static final long serialVersionUID = -5335991581245205667L;
    //始发站 武昌校区 | 嘉鱼校区
    private String startLocation;

    //终点站 武昌校区 | 嘉鱼校区
    private String endLocation;

}

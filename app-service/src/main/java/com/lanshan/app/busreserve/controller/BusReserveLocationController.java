package com.lanshan.app.busreserve.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lanshan.app.busreserve.po.BusReserveLocation;
import com.lanshan.app.busreserve.service.BusReserveLocationService;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.base.api.qo.CollResult;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 上车点
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/busReserve/location")
public class BusReserveLocationController {

    @Resource
    private BusReserveLocationService busReserveLocationService;

    /**
     * 获取上车点列表
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     * @param location 上车点
     */
    @PostMapping(value = "/getBusReserveLocationList" ,produces = "application/json;charset=UTF-8")
    public Result<CollResult<BusReserveLocation>> getBusReserveLocationList(@RequestParam(value = "location",required = false) String location){
        List<BusReserveLocation> records = busReserveLocationService.getBusReserveLocationList(location);
        return Result.build(new CollResult<>(records));
    }

    /**
     * 编辑上车点
     * <AUTHOR> yang.
     * @since 2025/5/21 10:42
     */
    @PostMapping(value = "/editBusReserveLocation" ,produces = "application/json;charset=UTF-8")
    public Result<Object> editBusReserveLocation(@RequestBody BusReserveLocation busReserveLocation){
        String userId = SecurityContextHolder.getUserIdStr();
        if (busReserveLocation.getId() == null){
            BusReserveLocation one = busReserveLocationService.getOne(new LambdaQueryWrapper<BusReserveLocation>().eq(BusReserveLocation::getLocation, busReserveLocation.getLocation()));
            if (one != null){
                return Result.build().error("上车点已存在，请勿重复添加!");
            }
        }
        busReserveLocationService.editBusReserveLocation(busReserveLocation,userId);
        return Result.build();
    }


    /**
     * 批量删除上车点
     * <AUTHOR> yang.
     * @since 2025/5/21 10:51
     */
    @PostMapping(value = "/removeBusReserveLocationByIds" ,produces = "application/json;charset=UTF-8")
    public Result<Object> removeBusReserveLocationByIds(@RequestBody Collection<String> ids){
        busReserveLocationService.removeBatchByIds(ids);
        return Result.build();
    }

}

package com.lanshan.app.busreserve.vo;

import com.lanshan.app.busreserve.po.BusReserveUserRecord;
import com.lanshan.base.api.vo.user.UserInfoPartVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class BusReserveUserRecordDetailVO implements Serializable {
    private static final long serialVersionUID = -8039728915108965735L;

    //自己的预约记录
    private BusReserveUserRecordVO record;

    //乘车人预约记录
    private List<BusReserveUserRecordVO> userRecords;
}

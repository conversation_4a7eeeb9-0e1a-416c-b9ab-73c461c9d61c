package com.lanshan.app.busreserve.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.lanshan.app.busreserve.dto.BusReserveDriverDTO;
import com.lanshan.app.busreserve.dto.BusReserveInfoCodeDTO;
import com.lanshan.app.busreserve.dto.BusReserveInfoDTO;
import com.lanshan.app.busreserve.dto.ReserveInfoRecordDTO;
import com.lanshan.app.busreserve.po.BusReserveInfo;
import com.lanshan.app.busreserve.service.BusReserveInfoService;
import com.lanshan.app.busreserve.service.BusReserveService;
import com.lanshan.app.busreserve.service.BusReserveSseService;
import com.lanshan.app.busreserve.vo.BusReserveInfoVO;
import com.lanshan.app.busreserve.vo.ReserveInfoRecordVO;
import org.springframework.http.MediaType;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.base.api.qo.CollResult;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 班车司机
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */


@Slf4j
@RestController
@RequestMapping("/busReserve/driver")
public class BusReserveDriverController {

    @Resource
    private BusReserveInfoService busReserveInfoService;
    @Resource
    private BusReserveService busReserveService;
    @Resource
    private BusReserveSseService sseService;

    /**
     * 获取司机的班车列表
     * <AUTHOR> yang.
     * @since 2025/5/22 19:15
     */
    @PostMapping(value = "/getBusReserveInfoByDriver" ,produces = "application/json;charset=UTF-8")
    public Result<CollResult<BusReserveInfoVO>> getBusReserveInfoByDriver(@RequestBody BusReserveDriverDTO dto){
        List<BusReserveInfoVO> list = busReserveInfoService.getBusReserveInfoByDriver(dto);
        return Result.build(new CollResult<>(list));
    }

    /**
     * 获取班车的预约记录
     * <AUTHOR> yang.
     * @since 2025/5/22 19:15
     */
    @PostMapping(value = "/getBusReserveInfoRecord" ,produces = "application/json;charset=UTF-8")
    public Result<Map<String ,Object>> getBusReserveInfoRecord(@RequestBody ReserveInfoRecordDTO dto){
        Map<String ,Object> map = busReserveService.getBusReserveInfoRecord(dto);
        return Result.build(map);
    }

    /**
     * 根据班次id生成核验码
     * <AUTHOR> yang.
     * @since 2025/5/28 10:54
     */
    @PostMapping(value = "/getBusReserveCheckCode" ,produces = "application/json;charset=UTF-8")
    public Result<Object> getBusReserveCheckCode(@RequestBody ReserveInfoRecordDTO dto){
        String checkUri = busReserveInfoService.getBusReserveCheckCode(dto);
        if (StrUtil.isEmpty(checkUri)){
            return Result.build().error("该班次已下线");
        }
        return Result.build(checkUri);
    }

    /**
     * 司机订阅SSE
     * <AUTHOR> yang.
     * @since 2025/5/28 15:04
     */
    @GetMapping(value = "/streamDriver", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamDriver(@RequestParam("code") String code,@RequestParam("userId") String userId) {
        String driverKey = sseService.getDriverKey(userId, code);
        return sseService.createDriverEmitter(driverKey);
    }

    /**
     * 司机 确认|拒绝
     * <AUTHOR> yang.
     * @since 2025/5/28 16:12
     */
    @PostMapping(value = "/driverConfirm" ,produces = "application/json;charset=UTF-8")
    public Result<Object> driverConfirm(@RequestBody BusReserveInfoCodeDTO dto){
        return busReserveService.driverConfirm(dto);
    }

    /**
     * 乘客订阅SSE
     * <AUTHOR> yang.
     * @since 2025/5/28 15:04
     */
    @GetMapping(value = "/streamUser", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamUser(@RequestParam("code") String code,@RequestParam("userId") String userId){
        String userKey = sseService.getUserKey(userId, code);
        return sseService.createPassengerEmitter(userKey);
    }


    /**
     * 根据核验码获取预约记录
     * <AUTHOR> yang.
     * @since 2025/5/27 9:33
     */
    @PostMapping(value = "/getBusReserveInfoByCode" ,produces = "application/json;charset=UTF-8")
    public Result<ReserveInfoRecordVO> getBusReserveInfoByCode(@RequestBody BusReserveInfoCodeDTO dto){
        ReserveInfoRecordVO re = busReserveService.getBusReserveInfoByCode(dto);
        return Result.build(re);
    }

    /**
     * 乘客扫码调用
     * <AUTHOR> yang.
     * @since 2025/5/28 15:04
     */
    @PostMapping(value = "/scanCheckCode" ,produces = "application/json;charset=UTF-8")
    public Result<Object> scanCheckCode(@RequestBody BusReserveInfoCodeDTO dto){
        return busReserveService.scanCheckCode(dto);
    }

}

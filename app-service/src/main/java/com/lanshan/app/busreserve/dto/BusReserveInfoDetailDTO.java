package com.lanshan.app.busreserve.dto;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Data
public class BusReserveInfoDetailDTO implements Serializable {
    private static final long serialVersionUID = -216846742780435607L;

    //班次id
    private Long id;

    //发车日期  yyyy-MM-dd
    private String startDay;

    //1 2 3 4 5 6 7   1代表周日  2代表周一
    private Integer week;

    //根据发车日期计算星期，计算现在距离当天0点的秒数
    public void setStartDay(String startDay) {
        this.startDay = startDay;
        if (StrUtil.isNotEmpty(startDay)){
            DateTime time = DateUtil.parseDate(startDay);
            this.week = DateUtil.dayOfWeekEnum(time).getValue();
        }
    }
}

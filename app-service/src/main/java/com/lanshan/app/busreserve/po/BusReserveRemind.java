package com.lanshan.app.busreserve.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

/**
 * 班车预约-预约提醒
 * <AUTHOR> yang.
 * @since 2025-05-27
 */

@Data
@TableName(value = "bus_reserve_remind", autoResultMap = true, schema = "standard_app")
public class BusReserveRemind implements Serializable {

	private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    //用户id  | 手机号
    private String userId;

    //1企微用户 2外部用户
    private Integer userType;

    //班次id
    private Long infoId;

    //创建时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date createTime;

    private String userName;

    //发车日期
	@JsonFormat(pattern = "yyyy-MM-dd",locale="zh", timezone="GMT+8")
    private Date startDay;



}

package com.lanshan.app.busreserve.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lanshan.base.api.qo.PageQo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class BusReserveOperationLogPageDTO  extends PageQo {
    private static final long serialVersionUID = 4802514483401139722L;


    //操作类型（0其它 1新增 2修改 3删除）
    private Integer businessType;

    //操作人姓名
    private String userName;

    //操作时间 yyyy-MM-dd HH:mm:ss
    private String startTime;

    private String endTime;

    //班次名称
    private String infoName;

}

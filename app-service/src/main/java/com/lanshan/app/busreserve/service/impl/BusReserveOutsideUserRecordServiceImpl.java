package com.lanshan.app.busreserve.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.*;
import com.lanshan.app.busreserve.enums.BusReserveStatus;
import com.lanshan.app.busreserve.po.BusReserveAudit;
import com.lanshan.app.busreserve.po.BusReserveInfo;
import com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord;
import com.lanshan.app.busreserve.dao.BusReserveOutsideUserRecordMapper;
import com.lanshan.app.busreserve.service.BusReserveAppointmentService;
import com.lanshan.app.busreserve.service.BusReserveAuditService;
import com.lanshan.app.busreserve.service.BusReserveInfoService;
import com.lanshan.app.busreserve.service.BusReserveOutsideUserRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.busreserve.util.BusReserveUtil;
import com.lanshan.app.busreserve.vo.BusReserveInfoVO;
import com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordExcelVO;
import com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordVO;
import com.lanshan.base.api.qo.CollResult;
import com.lanshan.base.api.utils.Result;
import com.lanshan.base.api.vo.user.UserInfoPartVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 班车预约-外部用户预约记录 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
@Service
public class BusReserveOutsideUserRecordServiceImpl extends ServiceImpl<BusReserveOutsideUserRecordMapper, BusReserveOutsideUserRecord> implements BusReserveOutsideUserRecordService {

    @Resource
    private BusReserveOutsideUserRecordMapper busReserveOutsideUserRecordMapper;
    @Resource
    private BusReserveInfoService busReserveInfoService;
    @Resource
    private BusReserveAuditService busReserveAuditService;

    @Override
    public List<BusReserveOutsideUserRecord> getOutsideUserRecordByDay(String startDay) {
        LambdaQueryWrapper<BusReserveOutsideUserRecord> wrapper = new  LambdaQueryWrapper<>();
        wrapper.ne(BusReserveOutsideUserRecord::getReserveStatus,BusReserveStatus.CANCEL.value);
        wrapper.eq(BusReserveOutsideUserRecord::getStartDay,startDay);
        wrapper.orderByDesc(BusReserveOutsideUserRecord::getCreateTime,BusReserveOutsideUserRecord::getId);
        return this.list(wrapper);
    }

    @Override
    public Page<BusReserveOutsideUserRecord> getOutsideUserRecordPageList(BusReserveOutSideUserRecordPageDTO dto) {
        Page<BusReserveOutsideUserRecord> pg = new Page<>(dto.getPage(), dto.getSize());
        LambdaQueryWrapper<BusReserveOutsideUserRecord> wrapper = getQueryWrapper(dto);
        pg = this.page(pg, wrapper);
        return pg;
    }

    @Override
    public List<BusReserveOutsideUserRecordExcelVO> exportOutsideUserRecord(BusReserveOutSideUserRecordPageDTO dto) {
        LambdaQueryWrapper<BusReserveOutsideUserRecord> wrapper = getQueryWrapper(dto);
        List<BusReserveOutsideUserRecord> list = this.list(wrapper);
        List<BusReserveOutsideUserRecordExcelVO> re = BeanUtil.copyToList(list, BusReserveOutsideUserRecordExcelVO.class);
        return re;
    }

    private LambdaQueryWrapper<BusReserveOutsideUserRecord> getQueryWrapper(BusReserveOutSideUserRecordPageDTO dto) {
        LambdaQueryWrapper<BusReserveOutsideUserRecord> wrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotEmpty(dto.getUserName())){
            wrapper.like(BusReserveOutsideUserRecord::getUserName, dto.getUserName());
        }
        if (StrUtil.isNotEmpty(dto.getDriverName())){
            wrapper.like(BusReserveOutsideUserRecord::getDriverName, dto.getDriverName());
        }
        if (StrUtil.isNotEmpty(dto.getInfoName())){
            wrapper.like(BusReserveOutsideUserRecord::getInfoName, dto.getInfoName());
        }
        if (StrUtil.isNotEmpty(dto.getStartTime())){
            wrapper.ge(BusReserveOutsideUserRecord::getStartTime, dto.getStartTime());
        }
        if (StrUtil.isNotEmpty(dto.getEndTime())){
            wrapper.le(BusReserveOutsideUserRecord::getStartTime, dto.getEndTime());
        }

        if (dto.getReserveStatus() != null) {
            wrapper.eq(BusReserveOutsideUserRecord::getReserveStatus, dto.getReserveStatus());
        }else {
            //后管看的是乘车记录
            List<Integer> reserveStatusList = Arrays.asList(
                    BusReserveStatus.NORMAL.value, BusReserveStatus.RESERVE_NOT_SIT.value,BusReserveStatus.SIT_NOT_RESERVE.value
            );
            wrapper.in(BusReserveOutsideUserRecord::getReserveStatus,reserveStatusList);
        }
        if (StrUtil.isNotEmpty(dto.getPhone())){
            wrapper.like(BusReserveOutsideUserRecord::getPhone, dto.getPhone());
        }

        wrapper.orderByDesc(BusReserveOutsideUserRecord::getCreateTime,BusReserveOutsideUserRecord::getId);
        return wrapper;
    }

    @Override
    public Page<BusReserveOutsideUserRecord> getOutsideUserRecord(BusReserveOutSideUserRecordPageDTO dto) {
        Page<BusReserveOutsideUserRecord> pg = new Page<>(dto.getPage(), dto.getSize());
        LambdaQueryWrapper<BusReserveOutsideUserRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(BusReserveOutsideUserRecord::getPhone, dto.getPhone());
        //发车时间排序
        wrapper.orderByDesc(BusReserveOutsideUserRecord::getStartTime,BusReserveOutsideUserRecord::getId);
        pg = this.page(pg, wrapper);
        return pg;
    }

    @Override
    public BusReserveOutsideUserRecordVO getMineBusReserveOutsideUserRecordDetail(String id) {
        BusReserveOutsideUserRecordVO vo= busReserveOutsideUserRecordMapper.getBusReserveOutsideUserRecordById(id);
        return vo;
    }

    @Override
    public boolean updateBusReserveOutsideUserRecord(Collection<Long> ids, Integer reserveStatus) {
        if(CollUtil.isEmpty(ids) || reserveStatus == null){
            return false;
        }
        return busReserveOutsideUserRecordMapper.updateBusReserveOutsideUserRecord(ids, reserveStatus) > 0;
    }

    @Override
    public List<BusReserveOutsideUserRecord> getOutSideUserRecordByInfoIdsAndReserveStatus(Collection<Long> ids,String startDay, Integer reserveStatus) {
        LambdaQueryWrapper<BusReserveOutsideUserRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BusReserveOutsideUserRecord::getInfoId, ids);
        if (StrUtil.isNotEmpty(startDay)){
            wrapper.eq(BusReserveOutsideUserRecord::getStartDay, startDay);
        }
        if (reserveStatus != null){
            wrapper.eq(BusReserveOutsideUserRecord::getReserveStatus, reserveStatus);
        }
        return this.list(wrapper);
    }

    @Override
    public List<BusReserveOutsideUserRecord> getOutsideUserRecordByInfoAndDay(Long infoId, String startDay) {
        LambdaQueryWrapper<BusReserveOutsideUserRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper
                .eq(BusReserveOutsideUserRecord::getInfoId, infoId)
                .eq(BusReserveOutsideUserRecord::getStartDay, startDay)
                .ne(BusReserveOutsideUserRecord::getReserveStatus,BusReserveStatus.CANCEL.value);
        return this.list(wrapper);
    }

    @Override
    public Boolean checkBusReserveOutsideUserAuth(BusReserveUserAuthDTO dto,BusReserveInfoVO info) {
        Long infoId = dto.getInfoId();
        String startDay = dto.getStartDay();
        List<String> userIds = dto.getUserIds();
        String startLocation = info.getStartLocation();
        //日期、班次、未取消的记录
        List<BusReserveOutsideUserRecordVO> records = busReserveOutsideUserRecordMapper.getOutsideUserRecordByInfoAndDayAndUser(null, startDay, userIds,BusReserveStatus.CANCEL.value);
        boolean auth = true;
        //有起点相同的记录，终止循环
        for (BusReserveOutsideUserRecordVO record : records) {
            String startLocationRe = record.getStartLocation();
            if (startLocation.equals(startLocationRe) ){
                auth = false;
                break;
            }
        }

        return auth;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void busReserve(BusReserveDTO dto, BusReserveInfoVO info) {
        List<UserInfoPartVO> users = dto.getUsers();
        if (CollUtil.isEmpty(users)) {
            return;
        }
        UserInfoPartVO user = users.get(0);
        BusReserveOutsideUserRecord record = new BusReserveOutsideUserRecord();
        record.setUserName(user.getName());
        record.setUserGender(user.getGender());
        record.setDeptName(user.getDeptName());
        record.setPosition(dto.getPosition());
        String auditUserId = dto.getAuditUserId();
        if (StrUtil.isNotEmpty(auditUserId)){
            BusReserveAudit audit = busReserveAuditService.getOne(new LambdaQueryWrapper<BusReserveAudit>().eq(BusReserveAudit::getUserId, auditUserId));
            if (audit != null){
                record.setAuditUserId(audit.getUserId());
                record.setAuditName(audit.getUserName());
                record.setAuditPhone(audit.getPhone());
            }
        }
        record.setReason(dto.getReason());
        record.setInfoId(info.getId());
        record.setInfoName(info.getName());
        record.setDriverUserId(info.getDriverUserIdToday());
        record.setDriverName(info.getDriverNameToday());
        record.setDriverPhone(info.getDriverPhoneToday());
        record.setReserveStatus(BusReserveStatus.WAIT.value);
        //计算发车时间
        Date startTime = BusReserveUtil.calculateStartTime(info, dto.getStartDay());
        record.setStartTime(startTime);
        record.setCreateTime(new Date());
        record.setHalfwayLocation(dto.getHalfwayLocation());
        record.setStartDay(DateUtil.parseDate(dto.getStartDay()));
        record.setPhone(user.getUserId());
        this.save(record);
    }

    @Override
    public int getOutSideUserCountByInfoIdsAndReserveStatus(Long infoId, String startDay, Integer reserveStatus) {
        return busReserveOutsideUserRecordMapper.getOutSideUserCountByInfoIdsAndReserveStatus(infoId, startDay, reserveStatus);
    }

    @Override
    public void insertSitNotReserveOutsideUserRecord(BusReserveInfoCodeDTO dto) {
        Date now = new Date();
        BusReserveInfo info = busReserveInfoService.getById(dto.getInfoId());
        BusReserveOutsideUserRecord record = new BusReserveOutsideUserRecord();
        record.setUserName(dto.getName());
        record.setUserGender(dto.getGender());
        record.setDeptId(dto.getDeptId());
        record.setDeptName(dto.getDeptName());
        record.setPosition(dto.getPosition());
        record.setAuditUserId(dto.getAuditUserId());
        record.setAuditName(dto.getAuditName());
        record.setReason(dto.getReason());
        record.setInfoId(info.getId());
        record.setInfoName(info.getName());
        record.setDriverUserId(info.getDriverUserIdToday());
        record.setDriverName(info.getDriverNameToday());
        record.setDriverPhone(info.getDriverPhoneToday());
        record.setReserveStatus(BusReserveStatus.SIT_NOT_RESERVE.value);
        //计算发车时间
        Date startTime = BusReserveUtil.calculateStartTime(info, dto.getStartDay());
        record.setStartTime(startTime);
        record.setCreateTime(now);
        record.setCheckTime(now);

        record.setStartDay(DateUtil.parseDate(dto.getStartDay()));
        record.setPhone(dto.getUserId());
        this.save(record);
    }

    @Override
    public BusReserveOutsideUserRecord getBusReserveRecordByLocation(Long infoId, String startLocation, String endLocation, String userId, String startDay) {
        return busReserveOutsideUserRecordMapper.getBusReserveRecordByLocation(infoId, startLocation, endLocation, userId, startDay,BusReserveStatus.WAIT.value);
    }

    @Override
    public List<BusReserveOutsideUserRecordVO> getWaitRecordByDate(String startTime, String endTime, Integer reserveStatus) {
        return busReserveOutsideUserRecordMapper.getWaitRecordByDate(startTime, endTime, reserveStatus);
    }

    @Override
    public List<BusReserveOutsideUserRecordVO> getTomorrowWaitRecord(String tomorrow, Integer reserveStatus) {
        return  busReserveOutsideUserRecordMapper.getTomorrowWaitRecord(tomorrow, reserveStatus);
    }


}

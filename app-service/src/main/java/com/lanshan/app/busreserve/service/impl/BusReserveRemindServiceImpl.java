package com.lanshan.app.busreserve.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.lanshan.app.busreserve.dto.BusReserveRemindSaveDTO;
import com.lanshan.app.busreserve.po.BusReserveRemind;
import com.lanshan.app.busreserve.dao.BusReserveRemindMapper;
import com.lanshan.app.busreserve.service.BusReserveRemindService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 班车预约-预约提醒 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-27
 */
@Service
public class BusReserveRemindServiceImpl extends ServiceImpl<BusReserveRemindMapper, BusReserveRemind> implements BusReserveRemindService {


    @Override
    public List<BusReserveRemind> getRemindByInfoIdAndDay(Long infoId, String startDay) {
        LambdaQueryWrapper<BusReserveRemind> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusReserveRemind::getInfoId, infoId).eq(BusReserveRemind::getStartDay, startDay);
        List<BusReserveRemind> reminds = this.list(wrapper);
        return reminds;
    }
}

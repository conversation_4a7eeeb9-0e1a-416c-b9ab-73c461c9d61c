package com.lanshan.app.busreserve.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveOutSideUserRecordPageDTO;
import com.lanshan.app.busreserve.dto.BusReserveUserAuthDTO;
import com.lanshan.app.busreserve.enums.BusReserveStatus;
import com.lanshan.app.busreserve.po.BusReserveOutsideUserRecord;
import com.lanshan.app.busreserve.service.BusReserveOutsideUserRecordService;
import com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordExcelVO;
import com.lanshan.app.busreserve.vo.BusReserveOutsideUserRecordVO;
import com.lanshan.app.common.utils.ExcelUtils;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.base.api.qo.CollResult;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;

/**
 * 外部用户预约记录
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/busReserve/outsideUserRecord")
public class BusReserveOutsideUserRecordController {

    @Resource
    private BusReserveOutsideUserRecordService busReserveOutsideUserRecordService;


    /**
     * 外部用户乘坐记录分页列表
     * <AUTHOR> yang.
     * @since 2025/5/21 17:37
     */
    @PostMapping(value = "/getOutsideUserRecordPageList",produces = "application/json;charset=UTF-8")
    public Result<Page<BusReserveOutsideUserRecord>> getOutsideUserRecordPageList(@RequestBody BusReserveOutSideUserRecordPageDTO dto) {
        Page<BusReserveOutsideUserRecord> pg = busReserveOutsideUserRecordService.getOutsideUserRecordPageList(dto);
        return Result.build(pg);
    }

    /**
     * 导出外部用户乘坐记录
     * <AUTHOR> yang.
     * @since 2025/5/21 17:37
     */
    @PostMapping(value = "/exportOutsideUserRecord",produces = "application/json;charset=UTF-8")
    public void exportOutsideUserRecord(@RequestBody BusReserveOutSideUserRecordPageDTO dto, HttpServletResponse response) {
        List<BusReserveOutsideUserRecordExcelVO> records = busReserveOutsideUserRecordService.exportOutsideUserRecord(dto);
        ExcelUtils.exportExcelByRecords("外部用户乘坐记录", records, BusReserveOutsideUserRecordExcelVO.class, response);
    }

    /**
     * 外部用户预约记录
     * <AUTHOR> yang.
     * @since 2025/5/21 17:37
     */
    @PostMapping(value = "/getOutsideUserRecord",produces = "application/json;charset=UTF-8")
    public Result<Page<BusReserveOutsideUserRecord>> getOutsideUserRecord(@RequestBody BusReserveOutSideUserRecordPageDTO dto){
        Page<BusReserveOutsideUserRecord> pg = busReserveOutsideUserRecordService.getOutsideUserRecord(dto);
        return Result.build(pg);
    }

    /**
     * 外部用户预约详情
     * <AUTHOR> yang.
     * @since 2025/5/21 17:51
     * @param id 预约记录id
     */
    @PostMapping(value = "/getMineBusReserveOutsideUserRecordDetail",produces = "application/json;charset=UTF-8")
    public Result<BusReserveOutsideUserRecordVO> getMineBusReserveOutsideUserRecordDetail(@RequestParam("id") String id){
        BusReserveOutsideUserRecordVO vo = busReserveOutsideUserRecordService.getMineBusReserveOutsideUserRecordDetail(id);
        return Result.build(vo);
    }


    /**
     * 外部用户取消预约
     * <AUTHOR> yang.
     * @since 2025/5/22 10:04
     */
    @PostMapping(value = "/cancelBusReserveOutsideUserRecord",produces = "application/json;charset=UTF-8")
    public Result<Object> cancelBusReserveOutsideUserRecord(@RequestParam("id") Long id){
        busReserveOutsideUserRecordService.updateBusReserveOutsideUserRecord(Arrays.asList(id), BusReserveStatus.CANCEL.value);
        return Result.build();
    }

}

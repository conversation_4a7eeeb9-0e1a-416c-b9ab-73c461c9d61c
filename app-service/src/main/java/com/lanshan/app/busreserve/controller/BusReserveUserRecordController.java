package com.lanshan.app.busreserve.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveUserRecordPageDTO;
import com.lanshan.app.busreserve.enums.BusReserveStatus;
import com.lanshan.app.busreserve.po.BusReserveUserRecord;
import com.lanshan.app.busreserve.service.BusReserveMessageService;
import com.lanshan.app.busreserve.service.BusReserveUserRecordService;
import com.lanshan.app.busreserve.vo.BusReserveUserRecordDetailVO;
import com.lanshan.app.busreserve.vo.BusReserveUserRecordExcelVO;
import com.lanshan.app.busreserve.vo.BusReserveUserVO;
import com.lanshan.app.common.utils.ExcelUtils;
import com.lanshan.app.common.utils.SecurityContextHolder;
import com.lanshan.base.api.qo.CollResult;
import com.lanshan.base.api.utils.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 企微用户预约记录
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@RestController
@RequestMapping("/busReserve/userRecord")
public class BusReserveUserRecordController {

    @Resource
    private BusReserveUserRecordService busReserveUserRecordService;
    @Resource
    private BusReserveMessageService busReserveMessageService;


    /**
     * 登录用户信息
     * <AUTHOR> yang.
     * @since 2025/5/23 16:54
     */
    @PostMapping(value = "/getBusReserveUserInfo",produces = "application/json;charset=UTF-8")
    public Result<BusReserveUserVO> getBusReserveUserInfo(){
        String userId = SecurityContextHolder.getUserIdStr();
        BusReserveUserVO user = busReserveUserRecordService.getBusReserveUserInfo(userId);
        return Result.build(user);
    }

    /**
     * 企微用户乘坐记录分页列表
     * <AUTHOR> yang.
     * @since 2025/5/21 17:37
     */
    @PostMapping(value = "/getBusReserveUserRecordPageList",produces = "application/json;charset=UTF-8")
    public Result<Page<BusReserveUserRecord>> getBusReserveUserRecordPageList(@RequestBody BusReserveUserRecordPageDTO dto){
        Page<BusReserveUserRecord> pg = busReserveUserRecordService.getBusReserveUserRecordPageList(dto);
        return Result.build(pg);
    }

    /**
     * 导出企微用户乘坐记录
     * <AUTHOR> yang.
     * @since 2025/5/29 9:37
     */
    @PostMapping(value = "/exportBusReserveUserRecord",produces = "application/json;charset=UTF-8")
    public void exportBusReserveUserRecord(@RequestBody BusReserveUserRecordPageDTO dto,HttpServletResponse response){
        List<BusReserveUserRecordExcelVO> list = busReserveUserRecordService.exportBusReserveUserRecord(dto);
        ExcelUtils.exportExcelByRecords("企微用户乘坐记录", list, BusReserveUserRecordExcelVO.class, response);
    }

    /**
     * 企微用户预约记录
     * <AUTHOR> yang.
     * @since 2025/5/21 17:51
     */
    @PostMapping(value = "/getMineBusReserveUserRecord",produces = "application/json;charset=UTF-8")
    public Result<Page<BusReserveUserRecord>> getMineBusReserveUserRecord(@RequestBody BusReserveUserRecordPageDTO dto){
        String userId = SecurityContextHolder.getUserIdStr();
        dto.setUserId(userId);
        Page<BusReserveUserRecord> pg = busReserveUserRecordService.getMineBusReserveUserRecord(dto);
        return Result.build(pg);
    }

    /**
     * 企微用户预约详情
     * <AUTHOR> yang.
     * @since 2025/5/21 17:51
     * @param id 预约记录id
     */
    @PostMapping(value = "/getMineBusReserveUserRecordDetail",produces = "application/json;charset=UTF-8")
    public Result<BusReserveUserRecordDetailVO> getMineBusReserveUserRecordDetail(@RequestParam Long id){
        BusReserveUserRecordDetailVO detail = busReserveUserRecordService.getMineBusReserveUserRecordDetail(id);
        return Result.build(detail);
    }

    /**
     * 企微用户批量取消预约
     * <AUTHOR> yang.
     * @since 2025/5/22 10:04
     */
    @PostMapping(value = "/cancelBatchBusReserveUserRecord",produces = "application/json;charset=UTF-8")
    public Result<Object> cancelBatchBusReserveUserRecord(@RequestBody @NotEmpty List<Long> ids){
        busReserveUserRecordService.updateBusReserveUserRecord(ids, BusReserveStatus.CANCEL.value);
        List<BusReserveUserRecord> records = busReserveUserRecordService.listByIds(ids);
        BusReserveUserRecord record = records.get(0);
        //取消后，发余座提醒
        Long infoId = record.getInfoId();
        Date startDay = record.getStartDay();
        busReserveMessageService.sendConcernMsg(infoId, startDay);
        return Result.build();
    }

}

package com.lanshan.app.busreserve.service;

import com.lanshan.app.busreserve.dto.BusReserveRemindSaveDTO;
import com.lanshan.app.busreserve.po.BusReserveRemind;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 班车预约-预约提醒 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-27
 */
public interface BusReserveRemindService extends IService<BusReserveRemind> {


    /**
     * 根据班次和日期查询订阅了提醒的
     * <AUTHOR> yang.
     * @since 2025/5/29 15:43
     */
    List<BusReserveRemind> getRemindByInfoIdAndDay(Long infoId, String startDay);

}

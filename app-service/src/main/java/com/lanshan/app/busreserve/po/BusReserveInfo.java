package com.lanshan.app.busreserve.po;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 班车预约-班次信息
 * <AUTHOR> yang.
 * @since 2025-05-20
 */

@Data
@TableName(value = "bus_reserve_info", autoResultMap = true, schema = "standard_app")
public class BusReserveInfo implements Serializable {

	private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    //班次名称
    private String name;

    //始发站 武昌校区 | 嘉鱼校区
    private String startLocation;

    //终点站 武昌校区 | 嘉鱼校区
    private String endLocation;

    //中途上车点
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> halfwayLocation;

    //车牌号
    private String licensePlate;

    //核载人数
    private Integer userCount;

    //是否重复 0否1是
    private Integer repeatStatus;

    //发车日期  不重复时生效
	@JsonFormat(pattern = "yyyy-MM-dd",locale="zh", timezone="GMT+8")
    private Date startDay;

    //1 2 3 4 5 6 7   1代表周日  2代表周一
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> weekList;

    //发车时间  HH:mm
    private String startTime;

    //司机 id
    private String driverUserId;

    //司机姓名
    private String driverName;

    //司机手机号
    private String driverPhone;

    //发车前几小时截止预约
    private Integer deadlineHour;

    //今日车牌
    private String licensePlateToday;

    //今日司机id
    private String driverUserIdToday;

    //0下线 1上线
    private Integer showStatus;

    //创建时间
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    private Date createTime;

    //今日司机姓名
    private String driverNameToday;

    //今日司机手机号
    private String driverPhoneToday;

    //核验码url
    private String checkCodeUrl;

    //核验码
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String checkCode;

    //今日发车时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",locale="zh", timezone="GMT+8")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date startTimeToday;

    //发车时间 距离当天0点的秒数
    private Integer startTimeSeconds;

    public String getUpdateInfo() {
        return StrUtil.format("车牌号: {},今日车牌号: {}; 司机: {},今日司机: {}",licensePlate,licensePlateToday,driverName,driverNameToday);
    }
}

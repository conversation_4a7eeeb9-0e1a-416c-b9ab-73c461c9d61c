package com.lanshan.app.busreserve.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveAuditPageDTO;
import com.lanshan.app.busreserve.po.BusReserveDispatcher;
import com.lanshan.app.busreserve.dao.BusReserveDispatcherMapper;
import com.lanshan.app.busreserve.service.BusReserveDispatcherService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.busreserve.vo.BusReserveInfoVO;
import com.lanshan.app.checkin.util.CheckinUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 班车预约-调度员 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
@Service
public class BusReserveDispatcherServiceImpl extends ServiceImpl<BusReserveDispatcherMapper, BusReserveDispatcher> implements BusReserveDispatcherService {

    @Override
    public Page<BusReserveDispatcher> getBusReserveDispatcherPageList(BusReserveAuditPageDTO dto) {
        Page<BusReserveDispatcher> pg = new Page<>(dto.getPage(), dto.getSize());
        LambdaQueryWrapper<BusReserveDispatcher> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(BusReserveDispatcher::getCreateTime,BusReserveDispatcher::getId);
        if (StrUtil.isNotEmpty(dto.getUserId())){
            wrapper.like(BusReserveDispatcher::getUserId,dto.getUserId());
        }
        if (StrUtil.isNotEmpty(dto.getName())){
            wrapper.like(BusReserveDispatcher::getUserName,dto.getName());
        }
        pg = this.page(pg, wrapper);
        return pg;
    }


}

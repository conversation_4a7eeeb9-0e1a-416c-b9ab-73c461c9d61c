package com.lanshan.app.busreserve.enums;

import com.lanshan.app.checkin.vo.CheckinEnumTypeVO;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

public enum BusReserveStatus {

    WAIT(1,"待乘车"),
    NORMAL(2,"正常乘车"),
    RESERVE_NOT_SIT(3,"约而不坐"),
    SIT_NOT_RESERVE(4,"坐而不约"),
    CANCEL(5,"已取消");


    public final Integer value;
    public final String label;

    BusReserveStatus(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String getName(Integer value) {
        for (BusReserveStatus cardStatus : BusReserveStatus.values()) {
            if (cardStatus.value.equals(value)) {
                return cardStatus.label;
            }
        }
        return "";
    }

    public CheckinEnumTypeVO toVO() {
        return new CheckinEnumTypeVO(this.value, this.label);
    }

    public static List<CheckinEnumTypeVO> getAllBusReserveStatus() {
        return Arrays.stream(BusReserveStatus.values())
                .map(BusReserveStatus::toVO)
                .collect(Collectors.toList());
    }

}

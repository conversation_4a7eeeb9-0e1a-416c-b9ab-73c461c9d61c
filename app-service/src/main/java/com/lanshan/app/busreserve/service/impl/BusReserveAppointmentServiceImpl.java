package com.lanshan.app.busreserve.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveAuditPageDTO;
import com.lanshan.app.busreserve.po.BusReserveAppointment;
import com.lanshan.app.busreserve.dao.BusReserveAppointmentMapper;
import com.lanshan.app.busreserve.service.BusReserveAppointmentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 班车预约-代预约人 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
@Service
public class BusReserveAppointmentServiceImpl extends ServiceImpl<BusReserveAppointmentMapper, BusReserveAppointment> implements BusReserveAppointmentService {

    @Override
    public Page<BusReserveAppointment> getBusReserveAppointmentPageList(BusReserveAuditPageDTO dto) {
        Page<BusReserveAppointment> pg = new Page<>(dto.getPage(), dto.getSize());
        LambdaQueryWrapper<BusReserveAppointment> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(BusReserveAppointment::getCreateTime, BusReserveAppointment::getId);

        if (StrUtil.isNotEmpty(dto.getUserId())){
            wrapper.like(BusReserveAppointment::getUserId, dto.getUserId());
        }
        if (StrUtil.isNotEmpty(dto.getName())){
            wrapper.like(BusReserveAppointment::getUserName, dto.getName());
        }

        pg = this.page(pg, wrapper);
        return pg;
    }

}

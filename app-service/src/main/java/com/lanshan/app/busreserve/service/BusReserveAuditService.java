package com.lanshan.app.busreserve.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveAuditPageDTO;
import com.lanshan.app.busreserve.po.BusReserveAudit;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 班车预约-审核人 服务类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
public interface BusReserveAuditService extends IService<BusReserveAudit> {

    /**
     * 获取审核人
     * <AUTHOR> yang.
     * @since 2025/5/21 10:41
     */
    List<BusReserveAudit> getBusReserveAuditList(BusReserveAuditPageDTO dto);

}

package com.lanshan.app.busreserve.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lanshan.app.busreserve.dto.BusReserveDepartureRecordPageDTO;
import com.lanshan.app.busreserve.po.BusReserveDepartureRecord;
import com.lanshan.app.busreserve.dao.BusReserveDepartureRecordMapper;
import com.lanshan.app.busreserve.service.BusReserveDepartureRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lanshan.app.busreserve.vo.BusReserveDepartureRecordExcelVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 班车预约-班车记录 服务实现类
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
@Service
public class BusReserveDepartureRecordServiceImpl extends ServiceImpl<BusReserveDepartureRecordMapper, BusReserveDepartureRecord> implements BusReserveDepartureRecordService {

    @Resource
    private BusReserveDepartureRecordMapper busReserveDepartureRecordMapper;

    @Override
    public Page<BusReserveDepartureRecord> getBusReserveDepartureRecordPageList(BusReserveDepartureRecordPageDTO dto) {
        Page<BusReserveDepartureRecord> pg = new Page<>(dto.getPage(), dto.getSize());
        LambdaQueryWrapper<BusReserveDepartureRecord> wrapper = getQueryWrapper(dto);
        pg = this.page(pg,wrapper);
        return pg;
    }

    @Override
    public List<BusReserveDepartureRecordExcelVO> exportBusReserveDepartureRecord(BusReserveDepartureRecordPageDTO dto) {
        LambdaQueryWrapper<BusReserveDepartureRecord> wrapper = getQueryWrapper(dto);
        List<BusReserveDepartureRecord> list = this.list(wrapper);
        List<BusReserveDepartureRecordExcelVO> re = BeanUtil.copyToList(list, BusReserveDepartureRecordExcelVO.class);
        return re;
    }

    @Override
    public void insertBatchDepartureRecord(List<BusReserveDepartureRecord> departureRecords) {
        busReserveDepartureRecordMapper.insertBatch(departureRecords);
    }

    private LambdaQueryWrapper<BusReserveDepartureRecord> getQueryWrapper(BusReserveDepartureRecordPageDTO dto) {
        LambdaQueryWrapper<BusReserveDepartureRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(BusReserveDepartureRecord::getStartTime,BusReserveDepartureRecord::getId);
        if (StrUtil.isNotEmpty(dto.getLicensePlate())){
            wrapper.like(BusReserveDepartureRecord::getLicensePlate,dto.getLicensePlate());
        }
        if (StrUtil.isNotEmpty(dto.getInfoName())){
            wrapper.like(BusReserveDepartureRecord::getInfoName,dto.getInfoName());
        }
        if (StrUtil.isNotEmpty(dto.getDriverName())){
            wrapper.like(BusReserveDepartureRecord::getDriverName,dto.getDriverName());
        }
        if (StrUtil.isNotEmpty(dto.getStartTime())){
            wrapper.ge(BusReserveDepartureRecord::getStartTime,dto.getStartTime());
        }
        if (StrUtil.isNotEmpty(dto.getEndTime())){
            wrapper.le(BusReserveDepartureRecord::getStartTime,dto.getEndTime());
        }
        return wrapper;
    }
}

package com.lanshan.app.busreserve.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.*;

/**
 * TODO
 *
 * <AUTHOR> yang.
 * @version 1.0
 * @since 2022/12/20 10:49
 */

@Slf4j
@Service
public class BusReserveSseService {

    // key = 司机的id_核验码, value = 对应司机的 SseEmitter
    private final ConcurrentMap<String, SseEmitter> driverEmitters = new ConcurrentHashMap<>();
    // key = 乘客的id_核验码, value = 对应乘客的 SseEmitter
    private final ConcurrentMap<String, SseEmitter> passengerEmitters = new ConcurrentHashMap<>();


    public String getDriverKey(String driverId, String code) {
        return driverId + "_" + code;
    }

    public String getUserKey(String userId, String code) {
        return userId + "_" + code;
    }

    /**
     * 司机端订阅
     */
    public SseEmitter createDriverEmitter(String driverKey) {
        log.info("司机端订阅: driverKey = {}", driverKey);
        // 超时时间设为5分钟
        SseEmitter emitter = new SseEmitter(5 * 60 * 1000L);
        driverEmitters.put(driverKey, emitter);

        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        executor.scheduleAtFixedRate(() -> {
            try {
                emitter.send(SseEmitter.event().name("ping").data("keep-alive"));
            } catch (IOException e) {
                emitter.complete();
                executor.shutdown();
            }
        }, 0, 30, TimeUnit.SECONDS);

        emitter.onCompletion(() -> {
            driverEmitters.remove(driverKey);
            executor.shutdown();
        });
        emitter.onTimeout   (() -> {
            driverEmitters.remove(driverKey);
            executor.shutdown();
        });
        emitter.onError     ((e) -> {
            driverEmitters.remove(driverKey);
            executor.shutdown();
        });
        return emitter;
    }

    /**
     * 乘客端订阅
     */
    public SseEmitter createPassengerEmitter(String userKey) {
        log.info("乘客端订阅: userKey = {}", userKey);
        // 超时时间设为5分钟
        SseEmitter emitter = new SseEmitter(5 * 60 * 1000L);
        passengerEmitters.put(userKey, emitter);
        ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
        executor.scheduleAtFixedRate(() -> {
            try {
                emitter.send(SseEmitter.event().name("ping").data("keep-alive"));
            } catch (IOException e) {
                emitter.complete();
                executor.shutdown();
            }
        }, 0, 30, TimeUnit.SECONDS);

        emitter.onCompletion(() -> {
            driverEmitters.remove(userKey);
            executor.shutdown();
        });
        emitter.onTimeout   (() -> {
            driverEmitters.remove(userKey);
            executor.shutdown();
        });
        emitter.onError     ((e) -> {
            driverEmitters.remove(userKey);
            executor.shutdown();
        });
        return emitter;
    }

    /**
     * 通知司机：有乘客扫码，需要确认
     */
    public void notifyDriverScan(String driverKey, String data) {
        log.info("通知司机：有乘客扫码，需要确认: driverKey = {}, data = {}", driverKey, data);
        SseEmitter emitter = driverEmitters.get(driverKey);
        if (emitter != null) {
            try {
                emitter.send(SseEmitter.event()
                        .id(String.valueOf(System.currentTimeMillis()))
                        .name("scanRequest")
                        .data(data)
                );
                log.info("发送成功: driverKey = {}", driverKey);
            } catch (IOException e) {
                driverEmitters.remove(driverKey);
            }
        }
    }

    /**
     * 通知乘客：司机已确认或拒绝
     */
    public void notifyPassengerResult(String userKey,String data) {
        log.info("通知乘客：司机已确认或拒绝: userKey = {}, data = {}", userKey, data);
        SseEmitter emitter = passengerEmitters.get(userKey);
        if (emitter != null) {
            try {
                emitter.send(SseEmitter.event()
                        .id(String.valueOf(System.currentTimeMillis()))
                        .name("scanResult")
                        .data(data)
                );
                log.info("发送成功: userKey = {}", userKey);
            } catch (IOException e) {
                passengerEmitters.remove(userKey);
            }
        }
    }

}

package com.lanshan.app.busreserve.dao;

import com.lanshan.app.busreserve.po.BusReserveLocation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 班车预约-上车点 Mapper 接口
 * </p>
 *
 * <AUTHOR> yang.
 * @since 2025-05-20
 */
@Mapper
public interface BusReserveLocationMapper extends BaseMapper<BusReserveLocation> {

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<BusReserveLocation> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<BusReserveLocation> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entities List<BusReserveLocation> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<BusReserveLocation> entities);

}
